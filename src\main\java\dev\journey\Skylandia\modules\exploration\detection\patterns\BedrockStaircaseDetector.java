package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class BedrockStaircaseDetector extends PatternDetector {
    private static final int MIN_STAIRCASE_LENGTH = 3;
    private static final int MAX_Y_VARIANCE = 2;
    private static final Direction[] HORIZONTAL_DIRECTIONS = {
        Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST
    };

    public BedrockStaircaseDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (isBedrock(pos)) {
                        StaircaseInfo info = findStaircase(pos);
                        if (info != null && !isAlreadyDetected(results, info.center)) {
                            results.add(new DetectionResult(
                                "Potential bedrock staircase",
                                info.center,
                                new Vec3d(info.center.getX() + 0.5, info.center.getY() + 0.5, info.center.getZ() + 0.5),
                                DetectionResult.DetectionType.ANOMALY,
                                info.confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private StaircaseInfo findStaircase(BlockPos start) {
        // Look for staircase patterns in each horizontal direction
        for (Direction dir : HORIZONTAL_DIRECTIONS) {
            StaircaseInfo info = analyzeDirection(start, dir);
            if (info != null) {
                return info;
            }
        }
        return null;
    }

    private StaircaseInfo analyzeDirection(BlockPos start, Direction direction) {
        int length = 0;
        int heightChange = 0;
        BlockPos current = start;
        List<BlockPos> bedrockPositions = new ArrayList<>();
        bedrockPositions.add(start);

        // Follow potential staircase pattern
        while (length < 8) { // Maximum reasonable length to check
            BlockPos next = current.offset(direction);
            BlockPos up = next.up();
            BlockPos down = next.down();

            // Check for bedrock in a vertical range
            BlockPos found = null;
            for (int y = -MAX_Y_VARIANCE; y <= MAX_Y_VARIANCE; y++) {
                BlockPos check = next.up(y);
                if (isBedrock(check)) {
                    found = check;
                    break;
                }
            }

            if (found == null) {
                break;
            }

            bedrockPositions.add(found);
            heightChange += found.getY() - current.getY();
            current = found;
            length++;
        }

        // Evaluate if this could be a staircase
        if (length >= MIN_STAIRCASE_LENGTH) {
            BlockPos center = bedrockPositions.get(bedrockPositions.size() / 2);
            float confidence = calculateConfidence(length, heightChange, bedrockPositions);
            return new StaircaseInfo(center, length, heightChange, confidence);
        }

        return null;
    }

    private float calculateConfidence(int length, int heightChange, List<BlockPos> positions) {
        float confidence = 0.5f; // Base confidence for meeting minimum length

        // Longer staircases are more likely to be player-made
        confidence += Math.min(0.1f * (length - MIN_STAIRCASE_LENGTH), 0.2f);

        // Regular height changes suggest intentional placement
        if (Math.abs(heightChange) > 0) {
            float heightPerStep = (float) Math.abs(heightChange) / length;
            if (heightPerStep <= 1.5f && heightPerStep >= 0.5f) {
                confidence += 0.2f;
            }
        }

        // Check for regular spacing
        if (hasRegularSpacing(positions)) {
            confidence += 0.1f;
        }

        return Math.min(confidence, 1.0f);
    }

    private boolean hasRegularSpacing(List<BlockPos> positions) {
        if (positions.size() < 3) return false;

        // Calculate average distance between steps
        double totalDistance = 0;
        for (int i = 1; i < positions.size(); i++) {
            totalDistance += positions.get(i).getSquaredDistance(positions.get(i - 1));
        }
        double averageDistance = totalDistance / (positions.size() - 1);

        // Check if distances are consistent
        for (int i = 1; i < positions.size(); i++) {
            double distance = positions.get(i).getSquaredDistance(positions.get(i - 1));
            if (Math.abs(distance - averageDistance) > 2.0) {
                return false;
            }
        }

        return true;
    }

    private boolean isBedrock(BlockPos pos) {
        return world.getBlockState(pos).getBlock() == Blocks.BEDROCK;
    }

    private boolean isAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (result.getPosition().getSquaredDistance(pos) < 25) { // Within 5 blocks
                return true;
            }
        }
        return false;
    }

    private static class StaircaseInfo {
        final BlockPos center;
        final int length;
        final int heightChange;
        final float confidence;

        StaircaseInfo(BlockPos center, int length, int heightChange, float confidence) {
            this.center = center;
            this.length = length;
            this.heightChange = heightChange;
            this.confidence = confidence;
        }
    }
}
