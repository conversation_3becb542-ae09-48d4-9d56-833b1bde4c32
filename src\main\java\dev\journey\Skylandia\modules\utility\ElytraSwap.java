package dev.journey.Skylandia.modules.utility;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.util.math.Direction;

public class ElytraSwap extends Module {
    private final SettingGroup sgWarning = settings.createGroup("Warning");
    private final SettingGroup sgInventory = settings.createGroup("Inventory");

    private final Setting<Integer> durabilityThreshold = sgWarning.add(new IntSetting.Builder()
            .name("durability-threshold")
            .description("Durability threshold to trigger warning.")
            .defaultValue(20)
            .min(1)
            .sliderRange(1, 432)
            .build()
    );

    private final Setting<Boolean> autoReplace = sgInventory.add(new BoolSetting.Builder()
            .name("auto-replace")
            .description("Automatically replaces broken elytra with a new one.")
            .defaultValue(false)
            .build()
    );

    private final Setting<Integer> replaceDurability = sgInventory.add(new IntSetting.Builder()
            .name("replace-durability")
            .description("The durability threshold your elytra will be replaced at.")
            .defaultValue(10)
            .range(1, 431)
            .sliderRange(1, 431)
            .visible(autoReplace::get)
            .build()
    );

    private boolean swapping = false;

    private final Setting<Integer> checkInterval = sgWarning.add(new IntSetting.Builder()
            .name("check-interval")
            .description("How often to check durability in ticks (20 ticks = 1 second).")
            .defaultValue(20)
            .min(1)
            .sliderRange(1, 200)
            .build()
    );

    private int tickCounter = 0;
    private int swapCooldown = 0;

    @Override
    public void onActivate() {
        tickCounter = 0;
        swapCooldown = 0;
    }

    private int getElytraDurability(ItemStack elytra) {
        if (elytra.getItem() != Items.ELYTRA) return 0;
        return elytra.getMaxDamage() - elytra.getDamage();
    }

    private void swapElytra(int inventorySlot) {
        if (swapCooldown > 0) return;
        
        int chestSlot = 38; // Chest armor slot
        ItemStack oldElytra = mc.player.getInventory().getStack(chestSlot);
        ItemStack newElytra = mc.player.getInventory().getStack(inventorySlot);
        
        // Perform the swap
        mc.player.getInventory().setStack(chestSlot, newElytra);
        mc.player.getInventory().setStack(inventorySlot, oldElytra);
        
        // Sync inventory
        mc.player.networkHandler.sendPacket(new PlayerActionC2SPacket(
            PlayerActionC2SPacket.Action.SWAP_ITEM_WITH_OFFHAND,
            mc.player.getBlockPos(),
            net.minecraft.util.math.Direction.UP
        ));
        
        swapCooldown = 10; // Half second cooldown
        info("Replaced elytra with new one from slot %d", inventorySlot);
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (swapCooldown > 0) swapCooldown--;
        tickCounter++;
        if (tickCounter >= checkInterval.get()) {
            tickCounter = 0;
            
            ItemStack chest = mc.player.getEquippedStack(EquipmentSlot.CHEST);
            if (chest.getItem() != Items.ELYTRA) return;

            int currentDurability = getElytraDurability(chest);
                
            // Check warning threshold
            if (currentDurability <= durabilityThreshold.get()) {
                warning("Elytra durability low! (%d durability remaining)", currentDurability);
            }

            // Handle auto-replacement if enabled and not currently swapping
            if (autoReplace.get() && !swapping && currentDurability <= replaceDurability.get()) {
                int bestSlot = findElytraInInventory();
                if (bestSlot != -1) {
                    ItemStack potentialElytra = mc.player.getInventory().getStack(bestSlot);
                    int newDurability = getElytraDurability(potentialElytra);
                        
                    // Only swap if new elytra has significantly better durability
                    if (newDurability > currentDurability + 10) {
                        swapping = true;
                        swapElytra(bestSlot);
                        swapping = false;
                    }
                } else {
                    warning("No replacement elytra found in inventory!");
                }
            }
        }
    }

    private int findElytraInInventory() {
        int bestSlot = -1;
        int bestDurability = 0;

        // Search main inventory
        for (int i = 0; i < mc.player.getInventory().main.size(); i++) {
            ItemStack stack = mc.player.getInventory().main.get(i);
            if (stack.getItem() == Items.ELYTRA) {
                int durability = getElytraDurability(stack);
                if (durability > bestDurability && durability > replaceDurability.get()) {
                    bestDurability = durability;
                    bestSlot = i;
                }
            }
        }
        return bestSlot;
    }

    public ElytraSwap() {
        super(Skylandia.Utility, "Skylandia", "Warns you when your elytra durability is getting low.");
    }
}
