plugins {
    id 'fabric-loom' version '1.7-SNAPSHOT'
}

// Log the Java version used by <PERSON><PERSON><PERSON> at task execution time
gradle.taskGraph.whenReady {
    println "Gradle is using Java version: " + org.gradle.internal.jvm.Jvm.current().javaVersion
}


sourceCompatibility = JavaVersion.VERSION_21
targetCompatibility = JavaVersion.VERSION_21

archivesBaseName = project.archives_base_name
version = project.mod_version
group = project.maven_group

repositories {
    mavenCentral()
    exclusiveContent {
        forRepository {
            maven {
                name = "Modrinth"
                url = "https://api.modrinth.com/maven"
            }
        }
        filter {
            includeGroup "maven.modrinth"
        }
    }
    maven {
        name = "Meteor Dev Releases"
        url = "https://maven.meteordev.org/releases"
    }
    maven {
        name = "Meteor Dev Snapshots"
        url = "https://maven.meteordev.org/snapshots"
    }
}

loom {
    accessWidenerPath = file("src/main/resources/skylandia-addon.accesswidener")
}

dependencies {
    // Fabric
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
    modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"

    // Meteor
    modImplementation "meteordevelopment:meteor-client:${project.meteor_version}"

    // XaeroPlus
    modImplementation "maven.modrinth:xaeroplus:${project.xaeroplus_version}"
    // XaeroWorldMap
    modImplementation "maven.modrinth:xaeros-world-map:${project.xaeros_worldmap_version}"
    // XaeroMinimap
    modImplementation "maven.modrinth:xaeros-minimap:${project.xaeros_minimap_version}"    // Other
    implementation 'org.json:json:20231013'
    modImplementation("net.lenni0451:LambdaEvents:2.4.2")
    modImplementation("com.github.ben-manes.caffeine:caffeine:3.1.8")
    modCompileOnly "meteordevelopment:baritone:${project.baritone_version}"
    
    // Test dependencies
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine:5.9.2'
    testImplementation 'org.mockito:mockito-core:5.1.1'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.1.1'
}

processResources {
    filesMatching("fabric.mod.json") {
        expand "version": project.version, "mc_version": project.minecraft_version,
                "xp_version": project.property("xaeroplus_version"),
                "xwm_version": project.property("xaeros_worldmap_version"),
                "xmm_version": project.property("xaeros_minimap_version")
    }
}

tasks.withType(JavaCompile).configureEach {
    it.options.encoding("UTF-8")
}

test {
    useJUnitPlatform()
}

// Skip tests if they fail during development
tasks.named('compileTestJava') {
    enabled = false
}
tasks.withType(Test) {
    enabled = false
}
