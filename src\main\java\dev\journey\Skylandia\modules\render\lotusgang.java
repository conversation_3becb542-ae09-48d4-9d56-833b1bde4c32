import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.config.Config;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.renderer.Renderer2D;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.*;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;
import org.joml.Vector3d;

import java.util.*;

public class RoleTags extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgRender = settings.createGroup("Render");
    private final SettingGroup sgRoles = settings.createGroup("Roles");
    private final SettingGroup sgPosition = settings.createGroup("Position");


    private final Setting<Double> xOffset = sgPosition.add(new DoubleSetting.Builder()
        .name("x-offset")
        .description("Horizontal offset for the role tag.")
        .defaultValue(115.0)
        .min(-500.0)
        .max(500.0)
        .sliderRange(-500.0, 500.0)
        .build()
    );

    private final Setting<Double> yOffset = sgPosition.add(new DoubleSetting.Builder()
        .name("y-offset")
        .description("Vertical offset for the role tag.")
        .defaultValue(-1.0)
        .min(-500.0)
        .max(500.0)
        .sliderRange(-500.0, 500.0)
        .build()
    );
    
    private final Setting<Boolean> showUnlisted = sgRoles.add(new BoolSetting.Builder()
        .name("show-unlisted")
        .description("Whether to show tags for unlisted players.")
        .defaultValue(false)
        .build()
    );

    private final Setting<String> recruitPlayers = sgRoles.add(new StringSetting.Builder()
        .name("recruit-players")
        .description("Recruit players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> memberPlayers = sgRoles.add(new StringSetting.Builder()
        .name("member-players")
        .description("Member players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> elitePlayers = sgRoles.add(new StringSetting.Builder()
        .name("elite-players")
        .description("Elite players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> adminPlayers = sgRoles.add(new StringSetting.Builder()
        .name("admin-players")
        .description("Admin players (comma separated)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> ownerPlayers = sgRoles.add(new StringSetting.Builder()
        .name("owner-players")
        .description("Owner players (comma separated)")
        .defaultValue("")
        .build()
    );

    // Role colors
    private final Setting<SettingColor> unlistedColor = sgRoles.add(new ColorSetting.Builder()
        .name("unlisted-color")
        .description("Color for unlisted players")
        .defaultValue(new SettingColor(150, 150, 150))
        .build()
    );

    private final Setting<SettingColor> recruitColor = sgRoles.add(new ColorSetting.Builder()
        .name("recruit-color")
        .description("Color for recruits")
        .defaultValue(new SettingColor(85, 255, 85))
        .build()
    );

    private final Setting<SettingColor> memberColor = sgRoles.add(new ColorSetting.Builder()
        .name("member-color")
        .description("Color for members")
        .defaultValue(new SettingColor(85, 85, 255))
        .build()
    );

    private final Setting<SettingColor> eliteColor = sgRoles.add(new ColorSetting.Builder()
        .name("elite-color")
        .description("Color for elite members")
        .defaultValue(new SettingColor(170, 0, 170))
        .build()
    );

    private final Setting<SettingColor> adminColor = sgRoles.add(new ColorSetting.Builder()
        .name("admin-color")
        .description("Color for admins")
        .defaultValue(new SettingColor(255, 85, 85))
        .build()
    );

    private final Setting<SettingColor> ownerColor = sgRoles.add(new ColorSetting.Builder()
        .name("owner-color")
        .description("Color for owners")
        .defaultValue(new SettingColor(255, 170, 0))
        .build()
    );

    private final Setting<Boolean> showBackground = sgRender.add(new BoolSetting.Builder()
        .name("background")
        .description("Whether to show background behind the role text.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> fixedWidth = sgRender.add(new IntSetting.Builder()
        .name("fixed-width")
        .description("Fixed width for all role tags in pixels")
        .defaultValue(80)
        .min(40)
        .max(120)
        .sliderRange(40, 120)
        .build()
    );

    private final Color background = new Color(50, 50, 50, 255);

    private final Setting<Double> scale = sgGeneral.add(new DoubleSetting.Builder()
        .name("scale")
        .description("Scale of the role text.")
        .defaultValue(1.0)
        .min(0.1)
        .max(5.0)
        .sliderRange(0.1, 5.0)
        .build()
    );

    private final List<Entity> entityList = new ArrayList<>();
    private final Vector3d pos;
    private final Set<String> recruitNames = new HashSet<>();
    private final Set<String> memberNames = new HashSet<>();
    private final Set<String> eliteNames = new HashSet<>();
    private final Set<String> adminNames = new HashSet<>();
    private final Set<String> ownerNames = new HashSet<>();

    public RoleTags() {
        super(DecaTemplate.CATEGORY, "RoleTag", "Displays player rolesnear there nametag");
        this.pos = new Vector3d();
    }

    @Override
    public void onActivate() {
        updateRoleLists();
    }

    private void updateRoleLists() {
        recruitNames.clear();
        memberNames.clear();
        eliteNames.clear();
        adminNames.clear();
        ownerNames.clear();

        addNamesToSet(recruitPlayers.get(), recruitNames);
        addNamesToSet(memberPlayers.get(), memberNames);
        addNamesToSet(elitePlayers.get(), eliteNames);
        addNamesToSet(adminPlayers.get(), adminNames);
        addNamesToSet(ownerPlayers.get(), ownerNames);
    }

    private void addNamesToSet(String input, Set<String> set) {
        if (input == null || input.isEmpty()) return;

        String[] names = input.split(",");
        for (String name : names) {
            String trimmed = name.trim();
            if (!trimmed.isEmpty()) {
                set.add(trimmed.toLowerCase());
            }
        }
    }

    private String getPlayerRole(PlayerEntity player) {
        String name = player.getName().getString().toLowerCase();

        if (ownerNames.contains(name)) return "Owner";
        if (adminNames.contains(name)) return "Admin";
        if (eliteNames.contains(name)) return "Elite";
        if (memberNames.contains(name)) return "Member";
        if (recruitNames.contains(name)) return "Recruit";
        return "Unlisted";
    }

    private boolean shouldRenderRole(PlayerEntity player) {
        String role = getPlayerRole(player);
        return !role.equals("Unlisted") || showUnlisted.get();
    }

    private Color getRoleColor(String role) {
        switch (role) {
            case "Owner": return ownerColor.get();
            case "Admin": return adminColor.get();
            case "Elite": return eliteColor.get();
            case "Member": return memberColor.get();
            case "Recruit": return recruitColor.get();
            default: return unlistedColor.get();
        }
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        entityList.clear();
        Vec3d cameraPos = mc.gameRenderer.getCamera().getPos();

        assert mc.world != null;
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof PlayerEntity && entity != mc.player) {
                entityList.add(entity);
            }
        }

        entityList.sort(Comparator.comparingDouble(e -> e.squaredDistanceTo(cameraPos)));
    }

    private void drawBg(double x, double y, double width, double height) {
        if (!showBackground.get()) return;

        Renderer2D.COLOR.begin();
        Renderer2D.COLOR.quad(x - 1.0, y - 1.0, width + 2.0, height + 2.0, background);
        Renderer2D.COLOR.quad(x, y, width, height, new Color(30, 30, 30, 255));
        Renderer2D.COLOR.render(null);
    }

    private void renderRoleTag(Render2DEvent event, PlayerEntity player, boolean shadow) {
        if (!shouldRenderRole(player)) return;

        TextRenderer text = TextRenderer.get();
        NametagUtils.begin(pos);

        String role = getPlayerRole(player);
        Color roleColor = getRoleColor(role);
        String roleText = "[" + role + "]";

        // Center text in fixed width box
        double textWidth = text.getWidth(roleText, shadow);
        double width = fixedWidth.get();
        double widthHalf = width / 2.0;
        double height = text.getHeight(shadow);

        text.beginBig();

        if (showBackground.get()) {
            drawBg(-widthHalf + xOffset.get(), -height + yOffset.get(), width, height);
        }

        // Calculate centered text position
        double textX = -widthHalf + (width - textWidth) / 2 + xOffset.get();
        text.render(roleText, textX, -height + yOffset.get(), roleColor, shadow);
        text.end();

        NametagUtils.end();
    }

    private double getHeight(Entity entity) {
        double height = entity.getEyeHeight(entity.getPose());
        if (entity.getType() != EntityType.ITEM && entity.getType() != EntityType.ITEM_FRAME) {
            height += 0.5;
        } else {
            height += 0.2;
        }
        return height;
    }

    @EventHandler
    private void onRender2D(Render2DEvent event) {
        boolean shadow = Config.get().customFont.get();

        for (Entity entity : entityList) {
            Utils.set(pos, entity, event.tickDelta);
            pos.add(0.0, getHeight(entity), 0.0);

            if (!NametagUtils.to2D(pos, scale.get())) continue;

            if (entity.getType() == EntityType.PLAYER) {
                renderRoleTag(event, (PlayerEntity)entity, shadow);
            }
        }
    }
}