package dev.journey.Skylandia.modules.exploration;

import dev.journey.Skylandia.Skylandia;
import net.minecraft.client.MinecraftClient;
import net.minecraft.item.Items;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.chunk.WorldChunk;
import xaero.common.minimap.waypoints.Waypoint;
import xaero.hud.minimap.BuiltInHudModules;
import xaero.hud.minimap.module.MinimapSession;
import xaero.hud.minimap.waypoint.set.WaypointSet;
import xaero.hud.minimap.world.MinimapWorld;
import xaero.map.mods.SupportMods;
import xaeroplus.XaeroPlus;
import xaeroplus.module.ModuleManager;
import xaeroplus.module.impl.PaletteNewChunks;

import java.util.*;

public class SearchBot extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgControl = settings.createGroup("Control");
    private final SettingGroup sgMovement = settings.createGroup("Movement");
    private final SettingGroup sgElytra = settings.createGroup("Elytra");
    private final SettingGroup sgSearch = settings.createGroup("Search");
    private final SettingGroup sgRender = settings.createGroup("Render");

    // Elytra Settings
    private final Setting<Double> targetHeight = sgElytra.add(new DoubleSetting.Builder()
        .name("Target Height")
        .description("Target Y level to maintain while flying")
        .defaultValue(120.0)
        .min(0)
        .sliderMax(256)
        .build()
    );

    private final Setting<Double> velocityThreshold = sgElytra.add(new DoubleSetting.Builder()
        .name("Speed Threshold")
        .description("Use firework if speed drops below this value")
        .defaultValue(0.7)
        .min(0.1)
        .sliderMax(2.0)
        .build()
    );

    private final Setting<Integer> fireworkDelay = sgElytra.add(new IntSetting.Builder()
        .name("Firework Delay")
        .description("Minimum delay between fireworks in ms")
        .defaultValue(3000)
        .min(500)
        .sliderMax(10000)
        .build()
    );

    private final Setting<Double> pitchSmoothness = sgElytra.add(new DoubleSetting.Builder()
        .name("Pitch Smoothness")
        .description("How smoothly to adjust pitch (0-1)")
        .defaultValue(0.1)
        .min(0.01)
        .sliderMax(1.0)
        .build()
    );

    // Movement Settings
    private final Setting<Double> moveSpeed = sgMovement.add(new DoubleSetting.Builder()
        .name("Movement Speed")
        .description("Base speed for movement commands")
        .defaultValue(1.0)
        .min(0.1)
        .sliderMax(5.0)
        .build()
    );

    private final Setting<Double> rotationSpeed = sgMovement.add(new DoubleSetting.Builder()
        .name("Rotation Speed")
        .description("How fast to rotate towards target points (0-1)")
        .defaultValue(0.2)
        .min(0.05)
        .sliderMax(1.0)
        .build()
    );

    private final Setting<Integer> pointTimeout = sgMovement.add(new IntSetting.Builder()
        .name("Point Timeout")
        .description("How long to wait at a point before moving to next (ticks)")
        .defaultValue(20)
        .min(5)
        .sliderMax(100)
        .build()
    );

    private final Setting<Double> arrivalDistance = sgMovement.add(new DoubleSetting.Builder()
        .name("Arrival Distance")
        .description("Distance to consider point reached")
        .defaultValue(3.0)
        .min(1.0)
        .sliderMax(16.0)
        .build()
    );

    // General Settings
    private final Setting<SearchPattern> pattern = sgGeneral.add(new EnumSetting.Builder<SearchPattern>()
        .name("Search Pattern")
        .description("The pattern to use for searching")
        .defaultValue(SearchPattern.SNAKE)
        .build()
    );

    private final Setting<Integer> searchRadius = sgControl.add(new IntSetting.Builder()
        .name("Search Radius")
        .description("Maximum search radius in chunks")
        .defaultValue(32)
        .min(8)
        .sliderMax(128)
        .build()
    );

    private final Setting<Boolean> returnToStart = sgControl.add(new BoolSetting.Builder()
        .name("Return To Start")
        .description("Return to starting position when done")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> avoidLava = sgControl.add(new BoolSetting.Builder()
        .name("Avoid Lava")
        .description("Try to avoid flying over lava")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> pauseOnDisconnect = sgControl.add(new BoolSetting.Builder()
        .name("Pause on Disconnect")
        .description("Pause the search pattern if disconnected")
        .defaultValue(true)
        .build()
    );

    // Search Settings
    private final Setting<Integer> pointLimit = sgSearch.add(new IntSetting.Builder()
        .name("Point Limit")
        .description("Maximum number of search points to generate")
        .defaultValue(100)
        .min(10)
        .sliderMax(500)
        .build()
    );

    private final Setting<Integer> gridSpacing = sgSearch.add(new IntSetting.Builder()
        .name("Grid Spacing")
        .description("Spacing between grid points in chunks")
        .defaultValue(4)
        .min(1)
        .sliderMax(16)
        .visible(() -> pattern.get() == SearchPattern.GRID)
        .build()
    );

    private final Setting<Integer> spiralSpacing = sgSearch.add(new IntSetting.Builder()
        .name("Spiral Spacing")
        .description("Spacing between spiral rings in chunks")
        .defaultValue(8)
        .min(2)
        .sliderMax(32)
        .visible(() -> pattern.get() == SearchPattern.SPIRAL)
        .build()
    );

    private final Setting<Integer> randomPoints = sgSearch.add(new IntSetting.Builder()
        .name("Random Points")
        .description("Number of random points to generate")
        .defaultValue(25)
        .min(5)
        .sliderMax(100)
        .visible(() -> pattern.get() == SearchPattern.RANDOM)
        .build()
    );

    private final Setting<Boolean> optimizePath = sgSearch.add(new BoolSetting.Builder()
        .name("Optimize Path")
        .description("Try to optimize the search path")
        .defaultValue(true)
        .build()
    );

    private final Setting<List<String>> customWaypoints = sgSearch.add(new StringListSetting.Builder()
        .name("Custom Waypoints")
        .description("List of waypoints for custom pattern (format: x,z)")
        .defaultValue(Collections.emptyList())
        .visible(() -> pattern.get() == SearchPattern.CUSTOM)
        .build()
    );

    // Render Settings
    private final Setting<Boolean> showPath = sgRender.add(new BoolSetting.Builder()
        .name("Show Path")
        .description("Renders the search pattern path")
        .defaultValue(true)
        .build()
    );

    private final Setting<PathStyle> pathStyle = sgRender.add(new EnumSetting.Builder<PathStyle>()
        .name("Path Style")
        .description("How to render the path")
        .defaultValue(PathStyle.FULL)
        .visible(() -> showPath.get())
        .build()
    );

    // Grid visualization settings
    private final Setting<Boolean> showTrackingGrid = sgRender.add(new BoolSetting.Builder()
        .name("Show Tracking Grid")
        .description("Shows a grid of chunks with their exploration status")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> gridSize = sgRender.add(new IntSetting.Builder()
        .name("Grid Size")
        .description("Size of the tracking grid in chunks")
        .defaultValue(32)
        .min(8)
        .sliderMax(64)
        .visible(() -> showTrackingGrid.get())
        .build()
    );

    private final Setting<SettingColor> gridColor = sgRender.add(new ColorSetting.Builder()
        .name("Grid Color")
        .description("Color of the grid lines")
        .defaultValue(new SettingColor(0, 255, 0, 100))
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<SettingColor> newChunkColor = sgRender.add(new ColorSetting.Builder()
        .name("New Chunk Color")
        .description("Color for new chunks")
        .defaultValue(new SettingColor(255, 0, 0, 150))
        .visible(() -> showTrackingGrid.get())
        .build()
    );

    private final Setting<SettingColor> oldChunkColor = sgRender.add(new ColorSetting.Builder()
        .name("Old Chunk Color")
        .description("Color for old chunks")
        .defaultValue(new SettingColor(0, 255, 0, 150))
        .visible(() -> showTrackingGrid.get())
        .build()
    );


    private final Setting<SettingColor> currentPathColor = sgRender.add(new ColorSetting.Builder()
        .name("Current Path Color")
        .description("Color of the current path segment")
        .defaultValue(new SettingColor(255, 0, 0, 255))
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<SettingColor> nextPathColor = sgRender.add(new ColorSetting.Builder()
        .name("Next Path Color")
        .description("Color of the upcoming path segment")
        .defaultValue(new SettingColor(0, 255, 0, 150))
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<SettingColor> pathColor = sgRender.add(new ColorSetting.Builder()
        .name("Path Color")
        .description("Color of the search path")
        .defaultValue(new SettingColor(255, 0, 0, 150))
        .visible(() -> showPath.get())
        .build()
    );

    private final Setting<Integer> renderDistance = sgRender.add(new IntSetting.Builder()
        .name("Render Distance")
        .description("Distance to render path/grid")
        .defaultValue(128)
        .min(32)
        .sliderMax(512)
        .visible(() -> showPath.get())
        .build()
    );

    // Grid state
    private final List<SearchPoint> gridPoints = new ArrayList<>();
    private double gridY;  // Fixed Y level for grid rendering
    private Vec3d gridCenter;
    
    // Movement and tracking state
    private List<SearchPoint> searchPoints = new ArrayList<>();
    private int currentPointIndex = 0;
    private Vec3d startPos;
    private SearchPoint currentTarget;
    private double targetYaw;
    private double targetPitch;
    private boolean isRotating = false;
    private boolean isMoving = false;
    private int pointStayTicks = 0;
    private long lastMoveTime = 0;
    private double lastMoveDistance = 0;
    private double actualMoveSpeed = 0;
    private final ArrayDeque<Long> recentChunks = new ArrayDeque<>(64);
    private Random random = new Random();
    private boolean isPaused = false;
    private Vec3d lastRenderPos = null;

    // Elytra state
    private long lastFireworkTime = 0;
    private double currentHeight = 0;
    private float smoothPitch = 0;
    private boolean elytraEquipped = false;
    private boolean fireworkReady = true;
    private Vec3d lastPosition = null;
    private double currentSpeed = 0;

    private static class SearchPoint {
        public final Vec3d pos;
        public final int index;
        public boolean visited;
        public String label;

        public SearchPoint(Vec3d pos, int index) {
            this.pos = pos;
            this.index = index;
            this.visited = false;
            this.label = String.format("Point %d", index + 1);
        }

        public int getChunkX() {
            return (int) pos.x >> 4;
        }

        public int getChunkZ() {
            return (int) pos.z >> 4;
        }
    }

    // Movement tracking
    private class MovementStats {
        public double heightDelta;
        public double speed;
        public float pitch;

        public MovementStats(double heightDelta, double speed, float pitch) {
            this.heightDelta = heightDelta;
            this.speed = speed;
            this.pitch = pitch;
        }
    }
    private final ArrayDeque<MovementStats> recentMovements = new ArrayDeque<>(20);

    // Performance tracking
    private class PointStats {
        public long timeToReach;
        public double distance;
        public double avgSpeed;

        public PointStats(long time, double dist, double speed) {
            timeToReach = time;
            distance = dist;
            avgSpeed = speed;
        }
    }
    private final ArrayDeque<PointStats> recentPointStats = new ArrayDeque<>(10);

    public SearchBot() {
        super(Skylandia.Hunting, "SearchBot", "Advanced systematic area search with multiple patterns.");
    }

    public enum SearchPattern {
        SNAKE("Snake Pattern"),
        GRID("Grid Search"),
        SPIRAL("Spiral Search"),
        QUADRANT("Quadrant Search"),
        RANDOM("Random Search"),
        CUSTOM("Custom Pattern");

        final String title;
        SearchPattern(String title) {
            this.title = title;
        }
    }

    private void updateMovementStats() {
        if (lastMoveTime > 0 && lastMoveDistance > 0) {
            long timeSpent = System.currentTimeMillis() - lastMoveTime;
            double avgSpeed = lastMoveDistance / (timeSpent / 1000.0);
            recentPointStats.add(new PointStats(timeSpent, lastMoveDistance, avgSpeed));
            if (recentPointStats.size() > 10) recentPointStats.poll();
            
            // Update actual move speed based on performance
            actualMoveSpeed = recentPointStats.stream()
                .mapToDouble(stats -> stats.avgSpeed)
                .average()
                .orElse(moveSpeed.get());
        }
    }

    private void updateMovementStats(Vec3d playerPos) {
        // Skip if no previous position
        if (lastPosition == null) {
            lastPosition = playerPos;
            return;
        }

        // Calculate movement metrics
        Vec3d movement = playerPos.subtract(lastPosition);
        double heightDelta = movement.y;
        double horizontalSpeed = movement.multiply(1, 0, 1).length() * 20; // blocks per second
        
        // Update stats tracking
        recentMovements.add(new MovementStats(heightDelta, horizontalSpeed, mc.player.getPitch()));
        if (recentMovements.size() > 20) recentMovements.removeFirst();

        // Update current state
        currentSpeed = horizontalSpeed;
        currentHeight = playerPos.y;
        lastPosition = playerPos;

        // Log significant changes
        if (Math.abs(heightDelta) > 2.0) {
            info(String.format("Height change: %.1f blocks (now at Y=%.1f)", heightDelta, currentHeight));
        }
        
        // Check if speed is too low
        if (horizontalSpeed < velocityThreshold.get() && fireworkReady) {
            info(String.format("Speed low (%.1f b/s) - attempting to use firework", horizontalSpeed));
        }
        
        // Calculate average stats
        if (recentMovements.size() >= 5) {
            double avgSpeed = recentMovements.stream().mapToDouble(s -> s.speed).average().orElse(0);
            double avgHeight = recentMovements.stream().mapToDouble(s -> s.heightDelta).average().orElse(0);
            if (mc.player.age % 100 == 0) { // Log every 5 seconds
                info(String.format("Movement stats - Speed: %.1f b/s, Height change: %.1f b/s",
                    avgSpeed, avgHeight * 20));
            }
        }
    }

    public enum PathStyle {
        FULL("Full Path"),
        CURRENT("Current Segment"),
        POINTS("Points Only");

        final String title;
        PathStyle(String title) {
            this.title = title;
        }
    }

    @Override
    public void onActivate() {
        if (mc.player == null) return;
        
        // Initialize grid and search points
        startPos = mc.player.getPos();
        gridCenter = startPos;
        gridY = startPos.y;
        
        // Clear existing points
        gridPoints.clear();
        searchPoints.clear();
        currentPointIndex = 0;
        
        // Generate search points with indices
        initializeSearchPattern();
        
        // Initialize elytra state
        lastPosition = mc.player.getPos();
        currentHeight = mc.player.getY();
        smoothPitch = mc.player.getPitch();
        lastFireworkTime = 0;
        elytraEquipped = false;
        fireworkReady = true;
        recentMovements.clear();

        // Register event handlers
        XaeroPlus.EVENT_BUS.register(this);
        
        // Check for required items
        FindItemResult elytra = InvUtils.findInHotbar(Items.ELYTRA);
        FindItemResult firework = InvUtils.findInHotbar(Items.FIREWORK_ROCKET);
        
        if (!elytra.found()) {
            error("No elytra found in hotbar! You need an elytra to use SearchBot.");
            this.toggle();
            return;
        }
        
        if (!firework.found()) {
            warning("No firework rockets found in hotbar! Movement will be limited.");
        }
        
        info(String.format("SearchBot activated - Grid centered at (%.1f, %.1f, %.1f) with %d points",
            gridCenter.x, gridY, gridCenter.z, searchPoints.size()));

        // Save waypoints
        if (saveToWaypoints.get()) {
            addGridWaypoints();
        }
    }

    private void initializeSearchPattern() {
        gridPoints.clear();
        searchPoints.clear();
        currentPointIndex = 0;
        
        info("Generating search path using " + pattern.get().title + " pattern...");

        // Calculate grid points first
        generateGridPoints();
        
        // Convert grid points to search points with sequential numbering
        for (int i = 0; i < gridPoints.size(); i++) {
            Vec3d point = gridPoints.get(i).pos;
            searchPoints.add(new SearchPoint(point, i));
        }

        // Optimize path if enabled
        if (optimizePath.get() && pattern.get() != SearchPattern.SPIRAL) {
            optimizeSearchPath();
        }

        // Add return point if enabled
        if (returnToStart.get()) {
            searchPoints.add(new SearchPoint(startPos, searchPoints.size()));
        }

        info("Generated " + searchPoints.size() + " search points");
    }

    private void generateGridPoints() {
        gridPoints.clear();
        int index = 0;

        // Generate points based on selected pattern
        int limit = Math.min(pointLimit.get(),
            pattern.get() == SearchPattern.RANDOM ? randomPoints.get() : 500);

        switch (pattern.get()) {
            case SNAKE -> generateSnakeGridPattern(limit);
            case GRID -> generateGridGridPattern(limit);
            case SPIRAL -> generateSpiralGridPattern(limit);
            case QUADRANT -> generateQuadrantGridPattern(limit);
            case RANDOM -> generateRandomGridPattern(limit);
            case CUSTOM -> generateCustomGridPattern(limit);
        }

        info("Generated grid with " + gridPoints.size() + " points");
    }

    private void addGridWaypoints() {
        WaypointSet waypointSet = getWaypointSet();
        if (waypointSet == null) {
            error("Could not get waypoint set!");
            return;
        }

        // Remove existing waypoints
        removeExistingWaypoints();

        // Add new waypoints for each search point
        for (SearchPoint point : searchPoints) {
            String name = String.format("Search %d", point.index + 1);
            int color = point.visited ? 0x00FF00 : 0xFF0000; // Green if visited, red if not

            Waypoint waypoint = new Waypoint(
                (int) point.pos.x,
                (int) gridY,
                (int) point.pos.z,
                name,
                "S",
                color,
                0,
                false
            );
            waypointSet.add(waypoint);
        }

        info("Added " + searchPoints.size() + " waypoints");
        SupportMods.xaeroMinimap.requestWaypointsRefresh();
    }

    private void removeExistingWaypoints() {
        WaypointSet waypointSet = getWaypointSet();
        if (waypointSet == null) return;

        // Remove waypoints that match our naming pattern
        List<Waypoint> toRemove = new ArrayList<>();
        for (Waypoint wp : waypointSet.getWaypoints()) {
            if (wp.getName().startsWith("Search ")) {
                toRemove.add(wp);
            }
        }
        
        for (Waypoint wp : toRemove) {
            waypointSet.remove(wp);
        }
        
        SupportMods.xaeroMinimap.requestWaypointsRefresh();
    }


    private WaypointSet getWaypointSet() {
        MinimapSession minimapSession = BuiltInHudModules.MINIMAP.getCurrentSession();
        if (minimapSession == null) return null;
        MinimapWorld currentWorld = minimapSession.getWorldManager().getCurrentWorld();
        if (currentWorld == null) return null;
        return currentWorld.getCurrentWaypointSet();
    }

    private List<Waypoint> getActiveWaypoints() {
        WaypointSet set = getWaypointSet();
        List<Waypoint> points = new ArrayList<>();
        if (set != null) {
            Iterator<Waypoint> it = set.getWaypoints().iterator();
            while (it.hasNext()) {
                points.add(it.next());
            }
        }
        return points;
    }

    private void optimizeSearchPath() {
        if (searchPoints.size() < 3) return;

        List<SearchPoint> optimized = new ArrayList<>();
        Set<SearchPoint> unvisited = new HashSet<>(searchPoints);
        
        SearchPoint current = unvisited.iterator().next();
        unvisited.remove(current);
        optimized.add(current);

        while (!unvisited.isEmpty()) {
            SearchPoint nearest = null;
            double minDist = Double.MAX_VALUE;
            
            for (SearchPoint point : unvisited) {
                double dist = current.pos.squaredDistanceTo(point.pos);
                if (dist < minDist) {
                    minDist = dist;
                    nearest = point;
                }
            }
            
            current = nearest;
            optimized.add(current);
            unvisited.remove(current);
        }

        // Update indices for optimized path
        for (int i = 0; i < optimized.size(); i++) {
            optimized.get(i).label = String.format("Point %d", i + 1);
        }

        searchPoints = optimized;

        // Validate points
        if (searchPoints.isEmpty()) {
            error("No valid search points were generated!");
            this.toggle();
            return;
        }

        // Calculate total path distance
        double totalDistance = 0;
        Vec3d lastPos = startPos;
        for (SearchPoint point : searchPoints) {
            totalDistance += point.pos.distanceTo(lastPos);
            lastPos = point.pos;
        }

        info(String.format("Search path generated: %d points covering %.1f blocks",
            searchPoints.size(), totalDistance));
    }

    private void generateSnakeGridPattern(int limit) {
        Vec3d current = gridCenter;
        double yaw = 180;
        boolean right = true;
        int points = 0;
        int spacing = gridSpacing.get() * 16;

        while (points < limit) {
            Vec3d next = current.add(Vec3d.fromPolar(0, (float)yaw).multiply(spacing));
            next = new Vec3d(next.x, gridY, next.z); // Keep fixed Y-level

            if (isPointInRange(next)) {
                gridPoints.add(new SearchPoint(next, points));
                current = next;
                points++;

                if (points % 4 == 0) {
                    yaw += right ? -90 : 90;
                    right = !right;
                }
            } else break;
        }
    }

    // Waypoint Settings
    private final SettingGroup sgWaypoints = settings.createGroup("Waypoints");
    private final Setting<Boolean> saveToWaypoints = sgWaypoints.add(new BoolSetting.Builder()
        .name("save-to-waypoints")
        .description("Creates waypoints for each grid point")
        .defaultValue(true)
        .onChanged(this::onWaypointSettingChanged)
        .build()
    );

    private final Setting<Double> waypointHeight = sgWaypoints.add(new DoubleSetting.Builder()
        .name("waypoint-height")
        .description("Height to place waypoints at")
        .defaultValue(120.0)
        .min(0)
        .sliderMax(256)
        .build()
    );

    private void generateGridGridPattern(int limit) {
        int spacing = gridSpacing.get();
        int radius = Math.min(searchRadius.get(), (int)Math.sqrt(limit));
        int points = 0;
        
        for (int x = -radius; x <= radius; x += spacing) {
            for (int z = -radius; z <= radius; z += spacing) {
                if (points >= limit) return;
                
                Vec3d point = gridCenter.add(x * 16, 0, z * 16);
                point = new Vec3d(point.x, gridY, point.z); // Keep fixed Y-level
                
                if (isPointInRange(point)) {
                    gridPoints.add(new SearchPoint(point, points++));
                }
            }
        }
    }

    private void resetSearch() {
        initializeSearchPattern();
        lastPosition = mc.player.getPos();
        currentHeight = mc.player.getY();
        recentMovements.clear();
        info("Search pattern reset");
    }

    private void onWaypointSettingChanged(boolean enabled) {
        if (enabled) {
            addGridWaypoints();
        } else {
            removeExistingWaypoints();
        }
    }

    private void generateSpiralGridPattern(int limit) {
        int spacing = spiralSpacing.get();
        double angle = 0;
        double radius = 0;
        int points = 0;
        
        while (points < limit) {
            double x = gridCenter.x + radius * Math.cos(angle);
            double z = gridCenter.z + radius * Math.sin(angle);
            Vec3d point = new Vec3d(x, gridY, z);
            
            if (!isPointInRange(point)) break;
            
            gridPoints.add(new SearchPoint(point, points++));
            angle += Math.PI / 8;
            radius += spacing * Math.PI / 16;
        }
    }

    private void generateQuadrantGridPattern(int limit) {
        int pointsPerQuadrant = limit / 4;
        int spacing = gridSpacing.get();
        int totalPoints = 0;
        
        for (int quadrant = 0; quadrant < 4; quadrant++) {
            int startX = (quadrant % 2 == 0) ? 0 : -searchRadius.get();
            int startZ = (quadrant / 2 == 0) ? 0 : -searchRadius.get();
            int points = 0;
            
            for (int x = startX; Math.abs(x) <= searchRadius.get() && points < pointsPerQuadrant; x += spacing) {
                for (int z = startZ; Math.abs(z) <= searchRadius.get() && points < pointsPerQuadrant; z += spacing) {
                    Vec3d point = gridCenter.add(x * 16, 0, z * 16);
                    point = new Vec3d(point.x, gridY, point.z);
                    
                    if (isPointInRange(point)) {
                        gridPoints.add(new SearchPoint(point, totalPoints++));
                        points++;
                    }
                }
            }
        }
    }

    private void generateCustomGridPattern(int limit) {
        List<String> waypoints = customWaypoints.get();
        if (waypoints.isEmpty()) return;
        int points = 0;

        for (String waypoint : waypoints) {
            if (points >= limit) break;
            
            String[] coords = waypoint.split(",");
            if (coords.length == 2) {
                try {
                    double x = Double.parseDouble(coords[0]);
                    double z = Double.parseDouble(coords[1]);
                    Vec3d point = new Vec3d(x, gridY, z);
                    
                    if (isPointInRange(point)) {
                        gridPoints.add(new SearchPoint(point, points++));
                    }
                } catch (NumberFormatException e) {
                    error("Invalid waypoint format: " + waypoint);
                }
            }
        }
    }

    private void generateRandomGridPattern(int limit) {
        int radius = searchRadius.get() * 16;
        int attempts = 0;
        int maxAttempts = limit * 2;
        int points = 0;

        while (points < limit && attempts < maxAttempts) {
            double angle = random.nextDouble() * Math.PI * 2;
            double dist = random.nextDouble() * radius;
            
            Vec3d point = gridCenter.add(
                Math.cos(angle) * dist,
                0,
                Math.sin(angle) * dist
            );
            point = new Vec3d(point.x, gridY, point.z);
            
            if (isPointInRange(point) && !isTooClose(point)) {
                gridPoints.add(new SearchPoint(point, points++));
            }
            attempts++;
        }
    }

    private boolean isTooClose(Vec3d point) {
        double minDistance = gridSpacing.get() * 16;
        return gridPoints.stream()
            .anyMatch(p -> p.pos.squaredDistanceTo(point) < minDistance * minDistance);
    }

    private boolean canLoadWaypoints() {
        if (!saveToWaypoints.get()) return false;

        MinimapSession session = BuiltInHudModules.MINIMAP.getCurrentSession();
        if (session == null) {
            error("Cannot load waypoints - minimap session not available");
            return false;
        }

        MinimapWorld world = session.getWorldManager().getCurrentWorld();
        if (world == null) {
            error("Cannot load waypoints - minimap world not available");
            return false;
        }

        WaypointSet waypoints = world.getCurrentWaypointSet();
        if (waypoints == null) {
            error("Cannot load waypoints - waypoint set not available");
            return false;
        }

        return true;
    }

    private boolean isPointInRange(Vec3d point) {
        double maxDist = searchRadius.get() * 16;
        return point.subtract(startPos).lengthSquared() <= maxDist * maxDist;
    }

    private void handlePointArrival(double distanceToTarget) {
        if (pointStayTicks++ >= pointTimeout.get()) {
            // Mark current point as visited
            if (currentTarget != null) {
                currentTarget.visited = true;
                
                // Update waypoint if enabled
                if (saveToWaypoints.get() && canLoadWaypoints()) {
                    WaypointSet waypointSet = getWaypointSet();
                    if (waypointSet != null) {
                        // Update waypoint color to show completion
                        Waypoint oldWaypoint = null;
                        for (Waypoint wp : waypointSet.getWaypoints()) {
                            if (wp.getName().equals("Search " + (currentPointIndex + 1))) {
                                oldWaypoint = wp;
                                break;
                            }
                        }
                        
                        if (oldWaypoint != null) {
                            waypointSet.remove(oldWaypoint);
                            waypointSet.add(new Waypoint(
                                (int)currentTarget.pos.x,
                                (int)gridY,
                                (int)currentTarget.pos.z,
                                oldWaypoint.getName(),
                                "S",
                                0x00FF00, // Green for visited
                                0,
                                false
                            ));
                            SupportMods.xaeroMinimap.requestWaypointsRefresh();
                        }
                    }
                }
            }

            // Record movement stats
            updateMovementStats();
            
            // Move to next point
            currentPointIndex = (currentPointIndex + 1) % searchPoints.size();
            if (currentPointIndex == 0 && !returnToStart.get()) {
                info("Search pattern complete!");
                this.toggle();
                return;
            }

            // Reset state for next point
            isRotating = true;
            isMoving = false;
            pointStayTicks = 0;
            lastMoveTime = System.currentTimeMillis();
            lastMoveDistance = distanceToTarget;
        }
    }

    @Override
    public void onDeactivate() {
        try {
            // Clean up event handlers
            XaeroPlus.EVENT_BUS.unregister(this);
            
            // Remove waypoints if enabled
            if (saveToWaypoints.get()) {
                info("Removing search waypoints...");
                removeExistingWaypoints();
            }
            
            // Reset collections
            gridPoints.clear();
            searchPoints.clear();
            recentChunks.clear();
            recentMovements.clear();
            
            // Reset movement state
            isPaused = false;
            isRotating = false;
            isMoving = false;
            currentPointIndex = 0;
            
            // Try to equip chestplate if we have one
            FindItemResult chestplate = InvUtils.find(item -> item.toString().contains("chestplate"));
            if (chestplate.found() && mc.player != null) {
                InvUtils.move().from(chestplate.slot()).toArmor(2);
            }
            
            info("SearchBot deactivated - Grid and waypoints cleared");
        } catch (Exception e) {
            error("Error during deactivation: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Movement helper methods
    private void checkElytraState() {
        if (mc.player == null) return;
        
        boolean hasElytra = mc.player.getInventory().getArmorStack(2).isOf(Items.ELYTRA);
        boolean isFlying = mc.player.getAbilities().flying;
        boolean isFalling = mc.player.fallDistance > 0;
        
        if (!hasElytra && mc.player.age % 20 == 0) {
            error("No elytra equipped!");
            return;
        }

        if (mc.player.age % 100 == 0) { // Log every 5 seconds
            info(String.format("Elytra state: equipped=%b, flying=%b, fallDist=%.1f, speed=%.1f",
                hasElytra, isFlying, mc.player.fallDistance, currentSpeed));
        }
    }

    private boolean isElytraFlying() {
        if (mc.player == null || !mc.player.getInventory().getArmorStack(2).isOf(Items.ELYTRA)) {
            return false;
        }
        
        return mc.player.getAbilities().flying && mc.player.fallDistance > 0;
    }

    private void moveToPoint(Vec3d target, double speed) {
        Vec3d velocity = target.subtract(mc.player.getPos()).normalize().multiply(speed);
        mc.player.setVelocity(velocity.x, velocity.y, velocity.z);
    }

    private double getAverageSpeed() {
        return recentPointStats.stream()
            .mapToDouble(stats -> stats.avgSpeed)
            .average()
            .orElse(moveSpeed.get());
    }

    private double getOptimalDistance(double speed) {
        // Calculate optimal movement distance based on speed and rotation
        return Math.min(16, speed * (pointTimeout.get() / 20.0));
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        try {
            if (mc.player == null) {
                error("Player is null!");
                this.toggle();
                return;
            }

            if (searchPoints.isEmpty()) {
                error("No search points defined!");
                this.toggle();
                return;
            }

            if (isPaused) {
                if (mc.player.age % 20 == 0) info("Search is paused");
                return;
            }

            // Validate current point index
            if (currentPointIndex >= searchPoints.size()) {
                error("Invalid point index: " + currentPointIndex);
                resetSearch();
                return;
            }

            // Update current state
            Vec3d playerPos = mc.player.getPos();
            currentTarget = searchPoints.get(currentPointIndex);
            Vec3d toTarget = currentTarget.pos.subtract(playerPos);
            double distanceToTarget = toTarget.length();

            // Update movement tracking
            updateMovementStats(playerPos);

            // Point arrival check
            if (distanceToTarget <= arrivalDistance.get()) {
                handlePointArrival(distanceToTarget);
                return;
            }

            // If we're here, we're not at target yet
            pointStayTicks = 0;

            // Handle elytra movement
            checkElytraState();

            if (isElytraFlying()) {
                handleElytraFlight(toTarget);
                
                // Log flight status periodically
                if (mc.player.age % 100 == 0) { // Every 5 seconds
                    info(String.format("Flying to point %d/%d - Distance: %.1f, Speed: %.1f, Height: %.1f | Yaw: %.1f, Pitch: %.1f",
                        currentPointIndex + 1, searchPoints.size(),
                        distanceToTarget, currentSpeed, mc.player.getY(),
                        mc.player.getYaw(), mc.player.getPitch()));
                }
            } else {
                initiateElytraFlight();
            }

            // Check if we need to terminate
            if (!mc.player.getInventory().getArmorStack(2).isOf(Items.ELYTRA) && mc.player.age % 20 == 0) {
                error("Lost elytra equipment! Terminating search.");
                this.toggle();
                return;
            }

            // Check for chunk updates
            updateChunkTracking();

        } catch (Exception e) {
            error("Error in tick: " + e.getMessage());
            e.printStackTrace();
            this.toggle();
        }
    }

    private void handleElytraFlight(Vec3d toTarget) {
        // Calculate target rotation
        targetYaw = Math.toDegrees(Math.atan2(-toTarget.x, toTarget.z));
        
        // Calculate height adjustment
        double heightDiff = mc.player.getY() - targetHeight.get();
        double idealPitch;
        
        if (Math.abs(heightDiff) > 10.0) {
            idealPitch = -Math.toDegrees(Math.atan2(heightDiff, 100));
            info(String.format("Large height difference (%.1f blocks) - Adjusting pitch to %.1f°",
                heightDiff, idealPitch));
        } else if (heightDiff > 2.0) {
            idealPitch = 10.0;
            if (mc.player.age % 20 == 0) // Log every second
                info(String.format("Too high (%.1f blocks) - Pitching down", heightDiff));
        } else if (heightDiff < -2.0) {
            idealPitch = -10.0;
            if (mc.player.age % 20 == 0)
                info(String.format("Too low (%.1f blocks) - Pitching up", heightDiff));
        } else {
            idealPitch = 0.0;
        }

        // Apply smooth pitch adjustment
        float currentPitch = mc.player.getPitch();
        float oldPitch = smoothPitch;
        smoothPitch = (float)(currentPitch + (idealPitch - currentPitch) * pitchSmoothness.get());
        mc.player.setPitch(smoothPitch);

        if (Math.abs(smoothPitch - oldPitch) > 5) {
            info(String.format("Pitch adjusted: %.1f° -> %.1f° (target: %.1f°)",
                oldPitch, smoothPitch, idealPitch));
        }

        // Handle rotation
        if (isRotating) {
            float currentYaw = mc.player.getYaw();
            float yawDiff = (float) angleDifference(targetYaw, currentYaw);
            float newYaw = currentYaw + (float)(yawDiff * rotationSpeed.get());
            mc.player.setYaw(newYaw);

            if (Math.abs(yawDiff) <= 5) {
                info(String.format("Rotation complete - Yaw: %.1f°", newYaw));
                isRotating = false;
                isMoving = true;
            } else if (mc.player.age % 20 == 0) {
                info(String.format("Rotating: %.1f° remaining", yawDiff));
            }
            return;
        }

        // Handle movement
        long currentTime = System.currentTimeMillis();
        boolean needsBoost = currentSpeed < velocityThreshold.get();
        boolean canBoost = currentTime - lastFireworkTime > fireworkDelay.get();
        
        if (needsBoost && canBoost) {
            FindItemResult firework = InvUtils.findInHotbar(Items.FIREWORK_ROCKET);
            if (firework.found()) {
                info(String.format("Using firework - Speed: %.1f (threshold: %.1f)",
                    currentSpeed, velocityThreshold.get()));
                InvUtils.swap(firework.slot(), true);
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                lastFireworkTime = currentTime;
            } else if (mc.player.age % 100 == 0) { // Log warning every 5 seconds
                warning("No fireworks found - Movement may be impaired!");
            }
        }
    }

    private void initiateElytraFlight() {
        // Don't try to fly if we're too close to the ground
        if (mc.player.getY() < targetHeight.get() - 10) {
            mc.player.jump();
            info("Gaining height before elytra activation (Y: " + String.format("%.1f", mc.player.getY()) + ")");
            return;
        }

        // Track equipment state
        boolean hasElytraEquipped = mc.player.getInventory().getArmorStack(2).isOf(Items.ELYTRA);
        FindItemResult elytra = InvUtils.findInHotbar(Items.ELYTRA);
        
        if (!elytra.found() && !hasElytraEquipped) {
            error("No elytra found in hotbar or equipped!");
            this.toggle();
            return;
        }

        // Start flying if not already
        if (!isElytraFlying() && mc.player.fallDistance > 0) {
            info("Initiating elytra flight...");
            
            // Equip elytra if needed
            if (!hasElytraEquipped) {
                info("Equipping elytra from slot " + elytra.slot());
                InvUtils.move().from(elytra.slot()).toArmor(2);
            }
            
            // Send start flying packet
            mc.player.networkHandler.sendPacket(
                new ClientCommandC2SPacket(mc.player, ClientCommandC2SPacket.Mode.START_FALL_FLYING)
            );
            
            info("Flight packet sent - Checking status in 5 ticks");
            // Schedule a status check
            mc.player.age += 5;
            return;
        } else if (!isElytraFlying()) {
            if (mc.player.age % 20 == 0) { // Log every second
                info("Waiting for elytra activation - Fall: " + String.format("%.2f", mc.player.fallDistance) +
                     ", Height: " + String.format("%.1f", mc.player.getY()));
            }
        }
    }

    private void updateChunkTracking() {
        WorldChunk chunk = (WorldChunk)mc.world.getChunk(mc.player.getBlockPos());
        long chunkLong = chunk.getPos().toLong();

        if (!recentChunks.contains(chunkLong)) {
            if (recentChunks.size() >= 64) {
                recentChunks.pollFirst();
            }
            recentChunks.add(chunkLong);

            boolean is119NewChunk = ModuleManager.getModule(PaletteNewChunks.class)
                    .isNewChunk(chunk.getPos().x, chunk.getPos().z, chunk.getWorld().getRegistryKey());
            
            if (!is119NewChunk) {
                Vec3d chunkCenter = new Vec3d(chunk.getPos().x * 16 + 8, mc.player.getY(), chunk.getPos().z * 16 + 8);
                info(String.format("Found old chunk at %d, %d (Distance: %.1f blocks)",
                    chunk.getPos().x, chunk.getPos().z,
                    chunkCenter.subtract(mc.player.getPos()).length()));
            }
        }
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (searchPoints.isEmpty()) return;

        // Render search grid (always at fixed Y)
        if (showPath.get()) {
            renderSearchGrid(event);
            renderWaypoints(event);
            
            // Render current path segment
            if (currentTarget != null) {
                renderCurrentPathSegment(event);
            }
        }

        // Render chunk tracking grid if enabled
        if (showTrackingGrid.get()) {
            renderTrackingGrid(event);
        }
    }

    private void renderSearchGrid(Render3DEvent event) {
        int renderDist = renderDistance.get();
        double maxDistSq = renderDist * renderDist;
        Vec3d playerPos = mc.player.getPos();

        // Draw grid points and connections
        Vec3d last = null;
        for (SearchPoint point : searchPoints) {
            if (point.pos.subtract(playerPos).horizontalLengthSquared() > maxDistSq) continue;

            // Draw point box
            double size = point == currentTarget ? 0.7 : 0.5;
            SettingColor pathColorSetting = point.visited ? nextPathColor.get() : currentPathColor.get();
            if (pathColorSetting == null) {
                info("Path color setting is null - visited: " + point.visited);
                return;
            }
            
            if (pathColorSetting == null) {
                error("Path color setting is null for point " + point.index + " (visited: " + point.visited + ")");
                return;
            }

            Color boxOutline = null;
            Color boxFill = null;
            try {
                boxOutline = new Color(pathColorSetting.r, pathColorSetting.g, pathColorSetting.b, pathColorSetting.a);
                boxFill = new Color(pathColorSetting.r, pathColorSetting.g, pathColorSetting.b, pathColorSetting.a / 4);
            } catch (Exception e) {
                error("Failed to create color objects: " + e.getMessage());
                return;
            }

            if (mc.player.age % 100 == 0) {
                info(String.format("Rendering box for point %d with color RGBA(%d,%d,%d,%d)",
                    point.index, boxOutline.r, boxOutline.g, boxOutline.b, boxOutline.a));
            }
            
            try {
                event.renderer.box(
                    point.pos.x - size, point.pos.y - size, point.pos.z - size,
                    point.pos.x + size, point.pos.y + size, point.pos.z + size,
                    boxOutline,
                    boxFill,
                    ShapeMode.Both,
                    0
                );
            } catch (Exception e) {
                error("Failed to render box: " + e.getMessage());
            }

            // Draw connecting line
            if (last != null) {
                Color lineColor = pathColor.get();
                event.renderer.line(
                    last.x, last.y, last.z,
                    point.pos.x, point.pos.y, point.pos.z,
                    lineColor != null ? lineColor : new Color(255, 0, 0, 255)
                );
            }
            last = point.pos;
        }
    }

    private void renderCurrentPathSegment(Render3DEvent event) {
        if (currentPointIndex >= searchPoints.size()) return;

        Vec3d playerPos = mc.player.getPos();
        Vec3d target = currentTarget.pos;

        // Render target height indicator
        double targetY = targetHeight.get();
        event.renderer.line(
            target.x, targetY - 2, target.z,
            target.x, targetY + 2, target.z,
            new Color(255, 255, 0, 100)
        );

        // Render current target point with arrival radius
        double boxSize = arrivalDistance.get();
        SettingColor targetColor = currentPathColor.get();
        if (targetColor == null) {
            error("Current path color is null during target box rendering");
            return;
        }
        try {
            event.renderer.box(
                target.x - boxSize, target.y - boxSize, target.z - boxSize,
                target.x + boxSize, target.y + boxSize, target.z + boxSize,
                targetColor, null, ShapeMode.Lines, 0
            );
        } catch (Exception e) {
            error("Failed to render target box: " + e.getMessage());
        }

        // Render movement indicators
        drawThickLine(event, playerPos, target, currentPathColor.get());

        // Show movement state
        double indicatorRadius = 2.0;
        if (isRotating) {
            // Show rotation target
            double yaw = Math.toRadians(targetYaw);
            Vec3d rotationEnd = playerPos.add(
                Math.sin(-yaw) * indicatorRadius,
                0,
                Math.cos(-yaw) * indicatorRadius
            );
            event.renderer.line(
                playerPos.x, playerPos.y, playerPos.z,
                rotationEnd.x, rotationEnd.y, rotationEnd.z,
                new Color(255, 255, 0, 255)
            );

            // Show pitch indicator
            float pitch = mc.player.getPitch();
            Vec3d pitchEnd = playerPos.add(
                0,
                -Math.sin(Math.toRadians(pitch)) * indicatorRadius,
                Math.cos(Math.toRadians(pitch)) * indicatorRadius
            );
            event.renderer.line(
                playerPos.x, playerPos.y, playerPos.z,
                pitchEnd.x, pitchEnd.y, pitchEnd.z,
                new Color(0, 255, 255, 255)
            );
        }

        // Show velocity
        if (currentSpeed > 0) {
            Vec3d velocity = mc.player.getVelocity();
            Vec3d speedEnd = playerPos.add(velocity.normalize().multiply(indicatorRadius));
            event.renderer.line(
                playerPos.x, playerPos.y, playerPos.z,
                speedEnd.x, speedEnd.y, speedEnd.z,
                new Color(0, 255, 0, 255)
            );
        }

        // Preview next points with decreasing opacity
        int nextSegments = Math.min(3, searchPoints.size() - currentPointIndex - 1);
        Vec3d lastPoint = target;
        
        for (int i = 0; i < nextSegments; i++) {
            SearchPoint nextPoint = searchPoints.get((currentPointIndex + i + 1) % searchPoints.size());
            float opacity = 0.7f - (i * 0.2f);
            SettingColor color = nextPathColor.get();
            SettingColor previewColor = new SettingColor(color.r, color.g, color.b, (int)(color.a * opacity));
            
            event.renderer.line(
                lastPoint.x, lastPoint.y, lastPoint.z,
                nextPoint.pos.x, nextPoint.pos.y, nextPoint.pos.z,
                previewColor
            );
            
            event.renderer.box(
                nextPoint.pos.x - 0.3, nextPoint.pos.y - 0.3, nextPoint.pos.z - 0.3,
                nextPoint.pos.x + 0.3, nextPoint.pos.y + 0.3, nextPoint.pos.z + 0.3,
                previewColor, null, ShapeMode.Lines, 0
            );
            
            lastPoint = nextPoint.pos;
        }

        // Show current stats in info stream
        if (mc.player.age % 20 == 0) { // Update every second
            info(String.format("Progress: %d/%d points | Speed: %.1f b/s | Height: %.1f",
                currentPointIndex + 1, searchPoints.size(),
                currentSpeed,
                mc.player.getY()));
        }
    }

    private void renderWaypoints(Render3DEvent event) {
        // Render waypoint labels at fixed height
        for (SearchPoint point : searchPoints) {
            double textScale = point == currentTarget ? 2.0 : 1.5;
            // Render point marker at fixed height
            Vec3d labelPos = point.pos.add(0, 1, 0);
            Color labelColor = point.visited ? nextPathColor.get() : currentPathColor.get();
            event.renderer.line(
                labelPos.x - 0.5, labelPos.y, labelPos.z,
                labelPos.x + 0.5, labelPos.y, labelPos.z,
                labelColor != null ? labelColor : new Color(255, 255, 255, 255)
            );
        }
    }

    private void renderTrackingGrid(Render3DEvent event) {
        if (!showTrackingGrid.get()) return;
        
        int size = gridSize.get();
        Vec3d center = mc.player.getPos();
        int chunkX = (int) center.x >> 4;
        int chunkZ = (int) center.z >> 4;
        
        // Draw grid centered on player
        for (int x = -size; x <= size; x++) {
            for (int z = -size; z <= size; z++) {
                int worldChunkX = chunkX + x;
                int worldChunkZ = chunkZ + z;
                
                // Check if chunk is new or old using XaeroPlus
                boolean isNewChunk = ModuleManager.getModule(PaletteNewChunks.class)
                    .isNewChunk(worldChunkX, worldChunkZ, mc.world.getRegistryKey());
                
                // Calculate chunk boundaries
                double x1 = worldChunkX * 16;
                double z1 = worldChunkZ * 16;
                double x2 = x1 + 16;
                double z2 = z1 + 16;
                
                // Get appropriate color
                SettingColor color = isNewChunk ? newChunkColor.get() : oldChunkColor.get();
                // Draw chunk outline with semi-transparent fill
                event.renderer.box(
                    x1, center.y - 1, z1,
                    x2, center.y + 1, z2,
                    color,
                    new Color(color.r, color.g, color.b, color.a / 4),
                    ShapeMode.Both,
                    0
                );
            }
        }
    }

    private void renderGrid(Render3DEvent event) {
        int spacing = gridSpacing.get() * 16;
        int radius = Math.min(renderDistance.get(), searchRadius.get() * 16);
        Vec3d center = lastRenderPos;

        // Draw grid lines within render distance
        for (int offset = -radius; offset <= radius; offset += spacing) {
            // Vertical lines
            event.renderer.line(
                center.x + offset, center.y, center.z - radius,
                center.x + offset, center.y, center.z + radius,
                gridColor.get()
            );

            // Horizontal lines
            event.renderer.line(
                center.x - radius, center.y, center.z + offset,
                center.x + radius, center.y, center.z + offset,
                gridColor.get()
            );
        }
    }
    private void drawThickLine(Render3DEvent event, Vec3d start, Vec3d end, SettingColor color) {
        // Main line
        event.renderer.line(
            start.x, start.y, start.z,
            end.x, end.y, end.z,
            color
        );

        // Additional lines for thickness
        double thickness = 0.15;
        for (int i = 0; i < 4; i++) {
            double angle = i * Math.PI / 2;
            double dx = Math.cos(angle) * thickness;
            double dz = Math.sin(angle) * thickness;

            event.renderer.line(
                start.x + dx, start.y, start.z + dz,
                end.x + dx, end.y, end.z + dz,
                color
            );
        }
    }

    // Rotation helper methods
    private float getActualYaw(float yaw) {
        return (yaw % 360 + 360) % 360;
    }

    private float smoothRotation(double current, double target) {
        double difference = angleDifference(target, current);
        return (float) (current + difference * rotationSpeed.get());
    }

    private double angleDifference(double target, double current) {
        double diff = (target - current + 180) % 360 - 180;
        return diff < -180 ? diff + 360 : diff;
    }
}
