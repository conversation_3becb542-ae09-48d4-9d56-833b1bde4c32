package dev.journey.Skylandia.modules.exploration.detection.structures;

public enum StructureType {
    TRIAL_CHAMBER("Trial Chamber", 0.8),
    DUNGEON("Dungeon", 0.7),
    VILLAGE("Village", 0.75);

    private final String displayName;
    private final double defaultConfidence;

    StructureType(String displayName, double defaultConfidence) {
        this.displayName = displayName;
        this.defaultConfidence = defaultConfidence;
    }

    public String getDisplayName() {
        return displayName;
    }

    public double getDefaultConfidence() {
        return defaultConfidence;
    }
}