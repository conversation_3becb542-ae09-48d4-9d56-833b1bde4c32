package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class PortalFrameDetector extends PatternDetector {
    private static final Direction[] HORIZONTAL_DIRECTIONS = {
        Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST
    };

    private static final int MIN_PORTAL_HEIGHT = 3;
    private static final int MAX_PORTAL_HEIGHT = 23;
    private static final int MIN_PORTAL_WIDTH = 2;
    private static final int MAX_PORTAL_WIDTH = 21;

    public PortalFrameDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Direction frameDir = findPortalFrameDirection(pos);
                    if (frameDir != null) {
                        PortalInfo portalInfo = analyzePortalFrame(pos, frameDir);
                        if (portalInfo != null && !isAlreadyDetected(results, portalInfo.center)) {
                            results.add(new DetectionResult(
                                portalInfo.isActive ? "Active nether portal frame" : "Inactive nether portal frame",
                                portalInfo.center,
                                new Vec3d(portalInfo.center.getX() + 0.5, portalInfo.center.getY() + 0.5, portalInfo.center.getZ() + 0.5),
                                DetectionResult.DetectionType.STRUCTURE,
                                portalInfo.confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private Direction findPortalFrameDirection(BlockPos start) {
        if (!isObsidian(start)) return null;

        for (Direction dir : HORIZONTAL_DIRECTIONS) {
            if (isPortalCorner(start, dir)) {
                return dir;
            }
        }
        return null;
    }

    private boolean isPortalCorner(BlockPos pos, Direction direction) {
        // Check if this could be a bottom corner of a portal frame
        if (!isObsidian(pos)) return false;
        if (!isObsidian(pos.up())) return false;
        
        Direction perpendicular = direction.rotateYClockwise();
        return isObsidian(pos.offset(direction)) && isObsidian(pos.offset(perpendicular));
    }

    private PortalInfo analyzePortalFrame(BlockPos corner, Direction direction) {
        // Find portal dimensions
        Direction perpendicular = direction.rotateYClockwise();
        int height = 0;
        int width = 0;
        boolean isActive = false;
        BlockPos current = corner;

        // Measure height
        while (height < MAX_PORTAL_HEIGHT && isObsidian(current.up(height))) {
            height++;
        }

        // Measure width
        while (width < MAX_PORTAL_WIDTH && isObsidian(current.offset(perpendicular, width))) {
            width++;
        }

        // Check if valid portal dimensions
        if (height < MIN_PORTAL_HEIGHT || width < MIN_PORTAL_WIDTH) {
            return null;
        }

        // Calculate center
        BlockPos center = corner.up(height / 2).offset(perpendicular, width / 2);

        // Check for active portal blocks
        for (int h = 1; h < height; h++) {
            for (int w = 1; w < width; w++) {
                BlockPos inner = corner.up(h).offset(perpendicular, w);
                if (isPortalBlock(inner)) {
                    isActive = true;
                    break;
                }
            }
            if (isActive) break;
        }

        float confidence = calculateConfidence(height, width, isActive);
        return new PortalInfo(center, height, width, isActive, confidence);
    }

    private float calculateConfidence(int height, int width, boolean isActive) {
        float confidence = 0.6f; // Base confidence for valid frame dimensions

        // Add confidence for reasonable portal sizes
        if (height >= 3 && height <= 7 && width >= 2 && width <= 5) {
            confidence += 0.2f;
        }

        // Active portals are more likely to be player-made
        if (isActive) {
            confidence += 0.2f;
        }

        return Math.min(confidence, 1.0f);
    }

    private boolean isObsidian(BlockPos pos) {
        return world.getBlockState(pos).getBlock() == Blocks.OBSIDIAN;
    }

    private boolean isPortalBlock(BlockPos pos) {
        return world.getBlockState(pos).getBlock() == Blocks.NETHER_PORTAL;
    }

    private boolean isAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (result.getPosition().getSquaredDistance(pos) < 16) { // Within 4 blocks
                return true;
            }
        }
        return false;
    }

    private static class PortalInfo {
        final BlockPos center;
        final int height;
        final int width;
        final boolean isActive;
        final float confidence;

        PortalInfo(BlockPos center, int height, int width, boolean isActive, float confidence) {
            this.center = center;
            this.height = height;
            this.width = width;
            this.isActive = isActive;
            this.confidence = confidence;
        }
    }
}
