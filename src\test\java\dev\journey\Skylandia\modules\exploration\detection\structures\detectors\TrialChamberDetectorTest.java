package dev.journey.Skylandia.modules.exploration.detection.structures.detectors;

import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class TrialChamberDetectorTest {

    @Mock
    private World world;

    @Mock
    private Chunk chunk;

    private TrialChamberDetector detector;
    private static final double CONFIDENCE_THRESHOLD = 0.8;
    private static final int MAX_SEARCH_RADIUS = 48;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        detector = new TrialChamberDetector(world, CONFIDENCE_THRESHOLD, MAX_SEARCH_RADIUS);
        
        // Setup default chunk position
        ChunkPos chunkPos = new ChunkPos(0, 0);
        when(chunk.getPos()).thenReturn(chunkPos);
    }

    @Test
    void getStructureType_ReturnsTrialChamber() {
        assertEquals(StructureType.TRIAL_CHAMBER, detector.getStructureType());
    }

    @Test
    void detectInChunk_PerfectPattern_ReturnsHighConfidenceMatch() {
        // Setup perfect trial chamber entrance pattern
        mockTrialChamberPattern(new BlockPos(7, 40, 7), 1.0);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertFalse(matches.isEmpty());
        StructureMatch match = matches.get(0);
        assertEquals(1.0, match.confidence());
        assertEquals(new BlockPos(7, 40, 7), match.center());
        assertEquals(StructureType.TRIAL_CHAMBER, match.type());
    }

    @Test
    void detectInChunk_PartialPattern_ReturnsLowerConfidenceMatch() {
        // Setup partial trial chamber pattern (80% complete)
        mockTrialChamberPattern(new BlockPos(7, 30, 7), 0.85);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertFalse(matches.isEmpty());
        StructureMatch match = matches.get(0);
        assertTrue(match.confidence() >= CONFIDENCE_THRESHOLD);
        assertTrue(match.confidence() < 1.0);
    }

    @Test
    void detectInChunk_NoPattern_ReturnsEmptyList() {
        // Mock random blocks that don't form a trial chamber pattern
        mockRandomBlocks();

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertTrue(matches.isEmpty());
    }

    @Test
    void detectInChunk_MultiplePatterns_ReturnsAllMatches() {
        // Setup two trial chamber patterns in different locations
        mockTrialChamberPattern(new BlockPos(7, 40, 7), 0.9);
        mockTrialChamberPattern(new BlockPos(7, 60, 7), 0.95);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertEquals(2, matches.size());
        assertTrue(matches.stream().allMatch(match -> match.confidence() >= CONFIDENCE_THRESHOLD));
    }

    private void mockTrialChamberPattern(BlockPos center, double completeness) {
        // Mock the copper block pattern for trial chamber entrance
        for (int y = 0; y < 2; y++) {
            for (int x = -1; x <= 1; x++) {
                for (int z = -1; z <= 1; z++) {
                    if (Math.random() < completeness) {  // Randomly skip some blocks based on completeness
                        BlockPos pos = center.add(x, y, z);
                        BlockState state = y == 0 ? 
                            Blocks.COPPER_BLOCK.getDefaultState() : 
                            Blocks.TUFF.getDefaultState();
                        when(world.getBlockState(pos)).thenReturn(state);
                    }
                }
            }
        }
    }

    private void mockRandomBlocks() {
        // Mock random non-trial chamber blocks
        for (int x = 0; x < 16; x++) {
            for (int z = 0; z < 16; z++) {
                for (int y = 0; y < 5; y++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    when(world.getBlockState(pos)).thenReturn(Blocks.STONE.getDefaultState());
                }
            }
        }
    }
}