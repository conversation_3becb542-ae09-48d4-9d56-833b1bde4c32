package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.passive.PassiveEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.screen.slot.SlotActionType;

import java.util.Set;
import java.util.List;
import java.util.ArrayList;


public class TridentAura extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTargeting = settings.createGroup("Targeting");
    private final SettingGroup sgAiming = settings.createGroup("Aiming");
    private final SettingGroup sgTiming = settings.createGroup("Timing");
    private final SettingGroup sgInventory = settings.createGroup("Inventory");

    // General Settings
    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
            .name("Range")
            .description("Maximum targeting range")
            .defaultValue(50)
            .min(1)
            .sliderMax(100)
            .build()
    );

    private final Setting<Boolean> requireUseKey = sgGeneral.add(new BoolSetting.Builder()
            .name("Require Use Key")
            .description("Only attack when holding the use key")
            .defaultValue(true)
            .build()
    );

    // Targeting Settings
    private final Setting<TargetMode> targetMode = sgTargeting.add(new EnumSetting.Builder<TargetMode>()
            .name("Target Mode")
            .description("What entities to target")
            .defaultValue(TargetMode.HostileOnly)
            .build()
    );

    private final Setting<Set<EntityType<?>>> entities = sgTargeting.add(new EntityTypeListSetting.Builder()
            .name("Entities")
            .description("Select specific entities to target")
            .visible(() -> targetMode.get() == TargetMode.Custom)
            .defaultValue(EntityType.ZOMBIE, EntityType.SKELETON, EntityType.CREEPER)
            .build()
    );

    private final Setting<TargetPriority> targetPriority = sgTargeting.add(new EnumSetting.Builder<TargetPriority>()
            .name("Target Priority")
            .description("How to prioritize targets")
            .defaultValue(TargetPriority.Closest)
            .build()
    );

    private final Setting<Boolean> targetPlayers = sgTargeting.add(new BoolSetting.Builder()
            .name("Target Players")
            .description("Target other players")
            .defaultValue(false)
            .visible(() -> targetMode.get() == TargetMode.All || targetMode.get() == TargetMode.Custom)
            .build()
    );

    private final Setting<Boolean> ignoreBabies = sgTargeting.add(new BoolSetting.Builder()
            .name("Ignore Babies")
            .description("Don't target baby mobs")
            .defaultValue(true)
            .build()
    );

    // Aiming Settings
    private final Setting<Double> aimSmoothness = sgAiming.add(new DoubleSetting.Builder()
            .name("Aim Smoothness")
            .description("How smoothly to aim at targets (0.1 = slow, 1.0 = instant)")
            .defaultValue(0.3)
            .min(0.1)
            .max(1.0)
            .sliderMax(1.0)
            .build()
    );

    private final Setting<Boolean> predictMovement = sgAiming.add(new BoolSetting.Builder()
            .name("Predict Movement")
            .description("Try to predict where moving targets will be")
            .defaultValue(true)
            .build()
    );

    private final Setting<Double> predictionMultiplier = sgAiming.add(new DoubleSetting.Builder()
            .name("Prediction Multiplier")
            .description("How much to predict target movement")
            .defaultValue(1.0)
            .min(0.1)
            .max(3.0)
            .sliderMax(3.0)
            .visible(predictMovement::get)
            .build()
    );

    private final Setting<Boolean> dropCompensation = sgAiming.add(new BoolSetting.Builder()
            .name("Drop Compensation")
            .description("Compensate for trident drop over distance")
            .defaultValue(true)
            .build()
    );

    // Timing Settings
    private final Setting<Integer> chargeTime = sgTiming.add(new IntSetting.Builder()
            .name("Charge Time")
            .description("How long to charge trident before throwing (ticks)")
            .defaultValue(10)
            .min(1)
            .max(100)
            .sliderMax(50)
            .build()
    );

    private final Setting<Integer> cooldownTicks = sgTiming.add(new IntSetting.Builder()
            .name("Cooldown")
            .description("Delay between throws (ticks)")
            .defaultValue(2)
            .min(0)
            .max(20)
            .sliderMax(20)
            .build()
    );

    // Multi-Trident Settings
    private final Setting<Boolean> multiTrident = sgTiming.add(new BoolSetting.Builder()
            .name("Multi-Trident")
            .description("Throw multiple tridents in rapid succession")
            .defaultValue(false)
            .build()
    );

    private final Setting<Integer> tridentCount = sgTiming.add(new IntSetting.Builder()
            .name("Trident Count")
            .description("Number of tridents to throw per burst")
            .defaultValue(3)
            .min(1)
            .max(9)
            .sliderMax(9)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Integer> burstDelay = sgTiming.add(new IntSetting.Builder()
            .name("Burst Delay")
            .description("Delay between each trident in a burst (ticks)")
            .defaultValue(1)
            .min(0)
            .max(10)
            .sliderMax(10)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Integer> burstCooldown = sgTiming.add(new IntSetting.Builder()
            .name("Burst Cooldown")
            .description("Cooldown between bursts (ticks)")
            .defaultValue(20)
            .min(5)
            .max(100)
            .sliderMax(100)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Boolean> rapidFire = sgTiming.add(new BoolSetting.Builder()
            .name("Rapid Fire")
            .description("Skip charge time for multi-trident (faster but less damage)")
            .defaultValue(true)
            .visible(multiTrident::get)
            .build()
    );

    private final Setting<Boolean> autoRefill = sgInventory.add(new BoolSetting.Builder()
            .name("Auto Refill")
            .description("Automatically refill hotbar with tridents during burst")
            .defaultValue(true)
            .visible(multiTrident::get)
            .build()
    );

    // Trident Dupe Integration Settings
    private final Setting<Boolean> autoDupe = sgInventory.add(new BoolSetting.Builder()
            .name("Auto Dupe")
            .description("Automatically dupe tridents when running low")
            .defaultValue(false)
            .build()
    );

    private final Setting<Integer> minTridentCount = sgInventory.add(new IntSetting.Builder()
            .name("Min Trident Count")
            .description("Minimum number of tridents before triggering auto-dupe")
            .defaultValue(3)
            .min(1)
            .max(9)
            .sliderMax(9)
            .visible(autoDupe::get)
            .build()
    );

    private final Setting<Integer> targetTridentCount = sgInventory.add(new IntSetting.Builder()
            .name("Target Trident Count")
            .description("Target number of tridents to dupe to")
            .defaultValue(9)
            .min(2)
            .max(36)
            .sliderMax(36)
            .visible(autoDupe::get)
            .build()
    );

    private final Setting<Double> dupeDelay = sgInventory.add(new DoubleSetting.Builder()
            .name("Dupe Delay")
            .description("Delay between dupe attempts (higher = more stable)")
            .defaultValue(5.0)
            .min(1.0)
            .max(20.0)
            .sliderMax(20.0)
            .visible(autoDupe::get)
            .build()
    );

    private final Setting<Boolean> dupeDurabilityManagement = sgInventory.add(new BoolSetting.Builder()
            .name("Dupe Best Durability")
            .description("Dupe the trident with the best durability")
            .defaultValue(true)
            .visible(autoDupe::get)
            .build()
    );

    // Inventory Settings
    private final Setting<Boolean> autoSwitchSlot = sgInventory.add(new BoolSetting.Builder()
            .name("Auto Switch Slot")
            .description("Automatically switch to trident slot")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> pullFromInventory = sgInventory.add(new BoolSetting.Builder()
            .name("Pull From Inventory")
            .description("Pull tridents from inventory to hotbar")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> preferEnchanted = sgInventory.add(new BoolSetting.Builder()
            .name("Prefer Enchanted")
            .description("Prefer enchanted tridents over unenchanted ones")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> preferHigherDurability = sgInventory.add(new BoolSetting.Builder()
            .name("Prefer Higher Durability")
            .description("Prefer tridents with higher durability")
            .defaultValue(false)
            .build()
    );

    // Instance variables
    private Entity currentTarget = null;
    private int currentCooldownTicks = 0;
    private int lastTargetSwitchTick = 0;

    // Multi-trident variables
    private boolean isBursting = false;
    private int burstTridentsThrown = 0;
    private int burstDelayTicks = 0;
    private int burstCooldownTicks = 0;
    private int originalSelectedSlot = -1;

    // Dupe integration variables
    private boolean isDuping = false;
    private boolean dupeCancel = true;
    private final List<TimedTask> dupeScheduledTasks = new ArrayList<>();
    private int lastTridentCount = 0;
    private int dupeCheckCooldown = 0;

    public TridentAura() {
        super(Skylandia.Automation, "Trident Aura", "Enhanced trident combat with entity targeting and customizable settings");
    }

    @Override
    public void onDeactivate() {
        currentTarget = null;
        currentCooldownTicks = 0;
        resetBurstState();
    }

    private void resetBurstState() {
        isBursting = false;
        burstTridentsThrown = 0;
        burstDelayTicks = 0;
        burstCooldownTicks = 0;
        originalSelectedSlot = -1;
        resetDupeState();
    }

    private void resetDupeState() {
        isDuping = false;
        dupeCancel = true;
        dupeScheduledTasks.clear();
        dupeCheckCooldown = 0;
    }

    // Helper class for dupe timing
    private static class TimedTask {
        private final long executeTime;
        private final Runnable task;

        TimedTask(long executeTime, Runnable task) {
            this.executeTime = executeTime;
            this.task = task;
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        // Process dupe scheduled tasks
        processDupeTasks();

        // Check if use key is required and pressed
        if (requireUseKey.get() && !mc.options.useKey.isPressed()) {
            currentTarget = null;
            currentCooldownTicks = 0;
            resetBurstState();
            return;
        }

        // Handle auto-dupe logic
        if (autoDupe.get() && !isDuping) {
            handleAutoDupeCheck();
        }

        // Don't do combat actions while duping
        if (isDuping) return;

        // Handle multi-trident burst logic
        if (multiTrident.get()) {
            handleMultiTridentLogic();
            return;
        }

        // Handle regular single trident logic
        handleSingleTridentLogic();
    }

    private void handleSingleTridentLogic() {
        // Handle cooldown
        if (currentCooldownTicks > 0) {
            currentCooldownTicks--;
            return;
        }

        // Find target
        findTarget();

        if (currentTarget == null) return;

        // Handle trident in hand
        if (!handleTridentEquipping()) return;

        // Aim at target
        aimAtTarget();

        // Handle throwing
        handleThrowing();
    }

    private void processDupeTasks() {
        long currentTime = System.currentTimeMillis();
        dupeScheduledTasks.removeIf(task -> {
            if (task.executeTime <= currentTime) {
                task.task.run();
                return true;
            }
            return false;
        });
    }

    private void handleAutoDupeCheck() {
        // Cooldown between checks
        if (dupeCheckCooldown > 0) {
            dupeCheckCooldown--;
            return;
        }

        int currentTridentCount = countTridents();

        // Check if we need to dupe
        if (currentTridentCount < minTridentCount.get()) {
            info("Low trident count (" + currentTridentCount + "), starting auto-dupe...");
            startAutoDupe();
            dupeCheckCooldown = 100; // 5 second cooldown
        } else {
            dupeCheckCooldown = 20; // 1 second between checks
        }
    }

    private int countTridents() {
        int count = 0;
        // Count tridents in hotbar
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                count++;
            }
        }
        // Count tridents in inventory
        for (int i = 9; i < 36; i++) {
            if (mc.player.getInventory().getStack(i).isOf(Items.TRIDENT)) {
                count++;
            }
        }
        return count;
    }

    private void startAutoDupe() {
        if (isDuping) return;

        isDuping = true;
        dupeCancel = true;

        // Find best trident to dupe
        int bestSlot = findBestTridentForDupe();
        if (bestSlot == -1) {
            error("No suitable trident found for duping");
            isDuping = false;
            return;
        }

        // Switch to the best trident
        mc.player.getInventory().selectedSlot = bestSlot;

        // Start dupe process
        performTridentDupe();
    }

    private int findBestTridentForDupe() {
        int bestSlot = -1;
        int bestDurability = Integer.MAX_VALUE;

        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.isOf(Items.TRIDENT)) {
                if (dupeDurabilityManagement.get()) {
                    // Find trident with best (lowest) damage
                    int damage = stack.getDamage();
                    if (damage < bestDurability) {
                        bestDurability = damage;
                        bestSlot = i;
                    }
                } else {
                    // Just use first trident found
                    return i;
                }
            }
        }

        return bestSlot;
    }

    private void handleMultiTridentLogic() {
        // Handle burst cooldown
        if (burstCooldownTicks > 0) {
            burstCooldownTicks--;
            return;
        }

        // Find target if not bursting
        if (!isBursting) {
            findTarget();
            if (currentTarget == null) return;

            // Start burst
            isBursting = true;
            burstTridentsThrown = 0;
            burstDelayTicks = 0;
            originalSelectedSlot = mc.player.getInventory().selectedSlot;
        }

        // Handle burst delay
        if (burstDelayTicks > 0) {
            burstDelayTicks--;
            return;
        }

        // Check if burst is complete
        if (burstTridentsThrown >= tridentCount.get()) {
            // End burst
            isBursting = false;
            burstCooldownTicks = burstCooldown.get();

            // Restore original slot if possible
            if (originalSelectedSlot != -1) {
                mc.player.getInventory().selectedSlot = originalSelectedSlot;
                originalSelectedSlot = -1;
            }
            return;
        }

        // Aim at target
        aimAtTarget();

        // Find and equip next trident
        if (!handleMultiTridentEquipping()) {
            // No more tridents available, end burst
            isBursting = false;
            burstCooldownTicks = burstCooldown.get();
            return;
        }

        // Throw trident
        if (handleMultiTridentThrowing()) {
            burstTridentsThrown++;
            burstDelayTicks = burstDelay.get();
        }
    }

    private void findTarget() {
        double closestDistance = range.get();
        Entity bestTarget = null;
        double bestScore = Double.MAX_VALUE;

        for (Entity entity : mc.world.getEntities()) {
            if (!isValidTarget(entity)) continue;

            double distance = mc.player.distanceTo(entity);
            if (distance > range.get()) continue;

            double score = calculateTargetScore(entity, distance);
            if (score < bestScore) {
                bestScore = score;
                bestTarget = entity;
                closestDistance = distance;
            }
        }

        // Only switch target if significantly better or current target is invalid
        if (bestTarget != currentTarget) {
            if (currentTarget == null || !isValidTarget(currentTarget) || 
                mc.player.distanceTo(currentTarget) > range.get() ||
                (mc.world.getTime() - lastTargetSwitchTick > 20)) { // Min 1 second between switches
                
                currentTarget = bestTarget;
                lastTargetSwitchTick = (int) mc.world.getTime();
            }
        }
    }

    private boolean isValidTarget(Entity entity) {
        if (!(entity instanceof LivingEntity)) return false;
        if (entity == mc.player) return false;
        if (!entity.isAlive()) return false;
        
        // Check baby mobs
        if (ignoreBabies.get() && entity instanceof LivingEntity living && living.isBaby()) {
            return false;
        }

        // Check target mode
        switch (targetMode.get()) {
            case HostileOnly:
                return entity instanceof HostileEntity;
            case PassiveOnly:
                return entity instanceof PassiveEntity;
            case Players:
                return entity instanceof PlayerEntity && targetPlayers.get();
            case All:
                if (entity instanceof PlayerEntity) return targetPlayers.get();
                return true;
            case Custom:
                if (entity instanceof PlayerEntity) return targetPlayers.get();
                return entities.get().contains(entity.getType());
        }
        return false;
    }

    private double calculateTargetScore(Entity entity, double distance) {
        double score = distance; // Base score on distance

        switch (targetPriority.get()) {
            case Closest:
                return distance;
            case LowestHealth:
                if (entity instanceof LivingEntity living) {
                    return living.getHealth();
                }
                return distance;
            case HighestHealth:
                if (entity instanceof LivingEntity living) {
                    return 100 - living.getHealth(); // Invert health for priority
                }
                return distance;
            case Players:
                if (entity instanceof PlayerEntity) {
                    return distance * 0.5; // Prioritize players
                }
                return distance * 2; // Deprioritize non-players
        }
        return score;
    }

    private boolean handleTridentEquipping() {
        ItemStack mainHand = mc.player.getMainHandStack();
        
        if (mainHand.isOf(Items.TRIDENT)) {
            return true; // Already holding trident
        }

        if (!autoSwitchSlot.get()) return false;

        // Find trident in hotbar
        int bestSlot = findBestTridentSlot(false);
        if (bestSlot != -1) {
            mc.player.getInventory().selectedSlot = bestSlot;
            return true;
        }

        // Pull from inventory if enabled
        if (pullFromInventory.get()) {
            return pullTridentFromInventory();
        }

        return false;
    }

    private int findBestTridentSlot(boolean includeInventory) {
        int bestSlot = -1;
        int bestScore = -1;
        
        int start = includeInventory ? 0 : 0;
        int end = includeInventory ? mc.player.getInventory().size() : 9;

        for (int i = start; i < end; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (!stack.isOf(Items.TRIDENT)) continue;

            int score = calculateTridentScore(stack);
            if (score > bestScore) {
                bestScore = score;
                bestSlot = i;
            }
        }
        
        return bestSlot;
    }

    private int calculateTridentScore(ItemStack stack) {
        int score = 0;
        
        // Prefer enchanted tridents
        if (preferEnchanted.get() && !stack.getEnchantments().isEmpty()) {
            score += 100;
        }
        
        // Consider durability
        if (preferHigherDurability.get() && stack.isDamageable()) {
            score += stack.getMaxDamage() - stack.getDamage();
        }
        
        return score;
    }

    private boolean pullTridentFromInventory() {
        int bestInventorySlot = findBestTridentSlot(true);
        if (bestInventorySlot == -1 || bestInventorySlot < 9) return false;

        // Find empty hotbar slot
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getStack(i).isEmpty()) {
                InvUtils.move().from(bestInventorySlot).to(i);
                mc.player.getInventory().selectedSlot = i;
                return true;
            }
        }
        
        return false;
    }

    private boolean handleMultiTridentEquipping() {
        // Check if current slot has trident
        ItemStack mainHand = mc.player.getMainHandStack();
        if (mainHand.isOf(Items.TRIDENT)) {
            return true;
        }

        // Find next available trident in hotbar
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.isOf(Items.TRIDENT)) {
                mc.player.getInventory().selectedSlot = i;
                return true;
            }
        }

        // Auto-refill hotbar with tridents if enabled
        if (autoRefill.get() && pullFromInventory.get()) {
            if (pullTridentFromInventory()) {
                return true;
            }
        }

        // If auto-dupe is enabled and we're low on tridents, trigger dupe
        if (autoDupe.get() && !isDuping && countTridents() < minTridentCount.get()) {
            info("Multi-trident mode triggered auto-dupe due to low trident count");
            startAutoDupe();
        }

        return false;
    }

    private boolean handleMultiTridentThrowing() {
        if (!mc.player.getMainHandStack().isOf(Items.TRIDENT)) return false;

        if (rapidFire.get()) {
            // Rapid fire mode - instant throws
            if (!mc.player.isUsingItem()) {
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            }

            // Immediately release for rapid fire
            if (mc.player.isUsingItem()) {
                mc.interactionManager.stopUsingItem(mc.player);
                return true;
            }
        } else {
            // Charged mode - use charge time
            if (!mc.player.isUsingItem()) {
                mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            } else if (mc.player.getItemUseTime() >= chargeTime.get()) {
                mc.interactionManager.stopUsingItem(mc.player);
                return true;
            }
        }

        return false;
    }

    private void performTridentDupe() {
        int delayMs = (int) (dupeDelay.get() * 100);
        int currentCount = countTridents();

        // Check if we've reached target count
        if (currentCount >= targetTridentCount.get()) {
            info("Reached target trident count (" + currentCount + "), stopping dupe");
            isDuping = false;
            return;
        }

        // Start the dupe sequence
        mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        dupeCancel = true;

        scheduleDupeTask(() -> {
            dupeCancel = false;

            // Perform the dupe sequence (based on TridentDupe module)
            if (mc.player.currentScreenHandler != null) {
                // Swap to offhand
                mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, 3, 0, SlotActionType.SWAP, mc.player);

                // Send release packet
                PlayerActionC2SPacket packet = new PlayerActionC2SPacket(
                    PlayerActionC2SPacket.Action.RELEASE_USE_ITEM,
                    BlockPos.ORIGIN,
                    Direction.DOWN,
                    0
                );
                mc.getNetworkHandler().sendPacket(packet);
            }

            dupeCancel = true;

            // Schedule next dupe attempt
            scheduleDupeTask(() -> {
                if (isDuping && countTridents() < targetTridentCount.get()) {
                    performTridentDupe();
                } else {
                    isDuping = false;
                    info("Auto-dupe completed. Total tridents: " + countTridents());
                }
            }, delayMs);
        }, delayMs);
    }

    private void scheduleDupeTask(Runnable task, long delayMs) {
        dupeScheduledTasks.add(new TimedTask(System.currentTimeMillis() + delayMs, task));
    }

    private void aimAtTarget() {
        if (currentTarget == null) return;

        // Get target position
        double targetX = currentTarget.getX();
        double targetY = currentTarget.getY() + currentTarget.getHeight() / 2.0;
        double targetZ = currentTarget.getZ();

        // Apply movement prediction
        if (predictMovement.get() && currentTarget instanceof LivingEntity) {
            double predictionTime = mc.player.distanceTo(currentTarget) / 20.0; // Rough prediction time
            targetX += currentTarget.getVelocity().x * predictionTime * predictionMultiplier.get();
            targetY += currentTarget.getVelocity().y * predictionTime * predictionMultiplier.get();
            targetZ += currentTarget.getVelocity().z * predictionTime * predictionMultiplier.get();
        }

        // Calculate aim angles
        double dx = targetX - mc.player.getX();
        double dz = targetZ - mc.player.getZ();
        double dy = targetY - (mc.player.getY() + mc.player.getEyeHeight(mc.player.getPose()));
        
        double horizontalDist = Math.sqrt(dx * dx + dz * dz);
        
        // Apply drop compensation
        if (dropCompensation.get()) {
            double dropComp = 0.05 * Math.pow(horizontalDist / 2.5, 2);
            dy += dropComp;
        }

        float targetYaw = (float) Math.toDegrees(Math.atan2(dz, dx)) - 90f;
        float targetPitch = (float) -Math.toDegrees(Math.atan2(dy, horizontalDist));
        
        // Apply smoothing
        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();
        
        float smoothness = aimSmoothness.get().floatValue();
        float smoothYaw = currentYaw + (targetYaw - currentYaw) * smoothness;
        float smoothPitch = currentPitch + (targetPitch - currentPitch) * smoothness;

        mc.player.setYaw(smoothYaw);
        mc.player.setPitch(smoothPitch);
    }

    private void handleThrowing() {
        if (!mc.player.getMainHandStack().isOf(Items.TRIDENT)) return;

        if (!mc.player.isUsingItem()) {
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        } else if (mc.player.getItemUseTime() >= chargeTime.get()) {
            mc.interactionManager.stopUsingItem(mc.player);
            currentCooldownTicks = cooldownTicks.get();
        }
    }

    // Enums for settings
    public enum TargetMode {
        HostileOnly("Hostile Only"),
        PassiveOnly("Passive Only"),
        Players("Players Only"),
        All("All Entities"),
        Custom("Custom List");

        private final String name;

        TargetMode(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum TargetPriority {
        Closest("Closest"),
        LowestHealth("Lowest Health"),
        HighestHealth("Highest Health"),
        Players("Players First");

        private final String name;

        TargetPriority(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }
}