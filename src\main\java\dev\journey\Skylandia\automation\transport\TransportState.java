package dev.journey.Skylandia.automation.transport;

/**
 * Represents the possible states of the transport system
 */
public enum TransportState {
    /** System is inactive */
    IDLE("Idle"),
    
    /** Collecting items from source container */
    COLLECTING("Collecting Items"),
    
    /** Waiting for teleport command response */
    WAITING_TELEPORT("Waiting for Teleport"),
    
    /** Throwing ender pearl for transport */
    THROWING_PEARL("Throwing Pearl"),
    
    /** Dropping items at destination */
    DROPPING("Dropping Items"),
    
    /** Returning to original position */
    RETURNING("Returning to Origin"),
    
    /** Validating configuration and requirements */
    VALIDATING("Validating Configuration"),
    
    /** Awaiting counterpart readiness */
    WAITING_READY("Waiting for Ready"),
    
    /** Checking inventories */
    CHECKING_INVENTORY("Checking Inventory"),
    
    /** Preparing for pearl throw */
    PREPARING_PEARL("Preparing Pearl"),
    
    /** <PERSON> has been thrown */
    PEARL_THROWN("Pearl Thrown"),
    
    /** Preparing stasis chamber */
    PREPARING_CHAMBER("Preparing Chamber"),
    
    /** Chamber is active */
    CHAMBER_ACTIVE("Chamber Active"),
    
    /** Executing teleport */
    TELEPORTING("Teleporting"),
    
    /** Transferring items */
    TRANSFERRING("Transferring Items"),
    
    /** Awaiting manual confirmation */
    AWAITING_CONFIRMATION("Awaiting Confirmation"),
    
    /** Rolling back from failed state */
    ROLLING_BACK("Rolling Back"),
    
    /** Transport cycle complete */
    COMPLETE("Complete"),
    
    /** Pearl throw failed */
    PEARL_THROW_FAILED("Pearl Throw Failed"),
    
    /** Inventory synchronization error */
    INVENTORY_SYNC_ERROR("Inventory Sync Error"),
    
    /** Teleport timeout */
    TELEPORT_TIMEOUT("Teleport Timeout"),
    
    /** Stasis chamber error */
    STASIS_ERROR("Stasis Error"),
    
    /** Connection lost */
    CONNECTION_LOST("Connection Lost"),
    
    /** Generic error state */
    ERROR("Error");

    private final String displayName;

    TransportState(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Get whether this state represents an active operation
     */
    public boolean isActive() {
        return this != IDLE && this != COMPLETE && this != ERROR;
    }

    /**
     * Get whether this state represents a failure condition
     */
    public boolean isFailure() {
        return this == ERROR || this == ROLLING_BACK;
    }

    /**
     * Get whether this state can be safely interrupted
     */
    public boolean isInterruptible() {
        return this != TELEPORTING && 
               this != CHAMBER_ACTIVE && 
               this != PEARL_THROWN;
    }

    /**
     * Get whether this state requires the player to be in a specific dimension
     */
    public boolean requiresSpecificDimension() {
        return this != IDLE && 
               this != ERROR && 
               this != COMPLETE;
    }

    /**
     * Get whether this state requires secure communication
     */
    public boolean requiresCoordination() {
        return this.isActive() && 
               this != VALIDATING && 
               this != CHECKING_INVENTORY;
    }

    /**
     * Get a user-friendly name for this state
     */
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}