package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IClientPlayerInteractionManager;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.network.MeteorExecutor;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.*;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.entity.decoration.ItemFrameEntity;
import net.minecraft.item.Item;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class ItemFrameDupe extends Module {
    private static final ArrayList<BlockPos> blocks = new ArrayList<>();
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgPlace = settings.createGroup("Place");
    private final SettingGroup sgBreak = settings.createGroup("Break");
    private final SettingGroup sgAutoScroll = settings.createGroup("Auto Scroll HotBar");
    private final SettingGroup sgTurbo = settings.createGroup("Turbo Mode");
    private final SettingGroup sgStats = settings.createGroup("Statistics");

    // Statistics settings
    private final Setting<Integer> dupedCount = sgStats.add(new IntSetting.Builder()
        .name("duped-count")
        .description("Number of items successfully duped")
        .defaultValue(0)
        .min(0)
        .build()
    );
    
    private final Setting<Integer> attemptCount = sgStats.add(new IntSetting.Builder()
        .name("attempt-count")
        .description("Number of dupe attempts made")
        .defaultValue(0)
        .min(0)
        .build()
    );

    private final Setting<Double> successRate = sgStats.add(new DoubleSetting.Builder()
        .name("success-rate")
        .description("Percentage of successful dupes")
        .defaultValue(0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );

    private final Setting<Boolean> displayStats = sgStats.add(new BoolSetting.Builder()
        .name("display-stats")
        .description("Show duping statistics in HUD")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> resetStatsOnDisable = sgStats.add(new BoolSetting.Builder()
        .name("reset-on-disable")
        .description("Reset statistics when module is disabled")
        .defaultValue(false)
        .build()
    );

    // Turbo mode settings
    // Move turboMode to general settings for better visibility
    private final Setting<Boolean> turboMode = sgGeneral.add(new BoolSetting.Builder()
        .name("turbo-mode")
        .description("Enable super fast duping")
        .defaultValue(false)
        .build()
    );

    // Improve setting descriptions and defaults
    private final Setting<Integer> rotationsPerDupe = sgTurbo.add(new IntSetting.Builder()
        .name("rotations")
        .description("Number of rotations per dupe attempt")
        .defaultValue(2)
        .min(1)
        .max(32)
        .sliderMax(10)
        .visible(turboMode::get)
        .build()
    );

    private final Setting<Integer> turboDelay = sgGeneral.add(new IntSetting.Builder()
        .name("turbo-delay")
        .description("Delay between rotations in milliseconds")
        .defaultValue(50)
        .min(10)
        .sliderMax(200)
        .visible(turboMode::get)
        .build()
    );

    private final Setting<Boolean> skipAnimations = sgGeneral.add(new BoolSetting.Builder()
        .name("skip-animations")
        .description("Skip item frame rotation animations in turbo mode")
        .defaultValue(false)
        .visible(turboMode::get)
        .build()
    );

    private final Setting<Integer> interactionLimit = sgGeneral.add(new IntSetting.Builder()
        .name("interaction-limit")
        .description("Maximum interactions per tick in turbo mode")
        .defaultValue(4)
        .min(1)
        .sliderMax(10)
        .visible(turboMode::get)
        .build()
    );

    private final Setting<Integer> maxItemFrames = sgGeneral.add(new IntSetting.Builder()
        .name("max-frames")
        .description("Maximum number of item frames to use at once")
        .defaultValue(1)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Integer> distance = sgGeneral.add(new IntSetting.Builder()
        .name("distance")
        .description("The max distance to search for pistons.")
        .min(1)
        .sliderMin(1)
        .defaultValue(5)
        .sliderMax(6)
        .max(6)
        .build()
    );

    private final Setting<Boolean> rotate = sgGeneral.add(new BoolSetting.Builder()
        .name("rotate")
        .description("Whether or not to rotate when placing.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> rotateItem = sgGeneral.add(new BoolSetting.Builder()
        .name("rotate item")
        .description("Whether or not to keep rotating the item frame")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> swapBack = sgGeneral.add(new BoolSetting.Builder()
        .name("swap-back")
        .description("Whether or not to swap back to the previous held item after placing.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> placeItemFrame = sgPlace.add(new BoolSetting.Builder()
        .name("place-item-frame")
        .description("Whether or not to place an item frame first (needs itemframes in hotbar)")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> placeDelay = sgPlace.add(new IntSetting.Builder()
        .name("place-delay")
        .description("The place delay between placements and breaking")
        .defaultValue(1)
        .sliderMin(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Boolean> multiBreak = sgBreak.add(new BoolSetting.Builder()
        .name("multi-break")
        .description("Attempts to rotate and break multiple item frames.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> breakDelay = sgBreak.add(new IntSetting.Builder()
        .name("item-break-delay")
        .description("The amount of delay between breaking the item in ticks.")
        .defaultValue(2)
        .min(0)
        .sliderMax(60)
        .build()
    );

    private final Setting<Boolean> autoScroll = sgAutoScroll.add(new BoolSetting.Builder()
        .name("auto-scroll-hotbar")
        .description("Will automatically scroll through the hot bar.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> scrollDelay = sgAutoScroll.add(new IntSetting.Builder()
        .name("auto-scroll-delay")
        .description("How many ticks later should we scroll to next hotbar item")
        .defaultValue(3)
        .min(0)
        .sliderMax(10)
        .build()
    );

    private int timer, autoScrollTimer = 0;
    private int breakDelaytimer;
    private int currentRotations = 0;
    private int activeFrameCount = 0;
    private long lastInteractionTime = 0;
    private static final long MIN_INTERACTION_DELAY = 50; // Minimum ms between interactions
    private long lastSuccessTime = 0;
    private static final long LAG_THRESHOLD = 1000; // ms threshold for lag detection
    private int targetRotation = 0;
    
    // Performance tracking
    private double averageAttemptTime = 0;
    private int attemptTimeCount = 0;

    public ItemFrameDupe() {
        super(Skylandia.Automation, "item-frame-dupe", "Automatically places and manipulates item frames to perform item duplication");
    }

    private static List<BlockPos> getSphere(BlockPos centerPos, int radius, int height) {
        blocks.clear();

        for (int i = centerPos.getX() - radius; i < centerPos.getX() + radius; i++) {
            for (int j = centerPos.getY() - height; j < centerPos.getY() + height; j++) {
                for (int k = centerPos.getZ() - radius; k < centerPos.getZ() + radius; k++) {
                    BlockPos pos = new BlockPos(i, j, k);
                    if (centerPos.isWithinDistance(pos, radius) && !blocks.contains(pos)) blocks.add(pos);
                }
            }
        }

        return blocks;
    }

    @Override
    public void onActivate() {
        timer = placeDelay.get();
        autoScrollTimer = scrollDelay.get();
        breakDelaytimer = 0;
        currentRotations = 0;
        targetRotation = 0;
        lastInteractionTime = 0;
        lastSuccessTime = System.currentTimeMillis();
        
        // Reset performance metrics
        if (!turboMode.get() || resetStatsOnDisable.get()) {
            averageAttemptTime = 0;
            attemptTimeCount = 0;
        }
        
        // Enhanced activation messages
        StringBuilder sb = new StringBuilder();
        sb.append("§aItem Frame Dupe activated");
        info(sb.toString());
        
        sb.setLength(0);
        sb.append("§7Mode: ").append(turboMode.get() ? "§bTurbo" : "§7Normal");
        info(sb.toString());
        
        info("§7Settings:");
        
        sb.setLength(0);
        sb.append("§7 - Frames: §b").append(maxItemFrames.get());
        info(sb.toString());
        
        if (turboMode.get()) {
            sb.setLength(0);
            sb.append("§7 - Rotations: §b").append(rotationsPerDupe.get());
            info(sb.toString());
            
            sb.setLength(0);
            sb.append("§7 - Delay: §b").append(turboDelay.get()).append("ms");
            info(sb.toString());
            
            sb.setLength(0);
            sb.append("§7 - Skip Animations: §b").append(skipAnimations.get());
            info(sb.toString());
        }
    }

    @Override
    public void onDeactivate() {
        super.onDeactivate();
        if (resetStatsOnDisable.get()) {
            // Reset all metrics
            dupedCount.set(0);
            attemptCount.set(0);
            successRate.set(0.0);
            averageAttemptTime = 0;
            attemptTimeCount = 0;
        }
        // Always reset operational counters
        breakDelaytimer = 0;
        currentRotations = 0;
        targetRotation = 0;
        lastInteractionTime = 0;
        
        StringBuilder sb = new StringBuilder();
        
        // Debug info
        sb.append("[DEBUG] Starting module deactivation");
        info(sb.toString());
        
        sb.setLength(0);
        sb.append("[DEBUG] Current stats - Duped: ")
          .append(dupedCount.get())
          .append("/")
          .append(attemptCount.get());
        info(sb.toString());
        
        if (attemptCount.get() > 0) {
            sb.setLength(0);
            sb.append("[DEBUG] Success rate: ")
              .append(Math.round((dupedCount.get() * 1000.0) / attemptCount.get()) / 10.0)
              .append(" percent");
            info(sb.toString());
        }

        sb.setLength(0);
        sb.append("[DEBUG] Average attempt time: ")
          .append(Math.round(averageAttemptTime * 10.0) / 10.0)
          .append("ms");
        info(sb.toString());

        info("Item Frame Dupe deactivated");
        
        // Final stats
        if (!resetStatsOnDisable.get() && dupedCount.get() > 0) {
            sb.setLength(0);
            sb.append("Stats - ");
            
            int duped = dupedCount.get();
            int attempts = attemptCount.get();
            sb.append(duped).append(" items duped");
            
            if (attempts > 0) {
                double rate = (duped * 100.0) / attempts;
                sb.append(" (Success rate: ")
                  .append(Math.round(rate * 10.0) / 10.0)
                  .append(" percent)");
            }
            
            if (turboMode.get() && averageAttemptTime > 0) {
                sb.append(" - Avg time: ")
                  .append(Math.round(averageAttemptTime * 10.0) / 10.0)
                  .append("ms");
            }
            
            info(sb.toString());
        }
    }

    @Override
    public String getInfoString() {
        if (!displayStats.get()) return null;
        
        StringBuilder sb = new StringBuilder();
        
        // Mode and basic stats
        sb.append(turboMode.get() ? "§bTurbo" : "§7Normal")
          .append("§r | ");
        
        int duped = dupedCount.get();
        int attempts = attemptCount.get();
        double rate = attempts > 0 ? (duped * 100.0) / attempts : 0;
        
        String color = rate > 75 ? "§a" : rate > 50 ? "§e" : rate > 25 ? "§6" : "§c";
        sb.append(color).append(formatPerformanceStats()).append("§r");
        
        // Frame count
        sb.append(" | §7Frames: ")
          .append(activeFrameCount)
          .append("/")
          .append(maxItemFrames.get());
        
        // Add average time if in turbo mode
        if (turboMode.get() && averageAttemptTime > 0) {
            sb.append(" | §7")
              .append(Math.round(averageAttemptTime * 10.0) / 10.0)
              .append("ms");
        }
        
        return sb.toString();
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (!Utils.canUpdate()) return;

        // Reset frame count at start of tick
        activeFrameCount = 0;

        ClientPlayerInteractionManager c = mc.interactionManager;

        if(autoScroll.get()) {
            if (autoScrollTimer > 0) {
                autoScrollTimer--;
            } else {
                // Find item frame slot to skip
                FindItemResult frameResult = InvUtils.findInHotbar(Items.ITEM_FRAME, Items.GLOW_ITEM_FRAME);
                int frameSlot = frameResult.slot();
                
                int currentSlot = mc.player.getInventory().selectedSlot;
                int nextSlot = (currentSlot + 1) % mc.player.getInventory().getHotbarSize();
                
                // Skip the frame slot if it's the next slot
                if (nextSlot == frameSlot && frameResult.found()) {
                    nextSlot = (nextSlot + 1) % mc.player.getInventory().getHotbarSize();
                }
                
                mc.player.getInventory().setSelectedSlot(nextSlot);
                ((IClientPlayerInteractionManager) mc.interactionManager).meteor$syncSelected();
                autoScrollTimer = scrollDelay.get();
            }
        }

        if (timer > 0) {
            timer--;
            return;
        } else {
            timer = placeDelay.get();
        }

        MeteorExecutor.execute(() -> {
            if (mc.player == null || mc.player.getWorld() == null) return;

            // Check for item frames first
            if (placeItemFrame.get() && !InvUtils.findInHotbar(Items.ITEM_FRAME, Items.GLOW_ITEM_FRAME).found()) {
                error("No item frames found in hotbar.");
                toggle();
                return;
            }

            Box box = new Box(mc.player.getEyePos().add(-3, -3, -3), mc.player.getEyePos().add(3, 3, 3));
            var itemframes = mc.player.getWorld().getEntitiesByClass(ItemFrameEntity.class, box, itemFrameEntity -> true);

            activeFrameCount = itemframes.size();
            
            // If no frames found or below max frames, try to place new ones
            if (placeItemFrame.get() && activeFrameCount < maxItemFrames.get()) {
                FindItemResult itemResult = InvUtils.findInHotbar(Items.ITEM_FRAME, Items.GLOW_ITEM_FRAME);
                if (itemResult.found()) {
                    for (BlockPos blockPos : getSphere(mc.player.getBlockPos(), distance.get(), distance.get() - 1)) {
                        if (shouldPlace(blockPos)) {
                            BlockState blockState = mc.world.getBlockState(blockPos);
                            if (!blockState.isSolidBlock(mc.world, blockPos)) continue;
                            BlockUtils.place(blockPos, itemResult, rotate.get(), 50, true, true, swapBack.get());
                            activeFrameCount++;
                            if (activeFrameCount >= maxItemFrames.get()) break;
                        }
                    }
                }
            }

            if (!itemframes.isEmpty()) {
                for(ItemFrameEntity itemFrame: itemframes) {
                    // Skip if too far or we've hit max frames
                    if(!itemFrame.getBlockPos().isWithinDistance(mc.player.getBlockPos(), mc.player.getEntityInteractionRange()) ||
                       activeFrameCount >= maxItemFrames.get()) continue;
                    
                    activeFrameCount++;

                    Item mainHanditem = mc.player.getMainHandStack().getItem();
                    if (mainHanditem == Items.ITEM_FRAME || mainHanditem == Items.GLOW_ITEM_FRAME) return;

                    c.interactEntity(mc.player, itemFrame, Hand.MAIN_HAND);

                    if (!itemFrame.getHeldItemStack().isEmpty() && itemFrame.getHeldItemStack().isInFrame()) {
                        if (turboMode.get()) {
                            // Check for server lag
                            if (System.currentTimeMillis() - lastSuccessTime > LAG_THRESHOLD) {
                                warning("Possible server lag detected, adjusting timings...");
                                try {
                                    TimeUnit.MILLISECONDS.sleep(LAG_THRESHOLD);
                                } catch (InterruptedException e) {
                                    error("Lag compensation interrupted");
                                }
                            }
                            
                            int targetRotations = rotationsPerDupe.get();
                            // Check interaction limit
                            if (currentRotations < targetRotations * interactionLimit.get()) {
                                try {
                                    long startTime = System.currentTimeMillis();
                                    
                                    // Calculate optimal rotation direction
                                    int currentRot = itemFrame.getRotation();
                                    targetRotation = (currentRot + targetRotations) % 8;
                                    int clockwiseSteps = (targetRotation - currentRot + 8) % 8;
                                    int counterClockwiseSteps = (currentRot - targetRotation + 8) % 8;
                                    boolean rotateClockwise = clockwiseSteps <= counterClockwiseSteps;
                                    
                                    // Skip animations if enabled
                                    if (skipAnimations.get()) {
                                        // Directly set rotation using optimal direction
                                        int newRotation = rotateClockwise
                                            ? (currentRot + 1) % 8
                                            : (currentRot - 1 + 8) % 8;
                                        itemFrame.setRotation(newRotation);
                                    } else {
                                        // Normal interaction
                                        c.interactEntity(mc.player, itemFrame, Hand.MAIN_HAND);
                                    }
                                    currentRotations++;
                                    
                                    if (currentRotations >= targetRotations) {
                                        currentRotations = 0;
                                        // Update performance metrics
                                        long attemptTime = System.currentTimeMillis() - startTime;
                                        averageAttemptTime = (averageAttemptTime * attemptTimeCount + attemptTime) / (attemptTimeCount + 1);
                                        attemptTimeCount++;

                                        StringBuilder msg = new StringBuilder();
                                        msg.append("Sequence complete - ")
                                           .append(dupedCount.get())
                                           .append("/")
                                           .append(attemptCount.get());

                                        if (averageAttemptTime > 0) {
                                            msg.append(" [")
                                                .append(Math.round(averageAttemptTime * 10.0) / 10.0)
                                                .append("ms]");
                                        }

                                        info(msg.toString());
                                    }
                                    
                                    TimeUnit.MILLISECONDS.sleep(turboDelay.get());
                                    lastSuccessTime = System.currentTimeMillis();
                                } catch (InterruptedException e) {
                                    error("Turbo mode interrupted: " + e.getMessage());
                                } catch (Exception e) {
                                    error("Turbo mode error: " + e.getMessage());
                                    toggle();
                                    return;
                                }
                            }
                        } else if (rotateItem.get()) {
                            c.interactEntity(mc.player, itemFrame, Hand.MAIN_HAND);
                        }

                        try {
                            TimeUnit.MILLISECONDS.sleep(600);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }

                        breakDelaytimer++;
                        if (breakDelaytimer > breakDelay.get() && !itemFrame.getHeldItemStack().isEmpty()) {
                            // Rate limiting check
                            long currentTime = System.currentTimeMillis();
                            if (currentTime - lastInteractionTime >= MIN_INTERACTION_DELAY) {
                                c.attackEntity(mc.player, itemFrame);
                                breakDelaytimer = 0;
                                lastInteractionTime = currentTime;
                                
                                // Update statistics
                                attemptCount.set(attemptCount.get() + 1);
                                if (!itemFrame.getHeldItemStack().isEmpty()) {
                                    dupedCount.set(dupedCount.get() + 1);
                                    // Update success rate
                                    if (attemptCount.get() > 0) {
                                        double rate = (dupedCount.get() * 100.0) / attemptCount.get();
                                        StringBuilder msg = new StringBuilder();
                                        msg.append("[DEBUG] Stats - Duped: ")
                                           .append(dupedCount.get())
                                           .append("/")
                                           .append(attemptCount.get());
                                        
                                        if (rate > 0) {
                                            msg.append(" - Success rate: ")
                                               .append(Math.round(rate * 10.0) / 10.0)
                                               .append(" percent");
                                        }
                                        
                                        info(msg.toString());
                                        successRate.set(Math.min(100.0, rate));
                                    }
                                }
                            }
                        }

                        try {
                            TimeUnit.MILLISECONDS.sleep(100);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }

                    if(!multiBreak.get()) break;
                }
            }
        });
    }

    private boolean shouldPlace(BlockPos blockPos) {
        if (!BlockUtils.canPlace(blockPos, true)) return false;
        
        // Check if block has a solid face to place against
        BlockState state = mc.world.getBlockState(blockPos);
        if (!state.isSolidBlock(mc.world, blockPos)) return false;
        
        // Check if there's already an item frame here
        Box box = new Box(blockPos);
        return mc.world.getEntitiesByClass(ItemFrameEntity.class, box, e -> true).isEmpty();
    }

    private String formatPerformanceStats() {
        StringBuilder sb = new StringBuilder();
        
        // Basic stats
        int duped = dupedCount.get();
        int attempts = attemptCount.get();
        sb.append(duped);
        
        if (attempts > 0) {
            sb.append("/").append(attempts);
            double rate = (duped * 100.0) / attempts;
            sb.append(" [")
              .append(Math.round(rate * 10.0) / 10.0)
              .append(" percent]");
        }

        // Add turbo stats
        if (turboMode.get() && averageAttemptTime > 0) {
            sb.append(" - ")
              .append(Math.round(averageAttemptTime * 10.0) / 10.0)
              .append("ms");
        }

        return sb.toString();
    }
}