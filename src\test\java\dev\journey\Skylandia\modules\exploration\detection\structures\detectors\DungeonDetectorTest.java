package dev.journey.Skylandia.modules.exploration.detection.structures.detectors;

import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.block.entity.MobSpawnerBlockEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class DungeonDetectorTest {

    @Mock
    private World world;

    @Mock
    private Chunk chunk;

    @Mock
    private MobSpawnerBlockEntity spawnerEntity;

    private DungeonDetector detector;
    private static final double CONFIDENCE_THRESHOLD = 0.8;
    private static final int MAX_SEARCH_RADIUS = 32;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        detector = new DungeonDetector(world, CONFIDENCE_THRESHOLD, MAX_SEARCH_RADIUS);
        
        // Setup default chunk position
        ChunkPos chunkPos = new ChunkPos(0, 0);
        when(chunk.getPos()).thenReturn(chunkPos);
    }

    @Test
    void getStructureType_ReturnsDungeon() {
        assertEquals(StructureType.DUNGEON, detector.getStructureType());
    }

    @Test
    void detectInChunk_IdealDungeonPattern_ReturnsHighConfidenceMatch() {
        BlockPos spawnerPos = new BlockPos(7, 30, 7);
        mockDungeonPattern(spawnerPos, 0.35, 0.65); // Ideal mossy/cobble ratio
        mockSpawner(spawnerPos);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertFalse(matches.isEmpty());
        StructureMatch match = matches.get(0);
        assertTrue(match.confidence() > 0.9);
        assertEquals(spawnerPos, match.center());
        assertEquals(StructureType.DUNGEON, match.type());
    }

    @Test
    void detectInChunk_SuboptimalRatios_ReturnsLowerConfidenceMatch() {
        BlockPos spawnerPos = new BlockPos(7, 30, 7);
        mockDungeonPattern(spawnerPos, 0.5, 0.5); // Equal mossy/cobble ratio
        mockSpawner(spawnerPos);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertFalse(matches.isEmpty());
        StructureMatch match = matches.get(0);
        assertTrue(match.confidence() >= CONFIDENCE_THRESHOLD);
        assertTrue(match.confidence() < 0.9);
    }

    @Test
    void detectInChunk_NoSpawner_ReturnsEmptyList() {
        BlockPos pos = new BlockPos(7, 30, 7);
        mockDungeonPattern(pos, 0.35, 0.65);
        // Don't mock spawner

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertTrue(matches.isEmpty());
    }

    @Test
    void detectInChunk_InvalidBlockRatios_ReturnsEmptyList() {
        BlockPos spawnerPos = new BlockPos(7, 30, 7);
        mockDungeonPattern(spawnerPos, 0.1, 0.1); // Very low ratios
        mockSpawner(spawnerPos);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertTrue(matches.isEmpty());
    }

    @Test
    void detectInChunk_MultipleDungeons_ReturnsAllMatches() {
        BlockPos spawnerPos1 = new BlockPos(7, 30, 7);
        BlockPos spawnerPos2 = new BlockPos(7, 40, 7);
        
        mockDungeonPattern(spawnerPos1, 0.35, 0.65);
        mockDungeonPattern(spawnerPos2, 0.4, 0.6);
        mockSpawner(spawnerPos1);
        mockSpawner(spawnerPos2);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertEquals(2, matches.size());
        assertTrue(matches.stream().allMatch(match -> match.confidence() >= CONFIDENCE_THRESHOLD));
    }

    private void mockDungeonPattern(BlockPos center, double mossyRatio, double cobbleRatio) {
        // Mock blocks in 7x5x7 area around spawner
        for (int x = -3; x <= 3; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -3; z <= 3; z++) {
                    if (x == 0 && y == 0 && z == 0) continue;
                    
                    BlockPos pos = center.add(x, y, z);
                    double random = Math.random();
                    BlockState state;
                    
                    if (random < mossyRatio) {
                        state = Blocks.MOSSY_COBBLESTONE.getDefaultState();
                    } else if (random < mossyRatio + cobbleRatio) {
                        state = Blocks.COBBLESTONE.getDefaultState();
                    } else {
                        state = Blocks.STONE.getDefaultState();
                    }
                    
                    when(world.getBlockState(pos)).thenReturn(state);
                }
            }
        }
    }

    private void mockSpawner(BlockPos pos) {
        when(world.getBlockState(pos)).thenReturn(Blocks.SPAWNER.getDefaultState());
        when(world.getBlockEntity(pos)).thenReturn(spawnerEntity);
    }
}