package dev.journey.Skylandia.modules.exploration.detection.structures.detectors;

import dev.journey.Skylandia.modules.exploration.detection.structures.StructureDetector;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import net.minecraft.block.Blocks;
import net.minecraft.block.BlockState;
import net.minecraft.block.DoorBlock;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.world.Heightmap;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class VillageDetector extends StructureDetector {
    private static final Set<BlockState> VILLAGE_PATH_BLOCKS = new HashSet<>();
    private static final Set<BlockState> VILLAGE_BUILDING_BLOCKS = new HashSet<>();
    
    static {
        // Path blocks
        VILLAGE_PATH_BLOCKS.add(Blocks.DIRT_PATH.getDefaultState());
        VILLAGE_PATH_BLOCKS.add(Blocks.GRAVEL.getDefaultState());
        
        // Common village building blocks
        VILLAGE_BUILDING_BLOCKS.add(Blocks.COBBLESTONE.getDefaultState());
        VILLAGE_BUILDING_BLOCKS.add(Blocks.STONE_BRICKS.getDefaultState());
        VILLAGE_BUILDING_BLOCKS.add(Blocks.OAK_PLANKS.getDefaultState());
        VILLAGE_BUILDING_BLOCKS.add(Blocks.SPRUCE_PLANKS.getDefaultState());
        VILLAGE_BUILDING_BLOCKS.add(Blocks.ACACIA_PLANKS.getDefaultState());
        VILLAGE_BUILDING_BLOCKS.add(Blocks.BIRCH_PLANKS.getDefaultState());
    }

    public VillageDetector(World world, double confidenceThreshold, int maxSearchRadius) {
        super(world, confidenceThreshold, maxSearchRadius);
    }

    @Override
    public StructureType getStructureType() {
        return StructureType.VILLAGE;
    }

    @Override
    public List<StructureMatch> detectInChunk(Chunk chunk) {
        List<StructureMatch> matches = new ArrayList<>();
        int chunkX = chunk.getPos().x * 16;
        int chunkZ = chunk.getPos().z * 16;

        // Villages are typically at surface level
        for (int x = chunkX; x < chunkX + 16; x++) {
            for (int z = chunkZ; z < chunkZ + 16; z++) {
                BlockPos surfacePos = getSurfacePosition(x, z);
                if (surfacePos == null) continue;

                double confidence = calculateVillageConfidence(surfacePos);
                if (confidence >= confidenceThreshold) {
                    Box bounds = calculateBounds(surfacePos, 48, 16, 48); // Village section size
                    String details = String.format("Village section at Y=%d with %.0f%% village-like structures", 
                        surfacePos.getY(), confidence * 100);
                    matches.add(new StructureMatch(
                        getStructureType(),
                        surfacePos,
                        confidence,
                        bounds,
                        details
                    ));
                }
            }
        }

        return matches;
    }    private BlockPos getSurfacePosition(int x, int z) {
        for (int y = world.getTopY(Heightmap.Type.WORLD_SURFACE, x, z); y > world.getBottomY(); y--) {
            BlockPos pos = new BlockPos(x, y, z);
            BlockState state = world.getBlockState(pos);
            if (!state.isAir() && !state.isOf(Blocks.SHORT_GRASS) && !state.isOf(Blocks.TALL_GRASS)) {
                return pos;
            }
        }
        return null;
    }

    private boolean isWorkstationBlock(BlockState state) {
        // Check for common village workstation blocks
        return state.isOf(Blocks.SMITHING_TABLE) ||
               state.isOf(Blocks.CRAFTING_TABLE) ||
               state.isOf(Blocks.BREWING_STAND) ||
               state.isOf(Blocks.ANVIL) ||
               state.isOf(Blocks.CHIPPED_ANVIL) ||
               state.isOf(Blocks.DAMAGED_ANVIL) ||
               state.isOf(Blocks.CAULDRON) ||
               state.isOf(Blocks.COMPOSTER) ||
               state.isOf(Blocks.BARREL) ||
               state.isOf(Blocks.SMOKER) ||
               state.isOf(Blocks.BLAST_FURNACE) ||
               state.isOf(Blocks.LOOM) ||
               state.isOf(Blocks.STONECUTTER) ||
               state.isOf(Blocks.CARTOGRAPHY_TABLE) ||
               state.isOf(Blocks.FLETCHING_TABLE) ||
               state.isOf(Blocks.LECTERN);
    }

    private double calculateVillageConfidence(BlockPos center) {
        int pathBlocks = 0;
        int buildingBlocks = 0;
        int doors = 0;
        int workstationBlocks = 0;
        int totalChecked = 0;

        // Check a larger 32x16x32 area around the center for better village detection
        for (int x = -16; x <= 16; x++) {
            for (int y = -8; y <= 8; y++) {
                for (int z = -16; z <= 16; z++) {
                    BlockPos checkPos = center.add(x, y, z);
                    BlockState state = world.getBlockState(checkPos);
                    
                    if (VILLAGE_PATH_BLOCKS.contains(state)) {
                        pathBlocks++;
                    }
                    if (VILLAGE_BUILDING_BLOCKS.contains(state)) {
                        buildingBlocks++;
                    }
                    if (state.getBlock() instanceof DoorBlock) {
                        doors++;
                    }
                    // Check for workstation blocks (smithing table, crafting table, etc.)
                    if (isWorkstationBlock(state)) {
                        workstationBlocks++;
                    }
                    totalChecked++;
                }
            }
        }

        // Calculate confidence based on village characteristics
        double pathRatio = Math.min(1.0, (double) pathBlocks / 30); // Expect around 30 path blocks in larger area
        double buildingRatio = Math.min(1.0, (double) buildingBlocks / 200); // Expect around 200 building blocks in larger area
        double doorRatio = Math.min(1.0, (double) doors / 5); // Expect at least 5 doors
        double workstationRatio = Math.min(1.0, (double) workstationBlocks / 3); // Expect at least 3 workstations

        // Weight the different factors
        return (pathRatio * 0.3) + (buildingRatio * 0.3) + (doorRatio * 0.2) + (workstationRatio * 0.2);
    }
}