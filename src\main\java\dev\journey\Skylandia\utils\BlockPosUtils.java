package dev.journey.Skylandia.utils;

import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.MathHelper;

public class BlockPosUtils {
    /**
     * Converts a Vec3d to a BlockPos by flooring coordinates
     */
    public static BlockPos fromVec3d(Vec3d vec) {
        return BlockPos.ofFloored(vec.x, vec.y, vec.z);
    }

    /**
     * Gets the center position of a block
     */
    public static Vec3d toVec3d(BlockPos pos) {
        return Vec3d.ofCenter(pos);
    }

    /**
     * Gets the bottom center position of a block
     */
    public static Vec3d toVec3dBottom(BlockPos pos) {
        return new Vec3d(pos.getX() + 0.5, pos.getY(), pos.getZ() + 0.5);
    }

    /**
     * Gets the exact position within a block based on offset ratios
     */
    public static Vec3d toVec3dOffset(BlockPos pos, double xOffset, double yOffset, double zOffset) {
        return new Vec3d(
            pos.getX() + MathHelper.clamp(xOffset, 0.0, 1.0),
            pos.getY() + MathHelper.clamp(yOffset, 0.0, 1.0),
            pos.getZ() + MathHelper.clamp(zOffset, 0.0, 1.0)
        );
    }

    /**
     * Gets a block position at the center of a collection of positions
     */
    public static BlockPos centerOf(BlockPos... positions) {
        if (positions.length == 0) {
            throw new IllegalArgumentException("Cannot find center of empty position list");
        }

        double x = 0, y = 0, z = 0;
        for (BlockPos pos : positions) {
            x += pos.getX();
            y += pos.getY();
            z += pos.getZ();
        }

        return new BlockPos(
            MathHelper.floor(x / positions.length),
            MathHelper.floor(y / positions.length),
            MathHelper.floor(z / positions.length)
        );
    }

    /**
     * Checks if two positions are within a given squared range
     */
    public static boolean isWithinRange(BlockPos pos1, BlockPos pos2, double squaredRange) {
        return pos1.getSquaredDistance(pos2) <= squaredRange;
    }
}
