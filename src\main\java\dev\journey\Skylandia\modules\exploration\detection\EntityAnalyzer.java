package dev.journey.Skylandia.modules.exploration.detection;

import dev.journey.Skylandia.utils.BlockPosUtils;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.function.Predicate;

public class EntityAnalyzer {
    private final World world;
    private static final double MIN_CLUSTER_DENSITY = 0.3;
    private static final double CLUSTER_RADIUS = 8.0;

    public EntityAnalyzer(World world) {
        this.world = world;
    }

    public List<DetectionResult> analyzeArea(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();
        Box searchBox = new Box(
            center.getX() - radius,
            center.getY() - radius,
            center.getZ() - radius,
            center.getX() + radius,
            center.getY() + radius,
            center.getZ() + radius
        );

        // Get all entities in the area
        List<? extends Entity> entities = world.getEntitiesByClass(
            Entity.class,
            searchBox,
            entity -> !entity.isSpectator() && !entity.isRemoved()
        );

        // Convert to proper list type
        List<Entity> filteredEntities = new ArrayList<>(entities);

        // Analyze entity clusters
        Map<BlockPos, List<Entity>> clusters = findEntityClusters(filteredEntities);
        for (Map.Entry<BlockPos, List<Entity>> entry : clusters.entrySet()) {
            if (entry.getValue().size() >= 3) {  // Minimum cluster size
                BlockPos clusterCenter = entry.getKey();
                float density = calculateClusterDensity(entry.getValue());
                
                if (density >= MIN_CLUSTER_DENSITY) {
                    results.add(DetectionResult.fromPositions(
                        String.format("Entity cluster found (%d entities)", entry.getValue().size()),
                        clusterCenter,
                        BlockPosUtils.toVec3d(clusterCenter),
                        DetectionResult.DetectionType.ENTITY,
                        density
                    ));
                }
            }
        }

        return results;
    }

    private Map<BlockPos, List<Entity>> findEntityClusters(List<Entity> entities) {
        Map<BlockPos, List<Entity>> clusters = new HashMap<>();
        
        for (Entity entity : entities) {
            Vec3d pos = entity.getPos();
            BlockPos entityPos = BlockPosUtils.fromVec3d(pos);
            boolean addedToCluster = false;
            
            // Check if entity belongs to an existing cluster
            for (Map.Entry<BlockPos, List<Entity>> entry : clusters.entrySet()) {
                if (BlockPosUtils.isWithinRange(entityPos, entry.getKey(), CLUSTER_RADIUS * CLUSTER_RADIUS)) {
                    entry.getValue().add(entity);
                    addedToCluster = true;
                    break;
                }
            }
            
            // If not added to any cluster, create a new one
            if (!addedToCluster) {
                List<Entity> newCluster = new ArrayList<>();
                newCluster.add(entity);
                clusters.put(entityPos, newCluster);
            }
        }
        
        return clusters;
    }

    private float calculateClusterDensity(List<Entity> cluster) {
        if (cluster.isEmpty()) return 0.0f;
        
        // Calculate the volume of space the entities occupy
        Box bounds = calculateClusterBounds(cluster);
        double volume = calculateBoxVolume(bounds);
        
        // Calculate density
        return (float) (cluster.size() / volume);
    }

    private Box calculateClusterBounds(List<Entity> cluster) {
        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        double minZ = Double.MAX_VALUE;
        double maxX = -Double.MAX_VALUE;
        double maxY = -Double.MAX_VALUE;
        double maxZ = -Double.MAX_VALUE;
        
        for (Entity entity : cluster) {
            Vec3d pos = entity.getPos();
            minX = Math.min(minX, pos.x);
            minY = Math.min(minY, pos.y);
            minZ = Math.min(minZ, pos.z);
            maxX = Math.max(maxX, pos.x);
            maxY = Math.max(maxY, pos.y);
            maxZ = Math.max(maxZ, pos.z);
        }
        
        return new Box(minX, minY, minZ, maxX, maxY, maxZ);
    }

    private double calculateBoxVolume(Box box) {
        return Math.max(1.0, (box.maxX - box.minX) * (box.maxY - box.minY) * (box.maxZ - box.minZ));
    }
}
