package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import dev.journey.Skylandia.utils.BlockPosUtils;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.List;

/**
 * Base class for all pattern detectors.
 * Provides common utilities and defines the detection interface.
 */
public abstract class PatternDetector {
    protected final World world;

    protected PatternDetector(World world) {
        this.world = world;
    }

    /**
     * Detects patterns within the given radius around a center point.
     * Each implementation should define its specific detection logic.
     */
    public abstract List<DetectionResult> detect(BlockPos center, int radius);

    protected boolean isAirBlock(BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        return state.isAir() || state.getBlock() == Blocks.CAVE_AIR;
    }

    protected boolean isValidSolidBlock(BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        return !state.isAir() && 
               state.getBlock() != Blocks.WATER && 
               state.getBlock() != Blocks.LAVA &&
               state.getBlock() != Blocks.CAVE_AIR;
    }

    protected boolean isBlockType(BlockPos pos, Block block) {
        return world.getBlockState(pos).getBlock() == block;
    }

    protected boolean isAnyBlockType(BlockPos pos, Block... blocks) {
        Block blockAtPos = world.getBlockState(pos).getBlock();
        for (Block block : blocks) {
            if (blockAtPos == block) return true;
        }
        return false;
    }

    protected DetectionResult createResult(String description, BlockPos pos, DetectionResult.DetectionType type, float confidence) {
        return DetectionResult.fromBlockPos(description, pos, type, confidence);
    }

    protected DetectionResult createResult(String description, Vec3d exactPos, DetectionResult.DetectionType type, float confidence) {
        return DetectionResult.fromVec3d(description, exactPos, type, confidence);
    }

    protected DetectionResult createResultWithExactPos(String description, BlockPos blockPos, Vec3d exactPos, DetectionResult.DetectionType type, float confidence) {
        return DetectionResult.fromPositions(description, blockPos, exactPos, type, confidence);
    }
}
