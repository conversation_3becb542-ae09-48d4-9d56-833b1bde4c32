{"schemaVersion": 1, "id": "skylandia", "version": "1.0.8+mc${mc_version}", "name": "Skylandia", "description": "Skylandia connected essentials", "authors": ["Beelzebub4883"], "contact": {"repo": "https://github.com/FaxHack/Skylandia"}, "icon": "assets/icon/icon.png", "environment": "client", "entrypoints": {"meteor": ["dev.journey.Skylandia.Skylandia"]}, "accessWidener": "skylandia-addon.accesswidener", "mixins": ["skylandia.mixins.json"], "custom": {"meteor-client:color": "255,0,255"}, "depends": {"java": ">=21", "minecraft": ">=${mc_version}", "meteor-client": "*"}}