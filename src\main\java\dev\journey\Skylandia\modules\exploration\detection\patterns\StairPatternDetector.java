package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.StairsBlock;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class StairPatternDetector extends PatternDetector {
    private static final int MIN_STAIRS = 3;
    private static final int MAX_Y_VARIANCE = 5;
    private static final Direction[] HORIZONTAL_DIRECTIONS = {
        Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST
    };

    public StairPatternDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (isStairsBlock(pos)) {
                        StairPattern pattern = findStairPattern(pos);
                        if (pattern != null && !isAlreadyDetected(results, pattern.center)) {
                            results.add(createResult(
                                String.format("Stair pattern found (%d steps)", pattern.stepCount),
                                pattern.center,
                                DetectionResult.DetectionType.STRUCTURE,
                                pattern.confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private boolean isStairsBlock(BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        return state.getBlock() instanceof StairsBlock;
    }

    private StairPattern findStairPattern(BlockPos start) {
        // Check each horizontal direction for a stair pattern
        for (Direction direction : HORIZONTAL_DIRECTIONS) {
            StairPattern pattern = analyzeStairPattern(start, direction);
            if (pattern != null) {
                return pattern;
            }
        }
        return null;
    }

    private StairPattern analyzeStairPattern(BlockPos start, Direction direction) {
        int stepCount = 1;
        int heightChange = 0;
        BlockPos current = start;
        List<BlockPos> stairs = new ArrayList<>();
        stairs.add(start);

        // Follow potential stair pattern
        while (stepCount < 8) { // Maximum reasonable stair length to check
            BlockPos next = current.offset(direction);
            BlockPos up = next.up();
            BlockPos down = next.down();

            // Check for next stair block within reasonable height variance
            BlockPos found = null;
            for (int y = -MAX_Y_VARIANCE; y <= MAX_Y_VARIANCE; y++) {
                BlockPos check = next.up(y);
                if (isStairsBlock(check)) {
                    found = check;
                    break;
                }
            }

            if (found == null) break;

            stairs.add(found);
            heightChange += found.getY() - current.getY();
            current = found;
            stepCount++;
        }

        // Evaluate if this could be a player-made stair pattern
        if (stepCount >= MIN_STAIRS) {
            BlockPos center = stairs.get(stairs.size() / 2);
            float confidence = calculateConfidence(stepCount, heightChange, stairs);
            return new StairPattern(center, stepCount, heightChange, confidence);
        }

        return null;
    }

    private float calculateConfidence(int stepCount, int heightChange, List<BlockPos> stairs) {
        float confidence = 0.5f; // Base confidence for meeting minimum steps

        // More steps increase confidence
        confidence += Math.min(0.1f * (stepCount - MIN_STAIRS), 0.2f);

        // Regular height changes suggest intentional placement
        if (Math.abs(heightChange) > 0) {
            float heightPerStep = (float) Math.abs(heightChange) / stepCount;
            if (heightPerStep <= 1.5f && heightPerStep >= 0.5f) {
                confidence += 0.2f;
            }
        }

        // Check for consistent spacing and alignment
        if (hasRegularSpacing(stairs)) {
            confidence += 0.1f;
        }

        return Math.min(confidence, 1.0f);
    }

    private boolean hasRegularSpacing(List<BlockPos> stairs) {
        if (stairs.size() < 3) return false;

        // Calculate average distance between steps
        double totalDistance = 0;
        for (int i = 1; i < stairs.size(); i++) {
            totalDistance += stairs.get(i).getSquaredDistance(stairs.get(i - 1));
        }
        double averageDistance = totalDistance / (stairs.size() - 1);

        // Check if distances are consistent
        for (int i = 1; i < stairs.size(); i++) {
            double distance = stairs.get(i).getSquaredDistance(stairs.get(i - 1));
            if (Math.abs(distance - averageDistance) > 2.0) {
                return false;
            }
        }

        return true;
    }

    private boolean isAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (result.getPosition().getSquaredDistance(pos) < 25) { // Within 5 blocks
                return true;
            }
        }
        return false;
    }

    private static class StairPattern {
        final BlockPos center;
        final int stepCount;
        final int heightChange;
        final float confidence;

        StairPattern(BlockPos center, int stepCount, int heightChange, float confidence) {
            this.center = center;
            this.stepCount = stepCount;
            this.heightChange = heightChange;
            this.confidence = confidence;
        }
    }
}
