package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.StringSetting;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.Registries;
import net.minecraft.util.Hand;
import net.minecraft.util.Identifier;

import java.util.concurrent.TimeUnit;

public class AutoDropDupe extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgCommands = settings.createGroup("Commands");
    
    // Command prefixes
    private final Setting<String> cmdPrefix = sgCommands.add(new StringSetting.Builder()
        .name("command-prefix")
        .description("Prefix for dupe commands")
        .defaultValue(".")
        .build()
    );

    private final Setting<String> dropHandCmd = sgCommands.add(new StringSetting.Builder()
        .name("drop-hand-command")
        .description("Command to drop item in hand")
        .defaultValue("drop hand")
        .build()
    );

    private final Setting<String> dropInvCmd = sgCommands.add(new StringSetting.Builder()
        .name("drop-inventory-command")
        .description("Command to drop entire inventory")
        .defaultValue("drop inventory")
        .build()
    );

    private final Setting<String> dropAllCmd = sgCommands.add(new StringSetting.Builder()
        .name("drop-all-command")
        .description("Command to drop everything")
        .defaultValue("drop all")
        .build()
    );
    private final SettingGroup sgTiming = settings.createGroup("Timing");
    private final SettingGroup sgStats = settings.createGroup("Statistics");

    // General settings
    private final Setting<Boolean> turboMode = sgGeneral.add(new BoolSetting.Builder()
        .name("turbo-mode")
        .description("Increase drop speed for faster duping")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> dropDelay = sgTiming.add(new IntSetting.Builder()
        .name("drop-delay")
        .description("Delay between drop attempts in milliseconds")
        .defaultValue(500)
        .min(50)
        .max(2000)
        .sliderMax(1000)
        .visible(() -> !turboMode.get())
        .build()
    );

    private final Setting<Integer> turboDropDelay = sgTiming.add(new IntSetting.Builder()
        .name("turbo-drop-delay")
        .description("Delay between drops in turbo mode (milliseconds)")
        .defaultValue(100)
        .min(20)
        .max(500)
        .sliderMax(200)
        .visible(turboMode::get)
        .build()
    );

    private final Setting<Integer> dropAttempts = sgTiming.add(new IntSetting.Builder()
        .name("drop-attempts")
        .description("Number of drop attempts per cycle")
        .defaultValue(3)
        .min(1)
        .max(10)
        .sliderMax(5)
        .build()
    );

    private final Setting<Integer> cycleDelay = sgTiming.add(new IntSetting.Builder()
        .name("cycle-delay")
        .description("Delay between drop cycles in ticks")
        .defaultValue(20)
        .min(1)
        .max(100)
        .sliderMax(40)
        .build()
    );

    // Statistics settings
    private final Setting<Integer> dupedCount = sgStats.add(new IntSetting.Builder()
        .name("duped-count")
        .description("Number of items successfully duped")
        .defaultValue(0)
        .min(0)
        .build()
    );
    
    private final Setting<Integer> attemptCount = sgStats.add(new IntSetting.Builder()
        .name("attempt-count")
        .description("Number of dupe attempts made")
        .defaultValue(0)
        .min(0)
        .build()
    );

    private final Setting<Double> successRate = sgStats.add(new DoubleSetting.Builder()
        .name("success-rate")
        .description("Percentage of successful dupes")
        .defaultValue(0)
        .min(0)
        .max(100)
        .sliderMax(100)
        .build()
    );

    private final Setting<Boolean> displayStats = sgStats.add(new BoolSetting.Builder()
        .name("display-stats")
        .description("Show duping statistics in HUD")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> resetStatsOnDisable = sgStats.add(new BoolSetting.Builder()
        .name("reset-on-disable")
        .description("Reset statistics when module is disabled")
        .defaultValue(false)
        .build()
    );

    private int cycleTimer = 0;
    private int currentAttempts = 0;
    private long lastDropTime = 0;
    private boolean isDropping = false;
    private String targetItemId = null;
    private DropMode currentDropMode = DropMode.NONE;
    
    private enum DropMode {
        NONE,
        HAND,
        INVENTORY,
        ALL,
        SPECIFIC_ITEM
    }

    @EventHandler
    private void onChatMessage(ReceiveMessageEvent event) {
        String message = event.getMessage().getString();
        if (!message.startsWith(cmdPrefix.get())) return;

        String command = message.substring(cmdPrefix.get().length()).trim();
        
        if (command.equals(dropHandCmd.get())) {
            currentDropMode = DropMode.HAND;
            info("Dropping hand item");
        }
        else if (command.equals(dropInvCmd.get())) {
            currentDropMode = DropMode.INVENTORY;
            info("Dropping inventory");
        }
        else if (command.equals(dropAllCmd.get())) {
            currentDropMode = DropMode.ALL;
            info("Dropping all items");
        }
        else if (command.startsWith("drop ")) {
            String itemId = command.substring(5).trim();
            if (!itemId.contains(":")) {
                itemId = "minecraft:" + itemId;
            }
            Identifier id = Identifier.tryParse(itemId);
            if (id == null) {
                error("Invalid item identifier: " + itemId);
                return;
            }
            Item item = Registries.ITEM.get(id);
            if (item != Items.AIR) {
                targetItemId = itemId;
                currentDropMode = DropMode.SPECIFIC_ITEM;
                info("Dropping all " + itemId);
            } else {
                error("Unknown item: " + itemId);
            }
        }
    }

    public AutoDropDupe() {
        super(Skylandia.Automation, "auto-drop-dupe", "Automatically performs drop dupe exploit");
    }

    @Override
    public void onActivate() {
        cycleTimer = 0;
        currentAttempts = 0;
        lastDropTime = 0;
        isDropping = false;
        currentDropMode = DropMode.NONE;
        targetItemId = null;

        info("§aAuto Drop Dupe activated");
        info("§7Mode: " + (turboMode.get() ? "§bTurbo" : "§7Normal"));
        info("§7Settings:");
        info(String.format("§7 - Attempts per cycle: §b%d", dropAttempts.get()));
        info(String.format("§7 - Delay: §b%dms", turboMode.get() ? turboDropDelay.get() : dropDelay.get()));
    }

    @Override
    public void onDeactivate() {
        if (resetStatsOnDisable.get()) {
            dupedCount.set(0);
            attemptCount.set(0);
            successRate.set(0.0);
        }

        info("Auto Drop Dupe deactivated");
        if (!resetStatsOnDisable.get() && dupedCount.get() > 0) {
            info(String.format("Session stats: %d items duped (%.1f%% success rate)",
                dupedCount.get(), successRate.get()));
        }
    }

    @Override
    public String getInfoString() {
        if (!displayStats.get()) return null;
        
        StringBuilder info = new StringBuilder();
        
        // Mode indicator
        info.append(turboMode.get() ? "§bTurbo§r " : "§7Normal§r ");
        
        // Statistics
        info.append(String.format("| §aDuped: %d§r/§7%d§r ", dupedCount.get(), attemptCount.get()));
        
        // Success rate with color based on performance
        double rate = successRate.get();
        String rateColor = rate > 75 ? "§a" : rate > 50 ? "§e" : rate > 25 ? "§6" : "§c";
        info.append(String.format("(§r%s%.1f%%§r)", rateColor, rate));
        
        return info.toString();
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (!Utils.canUpdate() || mc.player == null) return;

        // Check if the player has any items
        if (mc.player.getMainHandStack().isEmpty()) {
            warning("No item in main hand");
            return;
        }

        // Skip air and non-dupeable items
        if (mc.player.getMainHandStack().getItem() == Items.AIR) {
            return;
        }

        if (isDropping) {
            long currentTime = System.currentTimeMillis();
            int currentDelay = turboMode.get() ? turboDropDelay.get() : dropDelay.get();

            if (currentTime - lastDropTime >= currentDelay) {
                try {
                    // Perform drop action
                    mc.player.dropItem(mc.player.getMainHandStack(), false);
                    
                    // Handle different drop modes
                    switch (currentDropMode) {
                        case HAND:
                            dropHandItem();
                            break;
                            
                        case INVENTORY:
                            dropInventory(false);
                            break;
                            
                        case ALL:
                            dropInventory(true);
                            break;
                            
                        case SPECIFIC_ITEM:
                            if (targetItemId != null) {
                                dropSpecificItems(targetItemId);
                            }
                            break;
                            
                        default:
                            dropHandItem();
                    }

                    lastDropTime = currentTime;

                    if (currentAttempts >= dropAttempts.get()) {
                        isDropping = false;
                        currentAttempts = 0;
                        cycleTimer = cycleDelay.get();
                    } else {
                        // Add small delay between drops
                        TimeUnit.MILLISECONDS.sleep(50);
                    }
                } catch (InterruptedException e) {
                    error("Drop sequence interrupted");
                }
            }
        } else {
            if (cycleTimer > 0) {
                cycleTimer--;
            } else {
                isDropping = true;
                lastDropTime = System.currentTimeMillis();
            }
        }
    }
    
        private void dropHandItem() {
            if (!mc.player.getMainHandStack().isEmpty()) {
                mc.player.dropItem(mc.player.getMainHandStack(), false);
                currentAttempts++;
                attemptCount.set(attemptCount.get() + 1);
                
                dupedCount.set(dupedCount.get() + 1);
                double rate = (dupedCount.get() * 100.0) / attemptCount.get();
                successRate.set(Math.min(100.0, rate));
            }
        }
    
        private void dropInventory(boolean includeEquipped) {
            // Drop main inventory
            for (int i = 0; i < mc.player.getInventory().size(); i++) {
                ItemStack stack = mc.player.getInventory().getStack(i);
                if (!stack.isEmpty()) {
                    mc.player.dropItem(stack, false);
                    currentAttempts++;
                    attemptCount.set(attemptCount.get() + 1);
                    dupedCount.set(dupedCount.get() + 1);
                }
            }
    
            // Drop equipped items if requested
            if (includeEquipped) {
                for (ItemStack stack : mc.player.getArmorItems()) {
                    if (!stack.isEmpty()) {
                        mc.player.dropItem(stack, false);
                        currentAttempts++;
                        attemptCount.set(attemptCount.get() + 1);
                        dupedCount.set(dupedCount.get() + 1);
                    }
                }
            }
    
            // Update success rate
            double rate = (dupedCount.get() * 100.0) / attemptCount.get();
            successRate.set(Math.min(100.0, rate));
        }
    
        private void dropSpecificItems(String itemId) {
            if (!itemId.contains(":")) {
                itemId = "minecraft:" + itemId;
            }
            Identifier id = Identifier.tryParse(itemId);
            if (id == null) {
                error("Invalid item identifier: " + itemId);
                return;
            }
            
            Item targetItem = Registries.ITEM.get(id);
            
            // Search inventory for matching items
            for (int i = 0; i < mc.player.getInventory().size(); i++) {
                ItemStack stack = mc.player.getInventory().getStack(i);
                if (!stack.isEmpty() && stack.getItem() == targetItem) {
                    mc.player.dropItem(stack, false);
                    currentAttempts++;
                    attemptCount.set(attemptCount.get() + 1);
                    dupedCount.set(dupedCount.get() + 1);
                }
            }

            // Update success rate
            if (attemptCount.get() > 0) {
                double rate = (dupedCount.get() * 100.0) / attemptCount.get();
                successRate.set(Math.min(100.0, rate));
            }
        }
}
