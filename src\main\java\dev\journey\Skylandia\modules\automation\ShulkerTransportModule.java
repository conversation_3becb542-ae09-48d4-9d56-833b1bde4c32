package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import dev.journey.Skylandia.automation.AutomationFlow;
import dev.journey.Skylandia.automation.conditions.Condition;
import dev.journey.Skylandia.automation.actions.Action;
import dev.journey.Skylandia.automation.transport.*;
import dev.journey.Skylandia.modules.utility.AutoLoginModule;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.events.game.GameLeftEvent;
import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ShulkerTransportModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgCommands = settings.createGroup("Commands");
    private final SettingGroup sgSequences = settings.createGroup("Sequences");
    private final SettingGroup sgSafety = settings.createGroup("Safety");
    private final SettingGroup sgDebug = settings.createGroup("Debug");
    private final SettingGroup sgPositions = settings.createGroup("Positions");
    private final SettingGroup sgStats = settings.createGroup("Statistics");
    private final SettingGroup sgSteps = settings.createGroup("Transport Steps");

    // Statistics tracking
    private int shulkersTransported = 0;
    private int enderpearlsUsed = 0;
    private int totalCommands = 0;
    private int failedAttempts = 0;
    private long startTime = 0;
    private int successfulTransports = 0;
    private int totalDistance = 0;
    private int lastPosX = 0;
    private int lastPosZ = 0;
    private long lastStatusUpdate = 0;
    private int waterStuckCounter = 0;
    private BlockPos lastPearlPos = null;
    private int teleportTimeoutCounter = 0;
    private BlockPos teleportStartPos = null;

    // Stats display settings
    private final Setting<Boolean> showStats = sgStats.add(new BoolSetting.Builder()
        .name("show-stats")
        .description("Show transport statistics")
        .defaultValue(true)
        .build()
    );

    // Teleport timing settings
    private final Setting<Integer> teleportTimeout = sgSafety.add(new IntSetting.Builder()
        .name("teleport-timeout")
        .description("Timeout for teleport operations in ticks")
        .defaultValue(100)
        .min(40)
        .max(400)
        .build()
    );

    private final Setting<Boolean> commanderControlledTiming = sgSafety.add(new BoolSetting.Builder()
        .name("commander-timing")
        .description("Let commander control pearl teleport timing")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> itemDropTimeout = sgSafety.add(new IntSetting.Builder()
        .name("drop-timeout")
        .description("Timeout for item drop confirmation in ticks")
        .defaultValue(60)
        .min(20)
        .max(200)
        .build()
    );

    // Crash recovery settings
    private final Setting<Boolean> enableCrashRecovery = sgSafety.add(new BoolSetting.Builder()
        .name("crash-recovery")
        .description("Enable automatic crash recovery for transporter")
        .defaultValue(true)
        .build()
    );

    // Debug settings
    private final Setting<Boolean> debugMode = sgDebug.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Enable detailed debug logging")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> statusUpdateInterval = sgDebug.add(new IntSetting.Builder()
        .name("status-update-interval")
        .description("Interval between status updates in milliseconds")
        .defaultValue(1000)
        .min(100)
        .max(10000)
        .build()
    );

    private final Setting<Boolean> trackPearlCoords = sgDebug.add(new BoolSetting.Builder()
        .name("track-pearl-coords")
        .description("Track ender pearl throw coordinates")
        .defaultValue(true)
        .build()
    );

    // Safety settings
    private final Setting<Integer> waterStuckThreshold = sgSafety.add(new IntSetting.Builder()
        .name("water-stuck-threshold")
        .description("Ticks before considering player stuck in water")
        .defaultValue(40)
        .min(20)
        .max(200)
        .build()
    );

    private final Setting<Boolean> validateStashExistence = sgSafety.add(new BoolSetting.Builder()
        .name("validate-stash")
        .description("Validate stash exists before pickup")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> verifyPearlThrow = sgSafety.add(new BoolSetting.Builder()
        .name("verify-pearl-throw")
        .description("Verify ender pearl throw success")
        .defaultValue(true)
        .build()
    );

    // Transport cycle timeout
    private final Setting<Integer> cycleTimeout = sgSafety.add(new IntSetting.Builder()
        .name("cycle-timeout")
        .description("Maximum time (in ticks) for a transport cycle")
        .defaultValue(200)
        .min(100)
        .max(1000)
        .build()
    );

    // Pearl slot setting
    private final Setting<Integer> pearlSlot = sgSafety.add(new IntSetting.Builder()
        .name("pearl-slot")
        .description("Hotbar slot for ender pearls (0-8)")
        .defaultValue(0)
        .min(0)
        .max(8)
        .build()
    );

    private final Setting<List<String>> preTransportCommands = sgSteps.add(new StringListSetting.Builder()
        .name("pre-transport")
        .description("Commands to run before transport")
        .defaultValue(Arrays.asList(
            "/msg {other} Preparing transport",
            "/effect give {self} resistance 60 3",
            "/spawnpoint"
        ))
        .build()
    );

    private final Setting<List<String>> mainTransportCommands = sgSteps.add(new StringListSetting.Builder()
        .name("main-transport")
        .description("Main transport sequence commands")
        .defaultValue(Arrays.asList(
            "/tp {other}",
            "/msg {other} Ready to transfer"
        ))
        .build()
    );

    private final Setting<List<String>> postTransportCommands = sgSteps.add(new StringListSetting.Builder()
        .name("post-transport")
        .description("Commands to run after transport")
        .defaultValue(Arrays.asList(
            "/msg {other} Transport complete",
            "/home"
        ))
        .build()
    );

    private final Setting<List<String>> errorRecoveryCommands = sgSteps.add(new StringListSetting.Builder()
        .name("error-recovery")
        .description("Commands to run on transport error")
        .defaultValue(Arrays.asList(
            "/msg {other} Transport failed",
            "/spawn"
        ))
        .build()
    );
    
    private final TransportConfig config;
    private final CoordinationManager coordinator;
    private final StasisController stasis;
    private final InventoryManager inventory;
    
    private CommandSequence currentSequence;
    private int currentDelay;
    private int currentRetries;
    private CommandSequence.CommandStep lastFailedStep;
    private TransportState currentState;
    private final List<AutomationFlow> activeFlows;

    // Swarm settings
    private Setting<Integer> swarmPort;
    private Setting<Integer> initializationTimeout;
    private int initializationStep = 0;
    private long lastInitTime = 0;
    
    private void initSwarmSettings() {
        swarmPort = sgCommands.add(new IntSetting.Builder()
            .name("swarm-port")
            .description("Port to use for swarm communication")
            .defaultValue(9543)
            .min(1000)
            .max(65535)
            .build()
        );

        initializationTimeout = sgSafety.add(new IntSetting.Builder()
            .name("init-timeout")
            .description("Timeout for initialization steps (in ticks)")
            .defaultValue(100)
            .min(20)
            .max(200)
            .build()
        );
    }

    // Command settings
    private final Setting<List<String>> commanderCommands = sgCommands.add(new StringListSetting.Builder()
        .name("commander-commands")
        .description("Commands to be executed when playing as commander")
        .defaultValue(Arrays.asList(
            "/tpahere <transporter>",
            "/msg <transporter> ready",
            "/msg <transporter> done"
        ))
        .build()
    );

    private final Setting<List<String>> transporterCommands = sgCommands.add(new StringListSetting.Builder()
        .name("transporter-commands")
        .description("Commands to be executed when playing as transporter")
        .defaultValue(Arrays.asList(
            "/tpaccept",
            "/msg <commander> ready",
            "/msg <commander> complete"
        ))
        .build()
    );

    // Settings
    private final Setting<Integer> defaultCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("default-delay")
        .description("Default delay between commands in ticks")
        .defaultValue(20)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<String> commanderUsername = sgGeneral.add(new StringSetting.Builder()
        .name("commander-name")
        .description("Username of the commander")
        .defaultValue("")
        .build()
    );

    private final Setting<String> transporterUsername = sgGeneral.add(new StringSetting.Builder()
        .name("transporter-name")
        .description("Username of the transporter")
        .defaultValue("")
        .build()
    );

    private final Setting<Integer> readyMessageDelay = sgSequences.add(new IntSetting.Builder()
        .name("ready-delay")
        .description("Delay after ready messages in ticks")
        .defaultValue(40)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Integer> teleportDelay = sgSequences.add(new IntSetting.Builder()
        .name("teleport-delay")
        .description("Delay after teleport commands in ticks")
        .defaultValue(60)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Boolean> autoRetryCommands = sgSequences.add(new BoolSetting.Builder()
        .name("auto-retry")
        .description("Automatically retry failed commands")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> maxRetries = sgSequences.add(new IntSetting.Builder()
        .name("max-retries")
        .description("Maximum retry attempts for failed commands")
        .defaultValue(3)
        .min(1)
        .max(10)
        .visible(() -> autoRetryCommands.get())
        .build()
    );

    private final Setting<Integer> retryDelay = sgSequences.add(new IntSetting.Builder()
        .name("retry-delay")
        .description("Delay between retry attempts in ticks")
        .defaultValue(40)
        .min(1)
        .max(200)
        .visible(() -> autoRetryCommands.get())
        .build()
    );

    private final Setting<Boolean> announceSteps = sgSequences.add(new BoolSetting.Builder()
        .name("announce-steps")
        .description("Announce sequence steps in chat")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> previewMode = sgSequences.add(new BoolSetting.Builder()
        .name("preview-mode")
        .description("Show command timings without executing")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> moveCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("move-delay")
        .description("Delay after movement commands")
        .defaultValue(30)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Integer> chatCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("chat-delay") 
        .description("Delay after chat messages")
        .defaultValue(10)
        .min(1)
        .max(200)
        .build()
    );

    private final Setting<Integer> combatCommandDelay = sgSequences.add(new IntSetting.Builder()
        .name("combat-delay")
        .description("Delay after combat commands")
        .defaultValue(15)
        .min(1)
        .max(200)
        .build()
    );

    public ShulkerTransportModule() {
        super(Skylandia.Automation, "shulker-transport", "Automates shulker box transport between players");
        
        this.config = new TransportConfig();
        this.coordinator = new CoordinationManager(this, config);
        this.stasis = new StasisController(this, config);
        this.inventory = new InventoryManager(this, config);
        this.activeFlows = new ArrayList<>();
        this.currentState = TransportState.IDLE;
        
        // Initialize swarm settings
        initSwarmSettings();
    }

    private String getCommanderName() {
        return coordinator.getCommanderName();
    }

    private String getTransporterName() {
        return coordinator.getTransporterName();
    }

    @EventHandler
    public void onEnable() {
        if (mc.player == null) return;

        // Set role names
        if (config.mode.get() == TransportMode.COMMANDER) {
            coordinator.setCommanderName(mc.player.getName().getString());
            coordinator.setTransporterName(transporterUsername.get());
        } else {
            coordinator.setCommanderName(commanderUsername.get());
            coordinator.setTransporterName(mc.player.getName().getString());
        }

        // Start initialization sequence
        resetInitialization();
        setState(TransportState.VALIDATING);
        executeInitialization();
    }

    // New settings for StasisController
    private final Setting<Double> pitchSetting = sgSafety.add(new DoubleSetting.Builder()
        .name("pitch")
        .description("Pitch angle for stasis chamber")
        .defaultValue(90.0)
        .range(-90.0, 90.0)
        .build()
    );

    private final Setting<Double> yawSetting = sgSafety.add(new DoubleSetting.Builder()
        .name("yaw")
        .description("Yaw angle for stasis chamber")
        .defaultValue(0.0)
        .range(-180.0, 180.0)
        .build()
    );

    private List<String> getCommandsForRole() {
        return config.mode.get() == TransportMode.COMMANDER ?
               commanderCommands.get() : transporterCommands.get();
    }

    private final Setting<Integer> stasisDelay = sgSafety.add(new IntSetting.Builder()
        .name("stasis-delay")
        .description("Delay between stasis chamber operations")
        .defaultValue(10)
        .min(1)
        .max(100)
        .build()
    );

    private final Setting<BlockPos> trapdoorLocation = sgPositions.add(new BlockPosSetting.Builder()
        .name("trapdoor-location")
        .description("Location of the stasis chamber trapdoor")
        .defaultValue(new BlockPos(0, 64, 0))
        .build()
    );

    // Getter methods for StasisController
    public float getPitchSetting() {
        return pitchSetting.get().floatValue();
    }

    public float getYawSetting() {
        return yawSetting.get().floatValue();
    }

    public int getStasisDelay() {
        return stasisDelay.get();
    }

    public BlockPos getTrapdoorLocation() {
        return trapdoorLocation.get();
    }

    // Make executeCommand package-private for StasisController access
    public void executeCommand(String command) {
        if (mc.player == null) return;
        
        String finalCommand = command.startsWith("/") ? command.substring(1) : command;
        mc.player.networkHandler.sendCommand(finalCommand);
    }
private void initializeCommander() {
    if (!validateInitialization()) return;
    
    switch (initializationStep) {
        case 0: // Verify transporter online
            if (verifyTransporterOnline()) {
                initializationStep++;
                info("Transporter verified online");
            }
            break;
            
        case 1: // Enable swarm features
            executeCommand(".toggle swarm on");
            executeCommand(String.format(".settings swarm mode Host"));
            executeCommand(String.format(".settings swarm port %d", swarmPort.get()));
            initializationStep++;
            info("Swarm features enabled");
            break;
            
        case 2: // Wait for transporter ready
            AutomationFlow flow = new AutomationFlow();
            coordinator.readyForTransport().test(flow);
            initializationStep++;
            info("Received transporter ready signal");
            break;

        case 3: // Initialize stasis
            if (stasis.initialize()) {
                initializationStep++;
                setState(TransportState.CHAMBER_ACTIVE);
                info("Stasis chamber initialized");
            }
            break;
            
        default:
            resetInitialization();
            setState(TransportState.IDLE);
    }
}

private void initializeTransporter() {
    if (!validateInitialization()) return;
    
    switch (initializationStep) {
        case 0: // Set bed spawn
            if (mc.player != null) {
                executeCommand("/spawnpoint");
                initializationStep++;
                info("Spawn point set");
            }
            break;
            
        case 1: // Enable required modules
            executeCommand(".toggle swarm on");
            executeCommand(String.format(".settings swarm mode Worker"));
            executeCommand(String.format(".settings swarm port %d", swarmPort.get()));
            initializationStep++;
            info("Swarm features enabled");
            break;
            
        case 2: // Signal ready
            AutomationFlow flow = new AutomationFlow();
            coordinator.signalTransportComplete().execute(flow);
            initializationStep++;
            info("Ready signal sent");
            break;
            
        case 3: // Wait for proceed
            AutomationFlow startFlow = new AutomationFlow();
            coordinator.receivedStartSignal().test(startFlow);
            initializationStep++;
            setState(TransportState.WAITING_READY);
            info("Received proceed signal");
            break;
            
        default:
            resetInitialization();
            setState(TransportState.IDLE);
    }
}

private boolean validateInitialization() {
    if (System.currentTimeMillis() - lastInitTime > initializationTimeout.get() * 50) {
        error("Initialization step timed out");
        resetInitialization();
        return false;
    }
    return true;
}

private void resetInitialization() {
    initializationStep = 0;
    lastInitTime = System.currentTimeMillis();
}

private boolean verifyTransporterOnline() {
    return mc.player != null &&
           getTransporterName() != null &&
           !getTransporterName().isEmpty();
}

private void executeInitialization() {
    lastInitTime = System.currentTimeMillis();
    if (config.mode.get() == TransportMode.COMMANDER) {
        initializeCommander();
    } else {
        initializeTransporter();
    }
}

@EventHandler
private void onInit() {
    // Initialize settings if needed
    if (swarmPort == null) {
        info("Initializing swarm settings");
        initSwarmSettings();
    }
}

@EventHandler
private void onDisable() {
    // Clean up swarm when disabled
    if (isActive()) {
        executeCommand(".toggle swarm off");
    }
}


// Method for internal use (role-specific commands)
private int cycleTimeoutCounter = 0;

private void executeRoleCommands() {
    // Reset state and timeout counter
    cycleTimeoutCounter = 0;
    setState(TransportState.VALIDATING);
    
    List<String> commands = getCommandsForRole();
    if (commands == null || commands.isEmpty()) {
        warning("No commands configured for current role");
        return;
    }

    CommandSequence sequence = new CommandSequence("role-commands");
    
    // Initial TPA sequence
    if (config.mode.get() == TransportMode.COMMANDER) {
        sequence.addStep("/tpahere " + getTransporterName(), teleportDelay.get(), "Sending teleport request");
        sequence.addStep("/msg " + getTransporterName() + " Ready for transport", readyMessageDelay.get(), "Sending ready message");
    } else {
        sequence.addStep("/tpaccept", teleportDelay.get(), "Accepting teleport");
        sequence.addStep("/msg " + getCommanderName() + " Arrived", readyMessageDelay.get(), "Confirming arrival");
    }

    // Ensure pearl slot is empty
    sequence.addStep("/clear @s ender_pearl", defaultCommandDelay.get(), "Clearing pearls");
    
    // Add base role commands
    for (String cmd : commands) {
        String processedCmd = cmd.replace("<commander>", getCommanderName())
                            .replace("<transporter>", getTransporterName());
        sequence.addStep(processedCmd, defaultCommandDelay.get(), "Executing: " + cmd);
        updateStats("command");
    }
    
    currentSequence = sequence;
    currentRetries = 0;
    lastFailedStep = null;
}

public void executeCommandList(List<String> commands, String targetPlayer) {
    if (commands == null || commands.isEmpty()) {
        warning("No commands to execute");
        return;
    }

    CommandSequence sequence = new CommandSequence("command-list");
    for (String cmd : commands) {
        String processedCmd = cmd.replace("<commander>", getCommanderName())
                                .replace("<transporter>", getTransporterName());
        sequence.addStep(processedCmd, defaultCommandDelay.get(), "Executing: " + cmd);
    }

    currentSequence = sequence;
    currentRetries = 0;
    lastFailedStep = null;
}

@EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) return;

        try {
            // Validate teleport status
            validateTeleport();
            // Process initialization first
            if (currentState == TransportState.VALIDATING) {
                executeInitialization();
                return;
            }

            // Update debug status
            updateModuleStatus();

            // Run safety checks
            validateSafetyChecks();

            // Only process other states if module is active
            if (!config.active.get()) return;

            // Update managers
            coordinator.update();
            stasis.update();
            inventory.update();

            // Handle initialization states
            if (initializationStep > 0 && initializationStep < 4) {
                if (config.mode.get() == TransportMode.COMMANDER) {
                    initializeCommander();
                } else {
                    initializeTransporter();
                }
                return;
            }

            // Update cycle timeout for active transport states
            if (currentState.isActive() && !currentState.isInterruptible()) {
                cycleTimeoutCounter++;
                if (cycleTimeoutCounter >= cycleTimeout.get()) {
                    throw new TransportException(
                        "Transport cycle timeout",
                        TransportState.ERROR,
                TransportCondition.TransportFailureSeverity.CRITICAL
                    );
                }
            }

            // Validate coordination state
            if (currentState.requiresCoordination() && !coordinator.validateState()) {
                throw new TransportException(
                    "Lost coordination connection",
                    TransportState.ERROR,
                TransportCondition.TransportFailureSeverity.CRITICAL
                );
            }

            // Process state transitions
            validateStateTransition();

            if (currentSequence != null) {
                if (currentDelay > 0) {
                    currentDelay--;
                    return;
                }

                if (currentSequence.update()) {
                    CommandSequence.CommandStep step = currentSequence.getCurrentStep();
                    String command = currentSequence.getCurrentCommand();
                    
                    if (command != null) {
                        if (previewMode.get()) {
                            info("Would execute: " + command);
                        } else {
                            if (announceSteps.get() && step != null && !step.getDescription().isEmpty()) {
                                info("Executing: " + step.getDescription());
                            }
                            executeCommand(command);
                            
                            // Track item usage and commands
                            if (command.contains("shulker")) {
                                updateStats("shulker");
                            } else if (command.contains("pearl") || command.contains("enderpearl")) {
                                updateStats("pearl");
                            }
                            updateStats("command");
                        }
                        
                        String cmd = command.toLowerCase();
                        if (cmd.startsWith("/tp") || cmd.contains("teleport")) {
                            currentDelay = teleportDelay.get();
                        } else if (cmd.contains("ready") || cmd.startsWith("/msg")) {
                            currentDelay = readyMessageDelay.get();
                        } else if (cmd.contains("move") || cmd.contains("walk")) {
                            currentDelay = moveCommandDelay.get();
                        } else if (cmd.startsWith("/attack") || cmd.contains("kill")) {
                            currentDelay = combatCommandDelay.get();
                        } else if (cmd.startsWith("/")) {
                            currentDelay = chatCommandDelay.get();
                        } else {
                            currentDelay = defaultCommandDelay.get();
                        }

                        currentSequence.nextCommand();
                    }
                }

                if (currentSequence.isComplete()) {
                    info("Command sequence completed");
                    currentSequence = null;
                    currentRetries = 0;
                    lastFailedStep = null;
                }
                return;
            }

            if (currentState == TransportState.IDLE) return;
            
            for (AutomationFlow currentFlow : activeFlows) {
                try {
                    currentFlow.execute();
                } catch (Exception execError) {
                    error("Flow execution failed: " + execError.getMessage());
                    continue;
                }
            }
        } catch (Exception e) {
            handleSequenceError(e);
            if (currentSequence != null) {
                warning("Command sequence failed: " + e.getMessage());
                currentSequence = null;
            }
        }
    }

    private void handleSequenceError(Exception e) {
        updateStats("fail");
        if (!autoRetryCommands.get() || currentSequence == null) {
            handleError(e);
            return;
        }

        if (currentRetries < maxRetries.get()) {
            currentRetries++;
            currentDelay = retryDelay.get();
            warning("Command failed, retry attempt " + currentRetries + "/" + maxRetries.get());
            
            if (lastFailedStep == null) {
                lastFailedStep = currentSequence.getCurrentStep();
            }
            
            currentSequence.reset();
            while (currentSequence.getCurrentStep() != lastFailedStep) {
                currentSequence.nextCommand();
            }
        } else {
            error("Max retries reached, aborting sequence");
            handleError(e);
            currentSequence = null;
            currentRetries = 0;
            lastFailedStep = null;
        }
    }

    private void handleError(Exception e) {
        error("Transport error: " + e.getMessage());
        if (e instanceof TransportException) {
            TransportException te = (TransportException) e;
            setState(te.getErrorState());
        } else {
            setState(TransportState.IDLE);
        }
    }

    private void updateDistance() {
        if (mc.player != null) {
            int currentX = mc.player.getBlockX();
            int currentZ = mc.player.getBlockZ();
            
            // Calculate Manhattan distance
            totalDistance += Math.abs(currentX - lastPosX) + Math.abs(currentZ - lastPosZ);
            
            lastPosX = currentX;
            lastPosZ = currentZ;
        }
    }

    private void validateStateTransition() {
        if (!currentState.isActive()) return;

        switch (currentState) {
            case COLLECTING:
                if (!inventory.isCollecting()) {
                    setState(TransportState.WAITING_TELEPORT);
                }
                break;
            case WAITING_TELEPORT:
                if (inventory.isTeleportConfirmed()) {
                    setState(TransportState.THROWING_PEARL);
                }
                break;
            case THROWING_PEARL:
                if (inventory.isPearlThrown()) {
                    validatePearlThrow();
                    trackPearlThrow();
                    setState(TransportState.DROPPING);
                }
                break;
            case DROPPING:
                if (inventory.isDropComplete()) {
                    setState(TransportState.RETURNING);
                }
                break;
            case RETURNING:
                if (inventory.isReturnComplete()) {
                    setState(TransportState.IDLE);
                    cycleTimeoutCounter = 0;
                }
                break;
        }
    }

    public void setState(TransportState newState) {
        // Reset timeout on state change
        if (newState != currentState) {
            cycleTimeoutCounter = 0;
        }
        
        this.currentState = newState;
        info("State changed to: " + newState.getDisplayName());
    }

    public TransportConfig getConfig() {
        return config;
    }

    public TransportState getState() {
        return currentState;
    }

    private void validateTeleport() throws TransportException {
        if (currentState == TransportState.TELEPORTING) {
            if (teleportStartPos == null) {
                teleportStartPos = mc.player != null ? mc.player.getBlockPos() : null;
            }
            
            teleportTimeoutCounter++;
            if (teleportTimeoutCounter >= teleportTimeout.get()) {
                throw new TransportException("Teleport operation timed out", TransportState.TELEPORT_TIMEOUT, TransportCondition.TransportFailureSeverity.WARNING);
            }

            // Validate position change
            if (mc.player != null && teleportStartPos != null) {
                BlockPos currentPos = mc.player.getBlockPos();
                if (currentPos.equals(teleportStartPos)) {
                    warning("No position change during teleport");
                }
            }
        } else {
            teleportTimeoutCounter = 0;
            teleportStartPos = null;
        }
    }

    private void validateSafetyChecks() throws TransportException {
        // Validate slot 0 is empty before stash pickup
        if (mc.player != null && !mc.player.getInventory().getStack(0).isEmpty()) {
            throw new TransportException(
                "Slot 0 must be empty before stash pickup",
                TransportState.INVENTORY_SYNC_ERROR,
                TransportCondition.TransportFailureSeverity.WARNING
            );
        }

        // Verify pearl slot
        if (mc.player != null && !mc.player.getInventory().getStack(pearlSlot.get()).isEmpty()) {
            throw new TransportException(
                "Pearl slot must be empty",
                TransportState.PEARL_THROW_FAILED,
                TransportCondition.TransportFailureSeverity.WARNING
            );
        }

        // Check for water stuck condition
        if (mc.player != null && mc.player.isTouchingWater()) {
            waterStuckCounter++;
            if (waterStuckCounter >= waterStuckThreshold.get()) {
                throw new TransportException(
                    "Stuck in water",
                    TransportState.ERROR,
                    TransportCondition.TransportFailureSeverity.RECOVERABLE
                );
            }
        } else {
            waterStuckCounter = 0;
        }

        // Validate stash existence if enabled
        if (validateStashExistence.get() && !inventory.validateStashExists()) {
            throw new TransportException("Stash not found", TransportState.INVENTORY_SYNC_ERROR, TransportCondition.TransportFailureSeverity.WARNING);
        }
    }

    private void validatePearlThrow() {
        if (!verifyPearlThrow.get() || lastPearlPos == null || mc.player == null) return;

        BlockPos currentPos = mc.player.getBlockPos();
        if (currentPos.equals(lastPearlPos)) {
            throw new TransportException(
                "Pearl throw failed - no position change",
                TransportState.PEARL_THROW_FAILED,
                TransportCondition.TransportFailureSeverity.WARNING
            );
        }
    }

    private void updateModuleStatus() {
        if (!debugMode.get()) return;

        long now = System.currentTimeMillis();
        if (now - lastStatusUpdate < statusUpdateInterval.get()) return;

        writeStatusFile();
        lastStatusUpdate = now;
    }

    private void writeStatusFile() {
        try {
            String status = String.format(
                "{\n" +
                "  \"state\": \"%s\",\n" +
                "  \"shulkersTransported\": %d,\n" +
                "  \"enderpearlsUsed\": %d,\n" +
                "  \"failedAttempts\": %d,\n" +
                "  \"waterStuckCounter\": %d,\n" +
                "  \"lastPearlPos\": \"%s\",\n" +
                "  \"currentPhase\": \"%s\"\n" +
                "}",
                currentState.getDisplayName(),
                shulkersTransported,
                enderpearlsUsed,
                failedAttempts,
                waterStuckCounter,
                lastPearlPos != null ? lastPearlPos.toString() : "null",
                currentSequence != null ? currentSequence.getCurrentStep().getDescription() : "none"
            );

            try (java.io.FileWriter fw = new java.io.FileWriter("moduleStatus.json")) {
                fw.write(status);
            }
        } catch (Exception e) {
            error("Failed to write status file: " + e.getMessage());
        }
    }

    private void trackPearlThrow() {
        if (!trackPearlCoords.get() || mc.player == null) return;
        lastPearlPos = mc.player.getBlockPos();
    }

    public void sendTransportMessage(String message) {
        if (mc.player == null) return;
        mc.player.sendMessage(Text.literal("[Transport] " + message), false);
    }

    private void displayStats() {
        if (!showStats.get() || mc.player == null) return;

        long duration = System.currentTimeMillis() - startTime;
        String timeStr = String.format("%02d:%02d:%02d",
            duration / 3600000,
            (duration % 3600000) / 60000,
            (duration % 60000) / 1000);

        info("=== Transport Statistics ===");
        info(String.format("Shulkers Moved: %d", shulkersTransported));
        info(String.format("Enderpearls Used: %d", enderpearlsUsed));
        info(String.format("Commands Run: %d", totalCommands));
        info(String.format("Failed Attempts: %d", failedAttempts));
        info(String.format("Total Distance: %dm", totalDistance));
        info(String.format("Total Successful Transports: %d", successfulTransports));
        info(String.format("Transport Efficiency: %.2f shulkers/min",
             (duration > 0) ? (shulkersTransported * 60000.0f / duration) : 0));
        info("======================");
    }

    private void updateStats(String action) {
        switch (action) {
            case "shulker":
                shulkersTransported++;
                successfulTransports++;
                updateDistance();
                break;
            case "pearl":
                enderpearlsUsed++;
                break;
            case "command":
                totalCommands++;
                break;
            case "fail":
                failedAttempts++;
                break;
            case "success":
                successfulTransports++;
                updateDistance();
                break;
        }
        
        if (showStats.get()) {
            displayStats();
        }
    }

    private void resetStats() {
        shulkersTransported = 0;
        enderpearlsUsed = 0;
        totalCommands = 0;
        failedAttempts = 0;
        startTime = System.currentTimeMillis();
        if (showStats.get()) {
            info("Statistics reset");
        }
    }
}
