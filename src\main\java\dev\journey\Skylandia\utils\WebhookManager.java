package dev.journey.Skylandia.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import dev.journey.Skylandia.Skylandia;
import net.fabricmc.loader.api.FabricLoader;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class WebhookManager {
    private static final String WEBHOOKS_FILENAME = "webhooks.json";
    private static final File WEBHOOKS_FILE = FabricLoader.getInstance().getGameDir().resolve(Skylandia.MOD_ID).resolve(WEBHOOKS_FILENAME).toFile();
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    
    private static ConcurrentHashMap<String, StoredWebhook> webhooks = new ConcurrentHashMap<>();

    public static class StoredWebhook {
        public String name;
        public String url;
        public String discordId;
        public String serverName;
        public String channelName;
        public long lastUsed;
        public boolean enabled;

        public StoredWebhook(String name, String url, String discordId, String serverName, String channelName) {
            this.name = name;
            this.url = url;
            this.discordId = discordId;
            this.serverName = serverName;
            this.channelName = channelName;
            this.lastUsed = System.currentTimeMillis();
            this.enabled = true;
        }
    }

    public static void init() {
        loadWebhooks();
    }

    public static void addWebhook(String name, String url, String discordId, String serverName, String channelName) {
        StoredWebhook webhook = new StoredWebhook(name, url, discordId, serverName, channelName);
        webhooks.put(name, webhook);
        saveWebhooks();
    }

    public static StoredWebhook getWebhook(String name) {
        return webhooks.get(name);
    }

    public static List<StoredWebhook> getAllWebhooks() {
        return new ArrayList<>(webhooks.values());
    }

    public static void updateWebhook(String name, StoredWebhook webhook) {
        webhooks.put(name, webhook);
        saveWebhooks();
    }

    public static void removeWebhook(String name) {
        webhooks.remove(name);
        saveWebhooks();
    }

    private static void loadWebhooks() {
        try {
            if (!WEBHOOKS_FILE.exists()) {
                WEBHOOKS_FILE.getParentFile().mkdirs();
                saveWebhooks(); // Create empty file
                return;
            }

            try (Reader reader = new FileReader(WEBHOOKS_FILE)) {
                TypeToken<ConcurrentHashMap<String, StoredWebhook>> typeToken = new TypeToken<>() {};
                ConcurrentHashMap<String, StoredWebhook> loaded = GSON.fromJson(reader, typeToken.getType());
                if (loaded != null) {
                    webhooks = loaded;
                }
            }
        } catch (IOException e) {
            Skylandia.LOG.error("Failed to load webhooks: " + e.getMessage());
        }
    }

    private static void saveWebhooks() {
        try {
            WEBHOOKS_FILE.getParentFile().mkdirs();
            try (Writer writer = new FileWriter(WEBHOOKS_FILE)) {
                GSON.toJson(webhooks, writer);
            }
        } catch (IOException e) {
            Skylandia.LOG.error("Failed to save webhooks: " + e.getMessage());
        }
    }
}