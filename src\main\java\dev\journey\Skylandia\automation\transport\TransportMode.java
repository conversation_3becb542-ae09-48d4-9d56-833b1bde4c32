package dev.journey.Skylandia.automation.transport;

/**
 * Defines the operational roles for the transport system
 */
public enum TransportMode {
    /**
     * Responsible for managing the stasis chamber in the Overworld
     */
    TRANSPORTER,

    /**
     * Responsible for coordinating operations from the End
     */
    COMMANDER,
    
    /**
     * Host mode for swarm coordination
     */
    SWARM_HOST,
    
    /**
     * Worker mode for swarm operations
     */
    SWARM_WORKER;

    /**
     * Check if this mode is the primary coordinator
     * @return true if this is a coordinator mode (COMMANDER or SWARM_HOST)
     */
    public boolean isCoordinator() {
        return this == COMMANDER || this == SWARM_HOST;
    }

    /**
     * Get the opposite role
     * @return the complementary transport mode
     */
    public TransportMode getCounterpart() {
        switch(this) {
            case COMMANDER:
                return TRANSPORTER;
            case TRANSPORTER:
                return COMMANDER;
            case SWARM_HOST:
                return SWARM_WORKER;
            case SWARM_WORKER:
                return SWARM_HOST;
            default:
                return TRANSPORTER;
        }
    }

    /**
     * Get the dimension associated with this mode
     * @return "overworld" for TRANSPORTER, "end" for COMMANDER
     */
    public String getDimension() {
        return this == COMMANDER ? "end" : "overworld";
    }
}
