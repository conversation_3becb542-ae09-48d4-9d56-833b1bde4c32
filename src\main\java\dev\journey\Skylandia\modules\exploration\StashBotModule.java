package dev.journey.Skylandia.modules.exploration;

import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.systems.modules.Module;
import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.vehicle.BoatEntity;
import net.minecraft.entity.Entity;
import net.minecraft.text.Text;

import java.util.ArrayList;
import java.util.List;

public class StashBotModule extends Module {

    // All settings are registered with the default group for Meteor GUI visibility.
    private final Setting<Integer> gap = settings.getDefaultGroup().add(new IntSetting.Builder()
        .name("Gap")
        .description("Distance between scan points in blocks.")
        .defaultValue(8)
        .min(1)
        .max(64)
        .build());
    private final Setting<Integer> radius = settings.getDefaultGroup().add(new IntSetting.Builder()
        .name("Radius")
        .description("Number of scan rings (in gaps) from center.")
        .defaultValue(4)
        .min(1)
        .max(32)
        .build());
    private final Setting<Boolean> active = settings.getDefaultGroup().add(new BoolSetting.Builder()
        .name("Active")
        .description("Enable or disable the scan.")
        .defaultValue(false)
        .build());
    private final Setting<Boolean> disconnect = settings.getDefaultGroup().add(new BoolSetting.Builder()
        .name("LogOnArrival")
        .description("Disconnect when scan completes or on low health.")
        .defaultValue(false)
        .build());
    private final Setting<Integer> dishealth = settings.getDefaultGroup().add(new IntSetting.Builder()
        .name("LogOnHealth")
        .description("Health threshold to auto-disconnect if enabled.")
        .defaultValue(10)
        .min(1)
        .max(36)
        .build());
    private final Setting<Integer> boatFlyY = settings.getDefaultGroup().add(new IntSetting.Builder()
        .name("BoatFlyY")
        .description("Target Y level for boat fly (if using a boat).")
        .defaultValue(200)
        .min(50)
        .max(320)
        .build());
    private final Setting<Boolean> autoPlaceBoat = settings.getDefaultGroup().add(new BoolSetting.Builder()
        .name("Auto Place Boat")
        .description("Automatically place and mount a boat if not in one.")
        .defaultValue(true)
        .build());
    private final Setting<Boolean> detectWater = settings.getDefaultGroup().add(new BoolSetting.Builder()
        .name("Detect Water")
        .description("Detect water nearby before placing a boat.")
        .defaultValue(true)
        .build());
    private final Setting<Boolean> avoidNewChunks = settings.getDefaultGroup().add(new BoolSetting.Builder()
        .name("Avoid New Chunks")
        .description("Skips scan points in new/unexplored chunks using Xaero's minimap integration.")
        .defaultValue(true)
        .build());

    // XaeroPlus integration
    private final xaeroplus.module.impl.PaletteNewChunks paletteNewChunks =
        xaeroplus.module.ModuleManager.getModule(xaeroplus.module.impl.PaletteNewChunks.class);

    private List<BlockPos> scanBlocks;
    private BlockPos center;
    private int index;

    public StashBotModule() {
        super(Skylandia.Hunting, "StashBot", "Automatically scans territory");
    }

    private boolean validateGap(int v) { return v >= 1 && v <= 64; }
    private boolean validateRadius(int v) { return v >= 1 && v <= 32; }

    @Override
    public void onActivate() {
        if (mc.player == null) return;
        center = mc.player.getBlockPos();
        resetScan();
        ChatUtils.info("[StashBot] Scan started.");
    }

    @Override
    public void onDeactivate() {
        center = null;
        resetScan();
        ChatUtils.info("[StashBot] Scan stopped.");
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (!isActive() || mc.player == null) return;
        if (center == null) center = mc.player.getBlockPos();
        updateScan();
    }

    private void resetScan() {
        scanBlocks = blocks(gap.get() / 16, radius.get());
        index = 0;
    }

    private void updateScan() {
        if (mc.player != null && active.get() && center != null && scanBlocks != null && !scanBlocks.isEmpty()) {
            BlockPos target = scanBlocks.get(index);

            // Boat fly logic
            if (mc.player.hasVehicle() && mc.player.getVehicle() instanceof BoatEntity) {
                Entity boat = mc.player.getVehicle();
                if (boat != null) {
                    // Ascend to target Y
                    if (boat.getY() < boatFlyY.get()) {
                        mc.options.jumpKey.setPressed(true);
                        ChatUtils.info("[StashBot] Ascending to Y " + boatFlyY.get());
                        return;
                    } else {
                        mc.options.jumpKey.setPressed(false);
                    }
                }
            }

            lookAt(target);
            if (distanceToBlock(target) <= 5 && index < scanBlocks.size() - 1) {
                index++;
            } else if (index >= scanBlocks.size() - 1) {
                finishScan();
            }
        }
    }

    private void finishScan() {
        active.set(false);
        ChatUtils.info("[StashBot] Scan complete.");
        if (shouldDisconnect()) {
            ChatUtils.info("[StashBot] Disconnecting...");
            disconnectNow();
        }
    }

    private boolean shouldDisconnect() {
        if (disconnect.get()) return true;
        if (mc.player == null) return false;
        return mc.player.getHealth() <= dishealth.get() && disconnect.get();
    }

    private ArrayList<BlockPos> blocks(int gap, int radius) {
        ArrayList<BlockPos> blockies = new ArrayList<>();
        for (int i = 2; i <= radius; i++) {
            addBlockPositions(blockies, i);
        }
        applyGap(blockies, gap);
        centerBlocks(blockies);
        return blockies;
    }

    private void addBlockPositions(ArrayList<BlockPos> blockies, int distance) {
        blockies.add(new BlockPos(distance, 0, distance));
        blockies.add(new BlockPos(distance, 0, -distance));
        blockies.add(new BlockPos(-distance, 0, -distance));
        blockies.add(new BlockPos(-distance, 0, distance));
    }

    private void applyGap(ArrayList<BlockPos> blockies, int gap) {
        blockies.replaceAll(blockPos -> new BlockPos(blockPos.getX() * gap, 0, blockPos.getZ() * gap));
    }

    private void centerBlocks(ArrayList<BlockPos> blockies) {
        if (center == null) return;
        blockies.replaceAll(blockPos -> new BlockPos(blockPos.getX() + center.getX(), 0, blockPos.getZ() + center.getZ()));
    }

    private void lookAt(BlockPos block) {
        if (mc.player == null) return;
        double yaw = calculateYaw(block);
        mc.player.setYaw((float) yaw);
    }

    private double calculateYaw(BlockPos block) {
        PlayerEntity player = mc.player;
        if (player == null) return 0;
        double dx = block.getX() - player.getX();
        double dz = block.getZ() - player.getZ();
        return Math.toDegrees(Math.atan2(dz, dx)) + 90f;
    }

    private double distanceToBlock(BlockPos block) {
        if (mc.player == null) return Double.MAX_VALUE;
        return mc.player.squaredDistanceTo(block.getX(), mc.player.getY(), block.getZ());
    }

    private void disconnectNow() {
        if (mc.getNetworkHandler() != null) {
            mc.getNetworkHandler().getConnection().disconnect(Text.literal("StashBot: Scan complete"));
        }
    }
}