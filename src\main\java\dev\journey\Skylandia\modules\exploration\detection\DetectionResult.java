package dev.journey.Skylandia.modules.exploration.detection;

import dev.journey.Skylandia.utils.BlockPosUtils;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

public class DetectionResult {
    public enum DetectionType {
        STRUCTURE,   // For built structures like houses, tunnels, etc.
        ENTITY,      // For entity-related detections
        ANOMALY,     // For unusual patterns or modifications
        PATTERN      // For regular patterns that might indicate player activity
    }

    private final String description;
    private final BlockPos position;
    private final Vec3d exactPosition;
    private final DetectionType type;
    private final float confidence;

    public DetectionResult(String description, BlockPos position, Vec3d exactPosition, DetectionType type, float confidence) {
        this.description = description;
        this.position = position;
        this.exactPosition = exactPosition;
        this.type = type;
        this.confidence = confidence;
    }

    public String getDescription() {
        return description;
    }

    public BlockPos getPosition() {
        return position;
    }

    public Vec3d getExactPosition() {
        return exactPosition;
    }

    public DetectionType getType() {
        return type;
    }

    public float getConfidence() {
        return confidence;
    }

    // Factory methods using BlockPosUtils
    public static DetectionResult fromBlockPos(String description, BlockPos pos, DetectionType type, float confidence) {
        return new DetectionResult(
            description,
            pos,
            BlockPosUtils.toVec3d(pos),
            type,
            confidence
        );
    }

    public static DetectionResult fromVec3d(String description, Vec3d pos, DetectionType type, float confidence) {
        return new DetectionResult(
            description,
            BlockPosUtils.fromVec3d(pos),
            pos,
            type,
            confidence
        );
    }

    public static DetectionResult fromPositions(String description, BlockPos blockPos, Vec3d exactPos, DetectionType type, float confidence) {
        return new DetectionResult(
            description,
            blockPos,
            exactPos,
            type,
            confidence
        );
    }

    @Override
    public String toString() {
        return String.format("%s at %s (confidence: %.2f)", description, position.toString(), confidence);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DetectionResult)) return false;
        DetectionResult that = (DetectionResult) o;
        return position.equals(that.position) && type == that.type;
    }

    @Override
    public int hashCode() {
        int result = position.hashCode();
        result = 31 * result + type.hashCode();
        return result;
    }
}
