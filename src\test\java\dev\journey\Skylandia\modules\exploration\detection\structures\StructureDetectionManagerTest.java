package dev.journey.Skylandia.modules.exploration.detection.structures;

import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class StructureDetectionManagerTest {

    @Mock
    private World world;

    @Mock
    private Chunk chunk;

    @Mock
    private StructureDetector mockDetector1;

    @Mock
    private StructureDetector mockDetector2;

    private StructureDetectionManager manager;
    private static final int MAX_SEARCH_RADIUS = 48;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        manager = new StructureDetectionManager(world, MAX_SEARCH_RADIUS);

        // Configure mock detectors
        when(mockDetector1.getStructureType()).thenReturn(StructureType.TRIAL_CHAMBER);
        when(mockDetector2.getStructureType()).thenReturn(StructureType.DUNGEON);
    }

    @Test
    void registerDetector_AddsToRegisteredTypes() {
        manager.registerDetector(mockDetector1);

        Set<StructureType> types = manager.getRegisteredTypes();
        assertTrue(types.contains(StructureType.TRIAL_CHAMBER));
        assertEquals(1, types.size());
    }

    @Test
    void setStructureEnabled_DisablesDetection() {
        manager.registerDetector(mockDetector1);
        manager.setStructureEnabled(StructureType.TRIAL_CHAMBER, false);

        when(mockDetector1.detectInChunk(chunk)).thenReturn(Collections.emptyList());
        List<StructureMatch> matches = manager.detectStructures(chunk);

        verify(mockDetector1, never()).detectInChunk(chunk);
        assertTrue(matches.isEmpty());
    }

    @Test
    void setConfidenceThreshold_FiltersMatchesBelowThreshold() {
        manager.registerDetector(mockDetector1);
        manager.setConfidenceThreshold(StructureType.TRIAL_CHAMBER, 0.8);

        StructureMatch match1 = new StructureMatch(StructureType.TRIAL_CHAMBER, null, 0.9, null, "High confidence");
        StructureMatch match2 = new StructureMatch(StructureType.TRIAL_CHAMBER, null, 0.7, null, "Low confidence");
        
        when(mockDetector1.detectInChunk(chunk)).thenReturn(Arrays.asList(match1, match2));
        
        List<StructureMatch> matches = manager.detectStructures(chunk);
        
        assertEquals(1, matches.size());
        assertEquals(0.9, matches.get(0).confidence());
    }

    @Test
    void detectStructures_MultipleDetectors_ReturnsAllValidMatches() {
        manager.registerDetector(mockDetector1);
        manager.registerDetector(mockDetector2);

        StructureMatch match1 = new StructureMatch(StructureType.TRIAL_CHAMBER, null, 0.9, null, "Trial Chamber");
        StructureMatch match2 = new StructureMatch(StructureType.DUNGEON, null, 0.85, null, "Dungeon");
        
        when(mockDetector1.detectInChunk(chunk)).thenReturn(Collections.singletonList(match1));
        when(mockDetector2.detectInChunk(chunk)).thenReturn(Collections.singletonList(match2));
        
        List<StructureMatch> matches = manager.detectStructures(chunk);
        
        assertEquals(2, matches.size());
        assertTrue(matches.stream().anyMatch(m -> m.type() == StructureType.TRIAL_CHAMBER));
        assertTrue(matches.stream().anyMatch(m -> m.type() == StructureType.DUNGEON));
    }

    @Test
    void clearDetectors_RemovesAllDetectors() {
        manager.registerDetector(mockDetector1);
        manager.registerDetector(mockDetector2);
        
        assertFalse(manager.getRegisteredTypes().isEmpty());
        
        manager.clearDetectors();
        
        assertTrue(manager.getRegisteredTypes().isEmpty());
    }

    @Test
    void getConfidenceThreshold_ReturnsDefaultForUnregisteredType() {
        double threshold = manager.getConfidenceThreshold(StructureType.VILLAGE);
        assertEquals(StructureType.VILLAGE.getDefaultConfidence(), threshold);
    }

    @Test
    void setConfidenceThreshold_InvalidValues_DoesNotUpdate() {
        manager.registerDetector(mockDetector1);
        
        double originalThreshold = manager.getConfidenceThreshold(StructureType.TRIAL_CHAMBER);
        
        manager.setConfidenceThreshold(StructureType.TRIAL_CHAMBER, -0.1); // Invalid negative
        assertEquals(originalThreshold, manager.getConfidenceThreshold(StructureType.TRIAL_CHAMBER));
        
        manager.setConfidenceThreshold(StructureType.TRIAL_CHAMBER, 1.1); // Invalid > 1
        assertEquals(originalThreshold, manager.getConfidenceThreshold(StructureType.TRIAL_CHAMBER));
    }

    @Test
    void detectStructures_NoRegisteredDetectors_ReturnsEmptyList() {
        List<StructureMatch> matches = manager.detectStructures(chunk);
        assertTrue(matches.isEmpty());
    }

    @Test
    void detectStructures_AllDetectorsDisabled_ReturnsEmptyList() {
        manager.registerDetector(mockDetector1);
        manager.registerDetector(mockDetector2);
        
        manager.setStructureEnabled(StructureType.TRIAL_CHAMBER, false);
        manager.setStructureEnabled(StructureType.DUNGEON, false);
        
        List<StructureMatch> matches = manager.detectStructures(chunk);
        
        assertTrue(matches.isEmpty());
        verify(mockDetector1, never()).detectInChunk(chunk);
        verify(mockDetector2, never()).detectInChunk(chunk);
    }
}