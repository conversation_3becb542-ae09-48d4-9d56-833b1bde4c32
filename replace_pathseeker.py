import os
import re
from typing import <PERSON><PERSON>, List

def determine_case(match: str, line: str, filename: str) -> str:
    """Determine the correct case for replacement based on context"""
    if match.lower() == 'skylandia':
        # Package declarations
        if 'package' in line.lower() or 'import' in line.lower():
            return 'Skylandia'
        # Class names or similar PascalCase contexts
        if any(x in line for x in ['class ', 'interface ', 'enum ']):
            return 'Skylandia'
        # Config or similar contexts - transform case pattern
        if match.isupper():
            return 'SKYLANDIA'
        if match.islower():
            return 'skylandia'
        # Default to PascalCase for most contexts
        if match[0].isupper():
            return 'Skylandia'
        return 'skylandia'
    return match

def process_file(filepath: str) -> Tuple[int, List[str]]:
    """Process a single file and return number of replacements and log entries"""
    if not os.path.isfile(filepath):
        return 0, []
        
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # Skip binary files
        return 0, []
    
    # Skip if the file appears to be corrupted with placeholders
    if "[Rest of" in content or "[All static" in content:
        try:
            backup_path = filepath + '.bak'
            if os.path.exists(backup_path):
                with open(backup_path, 'r', encoding='utf-8') as f:
                    content = f.read()
        except:
            return 0, []
    
    replacement_count = 0
    log_entries = []
    
    def replace_func(match):
        nonlocal replacement_count
        original = match.group(0)
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.start())
        if line_end == -1:
            line_end = len(content)
        line = content[line_start:line_end]
        
        # Skip placeholder comments
        if "[Rest of" in line or "[All static" in line:
            return original
            
        replacement = determine_case(original, line, filepath)
        if original != replacement:
            replacement_count += 1
            log_entries.append(f"In {filepath}:")
            log_entries.append(f"  Original line: {line.strip()}")
            log_entries.append(f"  Replaced: {original} → {replacement}")
            log_entries.append("")
        return replacement

    # Create pattern that matches skylandia case-insensitively
    pattern = re.compile(r'skylandia', re.IGNORECASE)
    new_content = pattern.sub(replace_func, content)
    
    # Skip writing if no changes were made
    if replacement_count > 0:
        # Create backup of original file
        backup_path = filepath + '.bak'
        if not os.path.exists(backup_path):
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
    
    return replacement_count, log_entries

def should_process_file(filepath: str) -> bool:
    """Determine if a file should be processed based on extension and path"""
    # Skip binary and build files
    skip_extensions = {'.jar', '.class', '.bin', '.lock', '.probe', '.png', '.git'}
    skip_dirs = {'.git', 'build', '.gradle'}
    
    # Check directory
    for skip_dir in skip_dirs:
        if skip_dir in filepath.split(os.sep):
            return False
            
    # Check extension
    _, ext = os.path.splitext(filepath)
    return ext not in skip_extensions

def main():
    """Main function to walk directory and process files"""
    total_replacements = 0
    all_logs = []
    
    # Walk through all files in current directory
    for root, _, files in os.walk('.'):
        for file in files:
            filepath = os.path.join(root, file)
            
            # Skip files that shouldn't be processed
            if not should_process_file(filepath):
                continue
                
            try:
                replacements, logs = process_file(filepath)
                total_replacements += replacements
                all_logs.extend(logs)
            except Exception as e:
                print(f"Error processing {filepath}: {str(e)}")
    
    # Write log file
    with open('replacement_log.txt', 'w', encoding='utf-8') as f:
        f.write(f"Total replacements made: {total_replacements}\n\n")
        f.write("\n".join(all_logs))
    
    print(f"Processing complete. Total replacements: {total_replacements}")
    print("Check replacement_log.txt for details.")

if __name__ == '__main__':
    main()
