package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import meteordevelopment.meteorclient.events.game.GameLeftEvent;
import meteordevelopment.meteorclient.events.game.OpenScreenEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.network.PacketUtils;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority;
import net.minecraft.client.gui.screen.DisconnectedScreen;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.*;
import net.minecraft.network.packet.s2c.play.*;
import net.minecraft.item.Items;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Item;
import net.minecraft.screen.slot.SlotActionType;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import org.lwjgl.system.NativeResource;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

public class duprexion extends Module {
    private enum DupeMode {
        RECORD("Record", "Records packet sequences for analysis", "§b"),
        DISCOVER("Discover", "Tests known dupe patterns on items", "§d"), 
        TRIDENT("Trident", "Right click + swap timing dupe", "§a"),
        ITEM_FRAME("Item Frame", "Frame placement exploit", "§e"),
        DROP("Drop", "Drop timing manipulation", "§6");

        final String title;
        final String description;
        final String color;

        DupeMode(String title, String description, String color) {
            this.title = title;
            this.description = description;
            this.color = color;
        }
    }

    // Setting Groups
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTiming = settings.createGroup("Timing");
    private final SettingGroup sgPackets = settings.createGroup("Packets");
    private final SettingGroup sgTrident = settings.createGroup("Trident");
    private final SettingGroup sgDisplay = settings.createGroup("Display");
    private final SettingGroup sgDebug = settings.createGroup("Debug");

    // General Settings
    private final Setting<DupeMode> mode = sgGeneral.add(new EnumSetting.Builder<DupeMode>()
        .name("mode")
        .description("Operating mode")
        .defaultValue(DupeMode.RECORD)
        .onChanged(this::onModeChange)
        .build()
    );

    private final Setting<Boolean> autoDisable = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-disable")
        .description("Automatically disable after completing sequence")
        .defaultValue(false)
        .build()
    );

    private final Setting<List<Item>> targetItems = sgGeneral.add(new ItemListSetting.Builder()
        .name("target-items")
        .description("Items to test in discovery mode")
        .defaultValue(List.of(Items.TRIDENT, Items.SHULKER_BOX))
        .build()
    );

    // Timing Settings
    private final Setting<Integer> baseDelay = sgTiming.add(new IntSetting.Builder()
        .name("base-delay")
        .description("Base delay between actions (ms)")
        .defaultValue(500)
        .range(50, 2000)
        .sliderRange(50, 2000)
        .build()
    );

    private final Setting<Integer> variance = sgTiming.add(new IntSetting.Builder()
        .name("variance")
        .description("Random delay variance (ms)")
        .defaultValue(50)
        .range(0, 500)
        .build()
    );

    private final Setting<Integer> maxAttempts = sgTiming.add(new IntSetting.Builder()
        .name("max-attempts")
        .description("Maximum attempts before auto-disable (0 = infinite)")
        .defaultValue(0)
        .min(0)
        .build()
    );

    // Packet Settings
    private final Setting<Boolean> cancelOutgoing = sgPackets.add(new BoolSetting.Builder()
        .name("cancel-outgoing")
        .description("Cancel outgoing packets during sequence")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> cancelIncoming = sgPackets.add(new BoolSetting.Builder()
        .name("cancel-incoming")
        .description("Cancel incoming packets during sequence")
        .defaultValue(false)
        .build()
    );

    // Trident Settings
    private final Setting<Boolean> swapBack = sgTrident.add(new BoolSetting.Builder()
        .name("swap-back")
        .description("Swap trident back after sequence")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> dropDuped = sgTrident.add(new BoolSetting.Builder()
        .name("drop-duped")
        .description("Drop duped tridents")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> durabilityCheck = sgTrident.add(new BoolSetting.Builder()
        .name("durability-check")
        .description("Only use tridents above durability threshold")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> minDurability = sgTrident.add(new IntSetting.Builder()
        .name("min-durability")
        .description("Minimum trident durability %")
        .defaultValue(20)
        .range(1, 100)
        .visible(durabilityCheck::get)
        .build()
    );

    // Display Settings
    private final Setting<Boolean> showHUD = sgDisplay.add(new BoolSetting.Builder()
        .name("show-hud")
        .description("Show info in HUD")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> showToasts = sgDisplay.add(new BoolSetting.Builder()
        .name("show-toasts")
        .description("Show status messages")
        .defaultValue(true)
        .build()
    );

    // Debug Settings
    private final Setting<Boolean> debugLog = sgDebug.add(new BoolSetting.Builder()
        .name("debug-log")
        .description("Log detailed debug info")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> catchExceptions = sgDebug.add(new BoolSetting.Builder()
        .name("catch-exceptions")
        .description("Prevent crashes from packet errors")
        .defaultValue(true)
        .build()
    );

    // State
    private final List<TimedTask> scheduledTasks = new CopyOnWriteArrayList<>();
    private final List<RecordedPacket> recordedPackets = new ArrayList<>();
    private boolean cancel = true;
    private boolean isRecording = false;
    private int successCount = 0;
    private int totalAttempts = 0;
    private long lastActionTime = 0;
    private Random random = new Random();

    public duprexion() {
        super(Skylandia.Automation, "duprexion", "Advanced packet manipulation for dupes");
    }

    private static class TimedTask {
        private final long executeTime;
        private final Runnable task;

        TimedTask(long executeTime, Runnable task) {
            this.executeTime = executeTime;  
            this.task = task;
        }
    }

    private static class RecordedPacket {
        private final Packet<?> packet;
        private final long timeOffset;

        RecordedPacket(Packet<?> packet, long timeOffset) {
            this.packet = packet;
            this.timeOffset = timeOffset;
        }
    }

    @Override
    public void onActivate() {
        if (mc.player == null) return;

        resetStats();
        scheduledTasks.clear();
        recordedPackets.clear();
        cancel = true;

        DupeMode current = mode.get();
        info(current.color + "Activated: " + current.title);
        info("§7" + current.description);

        switch (current) {
            case RECORD -> startRecording();
            case DISCOVER -> startDiscovery();
            case TRIDENT -> setupTridentDupe();
            default -> info("§cSelected mode not implemented");
        }
    }

    @Override 
    public void onDeactivate() {
        scheduledTasks.clear();
        if (isRecording) {
            stopRecording();
        }
        if (totalAttempts > 0) {
            info(String.format("§7Stats: §f%d§7/§f%d §7(§f%.1f%%§7)", 
                successCount, totalAttempts,
                (successCount * 100.0f) / totalAttempts));
        }
    }

    private void onModeChange(DupeMode newMode) {
        if (isActive()) {
            toggle();
            mode.set(newMode);
            toggle();
        }
    }

    private void resetStats() {
        successCount = 0;
        totalAttempts = 0;
        lastActionTime = 0;
        cancel = true;
        isRecording = false;
    }

    private void startRecording() {
        info("§bStarting packet recording");
        recordedPackets.clear();
        lastActionTime = System.currentTimeMillis();
        isRecording = true;

        if (showToasts.get()) {
            info("§7Use items/slots to record sequence");
        }
    }

    private void stopRecording() {
        isRecording = false;
        if (recordedPackets.isEmpty()) {
            info("§cNo packets were recorded");
            return;
        }

        info("§aRecorded §f" + recordedPackets.size() + "§a packets");
        saveRecording();
    }

    private void saveRecording() {
        if (recordedPackets.isEmpty()) return;

        try {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String filename = "duprexion_" + timestamp + ".txt";
            File recordingsDir = new File("duprexion");
            recordingsDir.mkdir();

            PrintWriter writer = new PrintWriter(new FileWriter(new File(recordingsDir, filename)));
            writer.println("# Duprexion Recording " + timestamp);
            writer.println("# Total packets: " + recordedPackets.size());
            writer.println();

            for (RecordedPacket packet : recordedPackets) {
                writer.printf("%6dms : %s%n", 
                    packet.timeOffset,
                    packet.packet.getClass().getSimpleName()
                );
            }

            writer.close();
            info("§aSaved recording to §f" + filename);
        } catch (IOException e) {
            error("§cFailed to save recording: " + e.getMessage());
            if (debugLog.get()) {
                e.printStackTrace();
            }
        }
    }

    private void startDiscovery() {
        info("§dStarting discovery mode");
        int testedItems = 0;

        for (int slot = 0; slot < 9; slot++) {
            ItemStack stack = mc.player.getInventory().getStack(slot);
            if (stack.isEmpty()) continue;

            if (targetItems.get().contains(stack.getItem())) {
                info("§7Testing §f" + stack.getName().getString() + " §7in slot §f" + slot);
                queuePatternTests(slot, stack.getItem());
                testedItems++;
            }
        }

        if (testedItems == 0) {
            error("§cNo target items found in hotbar");
            if (autoDisable.get()) {
                toggle();
            }
        }
    }

    private void queuePatternTests(int slot, Item item) {
        int patternDelay = baseDelay.get();
        
        // Pattern 1: Right click + swap
        queuePattern("basic-interact", slot, patternDelay, () -> {
            mc.player.getInventory().selectedSlot = slot;
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        });

        // Pattern 2: Quick slot moves
        queuePattern("slot-cycle", slot, patternDelay * 2, () -> {
            for (int i = 0; i < 3; i++) {
                scheduleTask(() -> {
                    if (mc.player.currentScreenHandler != null) {
                        mc.interactionManager.clickSlot(
                            mc.player.currentScreenHandler.syncId,
                            36 + slot, 0,
                            SlotActionType.QUICK_MOVE,
                            mc.player
                        );
                    }
                }, i * 50);
            }
        });

        // Pattern 3: Drop + pickup
        queuePattern("drop-pickup", slot, patternDelay * 3, () -> {
            if (mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    36 + slot, 0,
                    SlotActionType.THROW,
                    mc.player
                );
                
                scheduleTask(() -> {
                    mc.interactionManager.clickSlot(
                        mc.player.currentScreenHandler.syncId,
                        36 + slot, 0,
                        SlotActionType.PICKUP,
                        mc.player
                    );
                }, 100);
            }
        });

        // Pattern 4: Hold + swap
        queuePattern("hold-swap", slot, patternDelay * 4, () -> {
            if (mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    36 + slot, 0,
                    SlotActionType.PICKUP,
                    mc.player
                );

                scheduleTask(() -> {
                    mc.interactionManager.clickSlot(
                        mc.player.currentScreenHandler.syncId,
                        44, 0,
                        SlotActionType.SWAP,
                        mc.player
                    );
                }, 50);

                scheduleTask(() -> {
                    mc.interactionManager.clickSlot(
                        mc.player.currentScreenHandler.syncId,
                        36 + slot, 0,
                        SlotActionType.PICKUP,
                        mc.player
                    );
                }, 100);
            }
        });

        // Item-specific patterns
        if (item == Items.TRIDENT) {
            queuePattern("trident-special", slot, patternDelay * 5, () -> {
                setupTridentDupe();
            });
        }
    }

    private void queuePattern(String name, int slot, int delay, Runnable pattern) {
        scheduleTask(() -> {
            if (debugLog.get()) {
                info("§7Testing pattern: §f" + name);
            }
            pattern.run();
        }, delay);
    }

    private void setupTridentDupe() {
        if (!hasTridentInHotbar()) {
            error("§cNo trident found in hotbar");
            toggle();
            return;
        }

        int slot = getBestTridentSlot();
        if (slot == -1 || (durabilityCheck.get() && getTridentDurabilityPercent(slot) < minDurability.get())) {
            error("§cNo suitable trident found");
            toggle();
            return;
        }

        mc.player.getInventory().selectedSlot = slot;
        cancel = true;

        if (maxAttempts.get() > 0 && totalAttempts >= maxAttempts.get()) {
            info("§7Reached maximum attempts (" + maxAttempts.get() + ")");
            if (autoDisable.get()) {
                toggle();
            }
            return;
        }

        int tickDelay = baseDelay.get();

        // Right click trident
        scheduleTask(() -> {
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
            if (debugLog.get()) {
                info("§7Step 1: Right click trident");
            }
        }, tickDelay);

        // Swap sequence
        scheduleTask(() -> {
            if (mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    44, 0,
                    SlotActionType.SWAP,
                    mc.player
                );
                if (debugLog.get()) {
                    info("§7Step 2: First swap");
                }
            }
        }, tickDelay + getAdjustedDelay(100));

        scheduleTask(() -> {
            if (mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    36 + slot, 0,
                    SlotActionType.SWAP,
                    mc.player
                );
                if (debugLog.get()) {
                    info("§7Step 3: Second swap");
                }
            }
        }, tickDelay + getAdjustedDelay(200));

        scheduleTask(() -> {
            if (mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    3, 0,
                    SlotActionType.SWAP,
                    mc.player
                );
                if (debugLog.get()) {
                    info("§7Step 4: Final swap");
                }
            }
        }, tickDelay + getAdjustedDelay(300));

        // Release and cleanup
        scheduleTask(() -> {
            mc.getNetworkHandler().sendPacket(new PlayerActionC2SPacket(
                PlayerActionC2SPacket.Action.RELEASE_USE_ITEM,
                BlockPos.ORIGIN,
                Direction.DOWN,
                0
            ));

            if (dropDuped.get() && mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    44, 0,
                    SlotActionType.THROW,
                    mc.player
                );
            }

            if (swapBack.get() && mc.player.currentScreenHandler != null) {
                mc.interactionManager.clickSlot(
                    mc.player.currentScreenHandler.syncId,
                    36 + slot, 0,
                    SlotActionType.SWAP,
                    mc.player
                );
            }

            if (debugLog.get()) {
                info("§7Step 5: Release and cleanup");
            }

            totalAttempts++;
            cancel = true;
        }, tickDelay + getAdjustedDelay(400));

        // Queue next attempt if not at max
        if (maxAttempts.get() == 0 || totalAttempts < maxAttempts.get()) {
            scheduleTask(() -> setupTridentDupe(), tickDelay + getAdjustedDelay(500));
        } else if (autoDisable.get()) {
            scheduleTask(() -> toggle(), tickDelay + getAdjustedDelay(600));
        }
    }

    private int getAdjustedDelay(int base) {
        return base + (variance.get() > 0 ? random.nextInt(variance.get()) : 0);
    }

    private void scheduleTask(Runnable task, long delayMs) {
        scheduledTasks.add(new TimedTask(System.currentTimeMillis() + delayMs, task));
    }

    private boolean hasTridentInHotbar() {
        return getBestTridentSlot() != -1;
    }

    private int getBestTridentSlot() {
        int bestSlot = -1;
        int lowestDamage = Integer.MAX_VALUE;

        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.isOf(Items.TRIDENT)) {
                int damage = stack.getDamage();
                if (damage < lowestDamage) {
                    bestSlot = i;
                    lowestDamage = damage;
                }
            }
        }

        return bestSlot;
    }

    private float getTridentDurabilityPercent(int slot) {
        ItemStack trident = mc.player.getInventory().getStack(slot);
        if (!trident.isOf(Items.TRIDENT)) return 0;
        return 100f * (trident.getMaxDamage() - trident.getDamage()) / trident.getMaxDamage();
    }

    @EventHandler(priority = EventPriority.HIGHEST + 1)
    private void onSendPacket(PacketEvent.Send event) {
        if (!cancelOutgoing.get()) return;

        if (event.packet instanceof ClientCommandC2SPacket
            || event.packet instanceof PlayerMoveC2SPacket
            || event.packet instanceof CloseHandledScreenC2SPacket) {
            return;
        }

        if (!(event.packet instanceof ClickSlotC2SPacket)
            && !(event.packet instanceof PlayerActionC2SPacket)) {
            return;
        }

        if (!cancel) return;

        try {
            if (isRecording) {
                long currentTime = System.currentTimeMillis();
                recordedPackets.add(new RecordedPacket(
                    event.packet,
                    currentTime - lastActionTime
                ));
                
                if (debugLog.get()) {
                    info("§7Recorded: §f" + event.packet.getClass().getSimpleName() + 
                         " §7(+" + (currentTime - lastActionTime) + "ms)");
                }
                
                lastActionTime = currentTime;
            }

            totalAttempts++;
            event.cancel();
        } catch (Exception e) {
            if (catchExceptions.get()) {
                error("§cPacket error: " + e.getMessage());
                if (debugLog.get()) {
                    e.printStackTrace();
                }
            }
        }
    }

    @EventHandler
    private void onReceivePacket(PacketEvent.Receive event) {
        if (!cancelIncoming.get()) return;

        if (cancel && (
            event.packet instanceof InventoryS2CPacket ||
            event.packet instanceof EntityStatusS2CPacket ||
            event.packet instanceof BlockUpdateS2CPacket
        )) {
            event.cancel();
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null) return;

        long currentTime = System.currentTimeMillis();
        List<TimedTask> tasksToExecute = new ArrayList<>();

        for (TimedTask task : scheduledTasks) {
            if (task.executeTime <= currentTime) {
                tasksToExecute.add(task);
            }
        }

        for (TimedTask task : tasksToExecute) {
            try {
                task.task.run();
                scheduledTasks.remove(task);
            } catch (Exception e) {
                if (catchExceptions.get()) {
                    error("§cTask error: " + e.getMessage());
                    if (debugLog.get()) {
                        e.printStackTrace();
                    }
                }
                scheduledTasks.remove(task);
            }
        }

        if (maxAttempts.get() > 0 && totalAttempts >= maxAttempts.get() && autoDisable.get()) {
            info("§7Max attempts reached, disabling...");
            toggle();
        }
    }

    @EventHandler
    private void onGameLeft(GameLeftEvent event) {
        toggle();
    }

    @EventHandler
    private void onScreenOpen(OpenScreenEvent event) {
        if (event.screen instanceof DisconnectedScreen) {
            toggle();
        }
    }

    @Override
    public String getInfoString() {
        if (!showHUD.get()) return null;

        DupeMode current = mode.get();
        StringBuilder info = new StringBuilder(current.color + current.title);

        if (totalAttempts > 0) {
            info.append(String.format(" §7[§f%d§7/§f%d§7]", successCount, totalAttempts));
        }

        return info.toString();
    }
}
