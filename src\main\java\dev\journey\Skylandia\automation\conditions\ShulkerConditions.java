package dev.journey.Skylandia.automation.conditions;

import net.minecraft.block.BlockState;
import net.minecraft.block.ShulkerBoxBlock;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.DyeColor;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;

public class ShulkerConditions {
    private static final MinecraftClient mc = MinecraftClient.getInstance();
    
    public static Condition shulkerNearby(int range) {
        return flow -> {
            if (mc.player == null || mc.world == null) return false;
            
            Vec3d pos = mc.player.getPos();
            BlockPos playerPos = mc.player.getBlockPos();
            
            // Search in a cube around the player
            for (int x = -range; x <= range; x++) {
                for (int y = -range; y <= range; y++) {
                    for (int z = -range; z <= range; z++) {
                        BlockPos checkPos = playerPos.add(x, y, z);
                        BlockState state = mc.world.getBlockState(checkPos);
                        
                        if (state.getBlock() instanceof ShulkerBoxBlock) {
                            // Store the position for later use
                            flow.setVariable("lastShulkerPos", checkPos);
                            return true;
                        }
                    }
                }
            }
            return false;
        };
    }
    
    public static Condition shulkerOfColor(DyeColor color) {
        return flow -> {
            if (mc.player == null || mc.world == null) return false;
            
            Object lastPosObj = flow.getVariable("lastShulkerPos");
            if (!(lastPosObj instanceof BlockPos)) return false;
            
            BlockPos pos = (BlockPos) lastPosObj;
            BlockState state = mc.world.getBlockState(pos);
            
            if (state.getBlock() instanceof ShulkerBoxBlock) {
                ShulkerBoxBlock shulker = (ShulkerBoxBlock) state.getBlock();
                return shulker.getColor() == color;
            }
            return false;
        };
    }
    
    public static Condition canReachShulker() {
        return flow -> {
            if (mc.player == null || mc.world == null) return false;
            
            Object lastPosObj = flow.getVariable("lastShulkerPos");
            if (!(lastPosObj instanceof BlockPos)) return false;
            
            BlockPos pos = (BlockPos) lastPosObj;
            Vec3d playerEyes = mc.player.getEyePos();
            Vec3d blockCenter = Vec3d.ofCenter(pos);
            
            // Check if within reach distance
            double distance = playerEyes.distanceTo(blockCenter);
            double reachDistance = mc.player.isCreative() ? 5.0 : 4.5;
            return distance <= reachDistance;
        };
    }
    
    public static Condition isShulkerEmpty() {
        // TODO: Implement inventory checking once we have container access
        return flow -> false;
    }
    
    public static Condition isShulkerFull() {
        // TODO: Implement inventory checking once we have container access
        return flow -> false;
    }
}