package dev.journey.Skylandia.modules.exploration.detection.analyzers;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import dev.journey.Skylandia.utils.BlockPosUtils;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.MiningToolItem;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EntityEquipmentAnalyzer {
    private final World world;
    private static final float EQUIPMENT_THRESHOLD = 0.5f;
    private static final double ANALYSIS_RADIUS = 32.0;

    public EntityEquipmentAnalyzer(World world) {
        this.world = world;
    }

    public List<DetectionResult> analyzeEntityEquipment(BlockPos center) {
        List<DetectionResult> results = new ArrayList<>();
        Box searchBox = new Box(
            center.getX() - ANALYSIS_RADIUS,
            center.getY() - ANALYSIS_RADIUS,
            center.getZ() - ANALYSIS_RADIUS,
            center.getX() + ANALYSIS_RADIUS,
            center.getY() + ANALYSIS_RADIUS,
            center.getZ() + ANALYSIS_RADIUS
        );

        List<LivingEntity> entities = world.getEntitiesByClass(
            LivingEntity.class,
            searchBox,
            entity -> true
        );

        Map<LivingEntity, Float> equipmentScores = new HashMap<>();

        for (Entity entity : entities) {
            if (entity instanceof LivingEntity) {
                LivingEntity livingEntity = (LivingEntity) entity;
                float score = evaluateEquipment(livingEntity);
                
                if (score > EQUIPMENT_THRESHOLD) {
                    equipmentScores.put(livingEntity, score);
                }
            }
        }

        // Create detection results for significant equipment findings
        for (Map.Entry<LivingEntity, Float> entry : equipmentScores.entrySet()) {
            LivingEntity entity = entry.getKey();
            float score = entry.getValue();
            
            String description = generateEquipmentDescription(entity);
            Vec3d pos = entity.getPos();
            
            results.add(new DetectionResult(
                description,
                BlockPosUtils.fromVec3d(pos),
                pos,
                DetectionResult.DetectionType.ENTITY,
                score
            ));
        }

        return results;
    }

    private float evaluateEquipment(LivingEntity entity) {
        float score = 0.0f;
        int itemCount = 0;

        // Check armor slots
        for (EquipmentSlot slot : EquipmentSlot.values()) {
            ItemStack stack = entity.getEquippedStack(slot);
            if (!stack.isEmpty()) {
                itemCount++;
                Item item = stack.getItem();
                
                if (item instanceof ArmorItem) {
                    score += 0.2f;
                } else if (item instanceof MiningToolItem) {
                    score += 0.15f;
                } else {
                    score += 0.1f;
                }
                
                // Additional score for enchanted items
                if (stack.hasEnchantments()) {
                    score += 0.1f;
                }
            }
        }

        // Normalize score based on item count
        if (itemCount > 0) {
            return score / Math.max(4.0f, itemCount);
        }
        return 0.0f;
    }

    private String generateEquipmentDescription(LivingEntity entity) {
        StringBuilder description = new StringBuilder();
        description.append(entity.getType().getName().getString())
                  .append(" with notable equipment: ");

        boolean first = true;
        for (EquipmentSlot slot : EquipmentSlot.values()) {
            ItemStack stack = entity.getEquippedStack(slot);
            if (!stack.isEmpty()) {
                if (!first) {
                    description.append(", ");
                }
                description.append(stack.getName().getString());
                if (stack.hasEnchantments()) {
                    description.append(" (Enchanted)");
                }
                first = false;
            }
        }

        return description.toString();
    }
}
