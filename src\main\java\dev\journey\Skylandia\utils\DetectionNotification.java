package dev.journey.Skylandia.utils;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;

public class DetectionNotification {
    private final DetectionResult result;
    private final NotificationPriority priority;
    private final long timestamp;

    public DetectionNotification(DetectionResult result, NotificationPriority priority) {
        this.result = result;
        this.priority = priority;
        this.timestamp = System.currentTimeMillis();
    }

    public DetectionResult getResult() {
        return result;
    }

    public NotificationPriority getPriority() {
        return priority;
    }

    public long getTimestamp() {
        return timestamp;
    }
}