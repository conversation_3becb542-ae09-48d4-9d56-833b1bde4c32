package dev.journey.Skylandia.utils;

import javax.crypto.*;
import javax.crypto.spec.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Arrays;
import java.util.Base64;

public class SecureChat {
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int KEY_LENGTH = 256;
    private static final int IV_LENGTH = 12;
    private static final int TAG_LENGTH = 128;
    private static final int SALT_LENGTH = 16;
    private static final int ITERATIONS = 10000;
    
    /**
     * Encrypts a message using AES-GCM with a derived key from the password
     * @param message The message to encrypt
     * @param password The password to derive the key from
     * @return Base64 encoded string containing salt:iv:ciphertext
     * @throws GeneralSecurityException If encryption fails
     */
    public static String encrypt(String message, String password) throws GeneralSecurityException {
        try {
            // Generate salt and derive key
            byte[] salt = generateRandomBytes(SALT_LENGTH);
            SecretKey key = deriveKey(password, salt);
            
            // Generate IV
            byte[] iv = generateRandomBytes(IV_LENGTH);
            
            // Create cipher
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, key, gcmSpec);
            
            // Encrypt
            byte[] ciphertext = cipher.doFinal(message.getBytes(StandardCharsets.UTF_8));
            
            // Combine salt:iv:ciphertext and encode
            byte[] combined = new byte[salt.length + iv.length + ciphertext.length];
            System.arraycopy(salt, 0, combined, 0, salt.length);
            System.arraycopy(iv, 0, combined, salt.length, iv.length);
            System.arraycopy(ciphertext, 0, combined, salt.length + iv.length, ciphertext.length);
            
            return Base64.getEncoder().encodeToString(combined);
        } catch (GeneralSecurityException e) {
            throw new GeneralSecurityException("Encryption failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Decrypts a message using AES-GCM with a derived key from the password
     * @param encryptedMessage Base64 encoded string containing salt:iv:ciphertext
     * @param password The password to derive the key from
     * @return The decrypted message
     * @throws GeneralSecurityException If decryption fails
     */
    public static String decrypt(String encryptedMessage, String password) throws GeneralSecurityException {
        try {
            // Decode message
            byte[] combined = Base64.getDecoder().decode(encryptedMessage);
            
            // Extract salt, iv, and ciphertext
            byte[] salt = Arrays.copyOfRange(combined, 0, SALT_LENGTH);
            byte[] iv = Arrays.copyOfRange(combined, SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
            byte[] ciphertext = Arrays.copyOfRange(combined, SALT_LENGTH + IV_LENGTH, combined.length);
            
            // Derive key
            SecretKey key = deriveKey(password, salt);
            
            // Decrypt
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, key, gcmSpec);
            
            byte[] decrypted = cipher.doFinal(ciphertext);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (GeneralSecurityException e) {
            throw new GeneralSecurityException("Decryption failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Generates a cryptographically secure random byte array
     */
    private static byte[] generateRandomBytes(int length) {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);
        return bytes;
    }
    
    /**
     * Derives an AES key from a password using PBKDF2
     */
    private static SecretKey deriveKey(String password, byte[] salt) throws GeneralSecurityException {
        PBEKeySpec spec = new PBEKeySpec(
            password.toCharArray(),
            salt,
            ITERATIONS,
            KEY_LENGTH
        );
        
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        byte[] keyBytes = factory.generateSecret(spec).getEncoded();
        return new SecretKeySpec(keyBytes, "AES");
    }
}