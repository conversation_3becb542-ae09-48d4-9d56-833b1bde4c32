package dev.journey.Skylandia.modules.exploration.detection;

import dev.journey.Skylandia.modules.exploration.detection.analyzers.EntityEquipmentAnalyzer;
import dev.journey.Skylandia.modules.exploration.detection.patterns.PatternDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.StairPatternDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.PistonDoorDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.TunnelEntranceDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.VillageHouseDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.PortalFrameDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.TunnelDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.BedrockStaircaseDetector;
import dev.journey.Skylandia.modules.exploration.detection.patterns.VillageAnomalyDetector;
import dev.journey.Skylandia.utils.NotificationManager;
import dev.journey.Skylandia.utils.DetectionNotification;
import dev.journey.Skylandia.utils.NotificationPriority;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class DetectionManager {
    private final World world;
    private final List<PatternDetector> detectors;
    private final EntityAnalyzer entityAnalyzer;
    private final EntityEquipmentAnalyzer equipmentAnalyzer;
    private static final int DEFAULT_SCAN_RADIUS = 32;

    public DetectionManager(World world) {
        this.world = world;
        this.detectors = new ArrayList<>();
        this.entityAnalyzer = new EntityAnalyzer(world);
        this.equipmentAnalyzer = new EntityEquipmentAnalyzer(world);
        
        initializeDetectors();
    }

    private void initializeDetectors() {
        // Initialize each detector directly to avoid type inference issues
        try {
            detectors.add(new StairPatternDetector(world));
            detectors.add(new PistonDoorDetector(world));
            detectors.add(new TunnelEntranceDetector(world));
            detectors.add(new VillageHouseDetector(world));
            detectors.add(new PortalFrameDetector(world));
            detectors.add(new TunnelDetector(world));
            detectors.add(new BedrockStaircaseDetector(world));
            detectors.add(new VillageAnomalyDetector(world));
        } catch (Exception e) {
            System.err.println("Failed to initialize detectors: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void scan(BlockPos center) {
        scan(center, DEFAULT_SCAN_RADIUS);
    }

    public void scan(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        // Run pattern detectors
        for (PatternDetector detector : detectors) {
            try {
                results.addAll(detector.detect(center, radius));
            } catch (Exception e) {
                // Log error but continue with other detectors
                System.err.println("Error in detector " + detector.getClass().getSimpleName() + ": " + e.getMessage());
            }
        }

        // Run entity analysis
        try {
            results.addAll(entityAnalyzer.analyzeArea(center, radius));
            if (equipmentAnalyzer != null) {
                results.addAll(equipmentAnalyzer.analyzeEntityEquipment(center));
            }
        } catch (Exception e) {
            System.err.println("Error in entity analysis: " + e.getMessage());
        }

        // Process and notify results
        processResults(results);
    }

    private void processResults(List<DetectionResult> results) {
        for (DetectionResult result : results) {
            NotificationPriority priority = calculatePriority(result);
            NotificationManager.addNotification(new DetectionNotification(result, priority));
        }
    }

    private NotificationPriority calculatePriority(DetectionResult result) {
        float confidence = result.getConfidence();
        
        // Prioritize based on detection type and confidence
        if (result.getType() == DetectionResult.DetectionType.STRUCTURE 
            || result.getType() == DetectionResult.DetectionType.ANOMALY) {
            if (confidence > 0.8f) {
                return NotificationPriority.CRITICAL;
            } else if (confidence > 0.6f) {
                return NotificationPriority.HIGH;
            }
        }
        
        if (confidence > 0.7f) {
            return NotificationPriority.MEDIUM;
        }
        
        return NotificationPriority.LOW;
    }

    public List<PatternDetector> getDetectors() {
        return new ArrayList<>(detectors);
    }

    public void clearDetectors() {
        detectors.clear();
        initializeDetectors();
    }
}
