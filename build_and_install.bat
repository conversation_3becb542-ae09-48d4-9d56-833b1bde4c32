@echo off
setlocal EnableDelayedExpansion

echo [1/4] Building mod...
call .\gradlew.bat build > gradle_output.txt 2>&1

:: Check if build was successful by looking for "BUILD SUCCESSFUL" in the output
findstr /C:"BUILD SUCCESSFUL" gradle_output.txt > nul
if %ERRORLEVEL% EQU 0 (
    :: Display the gradle output
    type gradle_output.txt
    echo Build successful!
    
    :: Get the name of the built jar
    for /f "tokens=*" %%f in ('dir /b /s "build\libs\*.jar" ^| findstr /v "sources.jar"') do (
        set "MOD_PATH=%%f"
        for %%i in ("!MOD_PATH!") do set "MOD_NAME=%%~nxi"
    )
    
    echo [2/4] Built mod: !MOD_NAME!
    
    :: Set mods directory with proper quoting
    set "MODS_DIR=C:\Users\<USER>\Desktop\mmc-develop-win32\MultiMC\instances\bot 2\.minecraft\mods"
    
    echo [3/4] Checking mods directory...
    if not exist "!MODS_DIR!" (
        echo Creating mods directory...
        mkdir "!MODS_DIR!"
    )
    
    :: Remove existing version if found
    echo [4/4] Installing new version...
    if exist "!MODS_DIR!\!MOD_NAME!" (
        echo Removing existing version: !MOD_NAME!
        del /f "!MODS_DIR!\!MOD_NAME!"
    )
    
    :: Copy new version
    echo Copying new mod file...
    copy /Y "!MOD_PATH!" "!MODS_DIR!\"
    if !ERRORLEVEL! EQU 0 (
        echo.
        echo Installation successful! 
        echo Installed: !MOD_NAME!
        echo Location: !MODS_DIR!
        
        :: Clean up
        del gradle_output.txt
    ) else (
        echo Failed to copy mod file!
        echo From: !MOD_PATH!
        echo To: !MODS_DIR!
        del gradle_output.txt
        pause
        exit /b 1
    )
) else (
    :: Display the gradle output and error message
    type gradle_output.txt
    echo.
    echo Build failed! Check the output above for errors.
    del gradle_output.txt
    pause
    exit /b 1
)

echo.
echo Process completed successfully!
pause