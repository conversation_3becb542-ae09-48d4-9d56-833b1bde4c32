Total replacements made: 393

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.conditions.Condition;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.actions.Action;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.actions;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.AutomationFlow;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.actions;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.AutomationFlow;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.conditions;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.AutomationFlow;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.conditions;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.transport;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.modules.automation.ShulkerTransportModule;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.AutomationFlow;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.conditions.Condition;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.actions.Action;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.transport;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.modules.automation.ShulkerTransportModule;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.AutomationFlow;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.actions.Action;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.automation.transport;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.modules.automation.ShulkerTransportModule;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.AutomationFlow;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.automation.conditions.Condition;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.commands;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.commands;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.commands;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.ApiHandler;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: private final PathSeekerUtil pathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: private final PathSeekerUtil pathSeekerUtil;
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: private final PathSeekerUtil pathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: private final PathSeekerUtil pathSeekerUtil;
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Replaced: pathSeeker → Skylandia
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: this.pathSeekerUtil = new PathSeekerUtil();
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: this.pathSeekerUtil = new PathSeekerUtil();
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: pathSeeker → Skylandia
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: this.pathSeekerUtil = new PathSeekerUtil();
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: this.pathSeekerUtil = new PathSeekerUtil();
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: "§8<" + PathSeekerUtil.randomColorCode() + "§o✨" + "§r§8> §4§oPlayer not found§7..."
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: String colorCode = PathSeekerUtil.randomColorCode();
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: pathSeekerUtil.updateTimeInfo(
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Replaced: pathSeeker → Skylandia
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: String formattedFirstSeen = pathSeekerUtil.getFormattedFirstSeen();
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Replaced: pathSeeker → Skylandia
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: String formattedLastSeen = pathSeekerUtil.getFormattedLastSeen();
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Replaced: pathSeeker → Skylandia
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: String formattedPlaytime = pathSeekerUtil.getFormattedPlaytime();
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Replaced: pathSeeker → Skylandia
  Replaced: pathSeeker → skylandia

In .\replacement_log.txt:
  Original line: Original line: String formattedPlaytimeInMonth = PathSeekerUtil.formatPlaytime(stats.playtimeSecondsMonth);
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: PathSeeker.LOG.error("[Stats2b2t] Failed to deserialize JSON: {}", err.getMessage());
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.events;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.mixin;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.events.OffGroundSpeedEvent;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.mixin.accessor;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "AFKVanillaFly", "Maintains a level Y-flight with fireworks and smooth pitch control.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Hunting, "area-loader", "Spiral out from your position to load chunks in an area");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "AutoEnchanter", "Automatically enchants items at an anvil.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "AutoPortal", "For the Base Hunter who has places to be.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "duprexion", "Advanced packet manipulation for dupes");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "item-frame-dupe", "Automatically places and manipulates item frames to perform item duplication");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "Trident Aura", "For trident spam");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.automation;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "trident-dupe", "Dupes tridents in first hotbar slot.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Hunting, "CaveDisturbanceDetector", "Scans for single air blocks within the cave air blocks found in caves and underground structures in 1.13+ chunks. There are several false positives.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: [Rest of the file implementation remains unchanged with the only difference being PathSeeker.Hunting replaced with Skylandia.Hunting in the constructor]
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Hunting, "PortalPatternFinder", "Scans for the shapes of broken/removed Nether Portals within the cave air blocks found in caves and underground structures in 1.13+ chunks. **May be useful for finding portal skips in the Nether**");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Hunting, "SearchBot", "Advanced systematic area search with multiple patterns.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.PathSeekerUtil;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Hunting, "terrain-analyzer", "Analyzes terrain for suspicious modifications indicating player activity.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.exploration;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.modules.automation.AFKVanillaFly;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.modules.utility.Pitch40Util;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Hunting, "TrailFollower", "Automatically follows trails in all dimensions.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.render;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Render, "Hole/Tunnel/StairsESP", "Finds and highlights holes and tunnels and stairs.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.render;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.sendWebhook;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Render, "OldChunkNotifier", "Pings you on discord if you find old chunks");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.render;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Render, "VanityESP", "Highlights maparts and banners");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.utility;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Automation, "Firework", "Automatically fires rockets when boost ends.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.utility;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Utility, "GrimFirework", "Swaps to your elytra so fireworks actually work.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.utility;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.*;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.*;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.*;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.*;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: PathSeeker.Automation,
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.utility;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import static dev.journey.PathSeeker.utils.PathSeekerUtil.firework;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Utility, "Pitch40Util", "Maintains pitch 40 flight on 2b2t and dynamically adjusts bounds");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.utility;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.utils.SecureChat;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Utility, "secure-chat", "Enables encrypted party chat using AES-256");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: PathSeeker.LOG.error(err.toString());
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: PathSeeker.LOG.error("Failed to parse SignBlockEntity Nbt: {}", String.valueOf(err));
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: PathSeeker.LOG.error(e.toString());
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: if (contentBlacklist.get() && PathSeekerUtil.checkOrCreateFile(mc, BLACKLIST_FILE)) initBlacklistText();
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.modules.utility;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: super(PathSeeker.Utility, "stacked-minecarts", "Detects stacked minecarts in the world.");
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: package dev.journey.PathSeeker.utils;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: import dev.journey.PathSeeker.PathSeeker;
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: .header("User-Agent", "PathSeeker/1.0")
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Original line: PathSeeker.LOG.error("[ApiHandler] Failed to fetch response: {}", e.getMessage());
  Replaced: PathSeeker → Skylandia

In .\replacement_log.txt:
  Original line: Replaced: PathSeeker → Skylandia
  Replaced: PathSeeker → Skylandia

In .\replace_pathseeker.py:
  Original line: if match.lower() == 'pathseeker':
  Replaced: pathseeker → skylandia

In .\replace_pathseeker.py:
  Original line: # Create pattern that matches pathseeker case-insensitively
  Replaced: pathseeker → skylandia

In .\replace_pathseeker.py:
  Original line: pattern = re.compile(r'pathseeker', re.IGNORECASE)
  Replaced: pathseeker → skylandia
