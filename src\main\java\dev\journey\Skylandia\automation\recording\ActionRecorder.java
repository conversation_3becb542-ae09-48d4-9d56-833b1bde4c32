package dev.journey.Skylandia.automation.recording;

import dev.journey.Skylandia.automation.actions.Action;
import meteordevelopment.meteorclient.events.game.SendMessageEvent;
import meteordevelopment.meteorclient.events.meteor.KeyEvent;
import meteordevelopment.meteorclient.events.meteor.MouseButtonEvent;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.misc.input.KeyAction;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;
import java.util.List;

public class ActionRecorder {
    private boolean isRecording = false;
    private final List<RecordedAction> recordedActions = new ArrayList<>();
    private long recordingStartTime;
    private final MinecraftClient mc;

    public ActionRecorder() {
        this.mc = MinecraftClient.getInstance();
    }

    public void startRecording() {
        recordedActions.clear();
        recordingStartTime = System.currentTimeMillis();
        isRecording = true;
    }

    public List<RecordedAction> stopRecording() {
        isRecording = false;
        return new ArrayList<>(recordedActions);
    }

    public boolean isRecording() {
        return isRecording;
    }

    @EventHandler
    private void onKey(KeyEvent event) {
        if (!isRecording) return;
        
        System.out.println("\n[Debug] ==========================================");
        System.out.println("[Debug] ActionRecorder MeteorClient Analysis");
        System.out.println("[Debug] ==========================================");
        
        Package pkg = KeyAction.class.getPackage();
        System.out.println("=== Package Info ===");
        System.out.println("Package: " + pkg.getName());
        System.out.println("Implementation: " + pkg.getImplementationTitle());
        System.out.println("Version: " + pkg.getImplementationVersion());
        System.out.println("Spec version: " + pkg.getSpecificationVersion());
        
        System.out.println("\n=== ClassLoader Info ===");
        System.out.println("KeyAction loader: " + KeyAction.class.getClassLoader());
        System.out.println("Event action loader: " + event.action.getClass().getClassLoader());
        
        System.out.println("\n=== Event Info ===");
        System.out.println(String.format("Key: %-5d ('%s')", event.key, String.valueOf((char)event.key)));
        System.out.println(String.format("Action: %-10s | Name: %-10s | Ordinal: %d",
            event.action, event.action.name(), event.action.ordinal()));
        System.out.println("[Debug] ==========================================");
        
        recordedActions.add(new RecordedAction(
            ActionType.KEYBOARD,
            getTimestamp(),
            event.key,
            // Convert KeyAction to int using ordinal comparison
            event.action.ordinal() == 0 ? 1 : 0, // Press is ordinal 0
            mc.player.getPos(),
            mc.player.getBlockPos(),
            null
        ));
    }

    @EventHandler
    private void onMouseButton(MouseButtonEvent event) {
        if (!isRecording) return;
        
        System.out.println("\n[Debug] ==========================================");
        System.out.println("[Debug] MouseButton MeteorClient Analysis");
        System.out.println("[Debug] ==========================================");
        
        Package pkg = KeyAction.class.getPackage();
        System.out.println("=== Package Info ===");
        System.out.println("Package: " + pkg.getName());
        System.out.println("Implementation: " + pkg.getImplementationTitle());
        System.out.println("Version: " + pkg.getImplementationVersion());
        System.out.println("Spec version: " + pkg.getSpecificationVersion());
        
        System.out.println("\n=== ClassLoader Info ===");
        System.out.println("KeyAction loader: " + KeyAction.class.getClassLoader());
        System.out.println("Event action loader: " + event.action.getClass().getClassLoader());
        
        System.out.println("\n=== Event Info ===");
        System.out.println(String.format("Button: %d", event.button));
        System.out.println(String.format("Action: %-10s | Name: %-10s | Ordinal: %d",
            event.action, event.action.name(), event.action.ordinal()));
        
        recordedActions.add(new RecordedAction(
            ActionType.MOUSE,
            getTimestamp(),
            event.button,
            // Convert KeyAction to int using ordinal comparison
            event.action.ordinal() == 0 ? 1 : 0, // Press is ordinal 0
            mc.player.getPos(),
            mc.player.getBlockPos(),
            null
        ));
    }

    @EventHandler
    private void onChatMessage(SendMessageEvent event) {
        if (!isRecording) return;
        
        recordedActions.add(new RecordedAction(
            ActionType.CHAT,
            getTimestamp(),
            0,
            0,
            mc.player.getPos(),
            mc.player.getBlockPos(),
            event.message
        ));
    }

    private long getTimestamp() {
        return System.currentTimeMillis() - recordingStartTime;
    }

    public static class RecordedAction {
        public final ActionType type;
        public final long timestamp;
        public final int key;
        public final int action;
        public final Vec3d position;
        public final BlockPos blockPos;
        public final String message;

        public RecordedAction(ActionType type, long timestamp, int key, int action, Vec3d position, BlockPos blockPos, String message) {
            this.type = type;
            this.timestamp = timestamp;
            this.key = key;
            this.action = action;
            this.position = position;
            this.blockPos = blockPos;
            this.message = message;
        }
    }

    public enum ActionType {
        KEYBOARD,
        MOUSE,
        CHAT
    }
}