package dev.journey.Skylandia.modules.exploration.detection.structures;

import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class StructureDetectionManager {
    private final Map<StructureType, StructureDetector> detectors;
    private final Map<StructureType, Boolean> enabledTypes;
    private final Map<StructureType, Double> confidenceThresholds;
    private final int maxSearchRadius;

    public StructureDetectionManager(World world, int maxSearchRadius) {
        this.detectors = new ConcurrentHashMap<>();
        this.enabledTypes = new ConcurrentHashMap<>();
        this.confidenceThresholds = new ConcurrentHashMap<>();
        this.maxSearchRadius = maxSearchRadius;

        // Initialize default settings for all structure types
        for (StructureType type : StructureType.values()) {
            enabledTypes.put(type, true);
            confidenceThresholds.put(type, type.getDefaultConfidence());
        }
    }

    public void registerDetector(StructureDetector detector) {
        detectors.put(detector.getStructureType(), detector);
    }

    public void setStructureEnabled(StructureType type, boolean enabled) {
        enabledTypes.put(type, enabled);
    }

    public boolean isStructureEnabled(StructureType type) {
        return enabledTypes.getOrDefault(type, false);
    }

    public void setConfidenceThreshold(StructureType type, double threshold) {
        if (threshold >= 0.0 && threshold <= 1.0) {
            confidenceThresholds.put(type, threshold);
        }
    }

    public double getConfidenceThreshold(StructureType type) {
        return confidenceThresholds.getOrDefault(type, type.getDefaultConfidence());
    }

    public List<StructureMatch> detectStructures(Chunk chunk) {
        List<StructureMatch> matches = new ArrayList<>();
        
        for (Map.Entry<StructureType, StructureDetector> entry : detectors.entrySet()) {
            StructureType type = entry.getKey();
            if (isStructureEnabled(type)) {
                StructureDetector detector = entry.getValue();
                List<StructureMatch> typeMatches = detector.detectInChunk(chunk);
                
                // Filter matches by confidence threshold
                double threshold = getConfidenceThreshold(type);
                typeMatches.removeIf(match -> match.getConfidence() < threshold);
                
                matches.addAll(typeMatches);
            }
        }

        return matches;
    }

    public Set<StructureType> getRegisteredTypes() {
        return Collections.unmodifiableSet(detectors.keySet());
    }

    public void clearDetectors() {
        detectors.clear();
    }
}