package dev.journey.Skylandia.automation.transport;

/**
 * Exception class for transport-specific errors
 */
public class TransportException extends RuntimeException {
    private final TransportState errorState;
    private final TransportCondition.TransportFailureSeverity severity;
    private final String recoveryAction;

    /**
     * Create a new transport exception
     * @param message Error message
     * @param state State when error occurred
     * @param severity Severity of the error
     */
    public TransportException(String message, TransportState state, TransportCondition.TransportFailureSeverity severity) {
        this(message, state, severity, (String)null);
    }

    /**
     * Create a new transport exception with recovery action
     * @param message Error message
     * @param state State when error occurred
     * @param severity Severity of the error
     * @param recoveryAction Suggested recovery action
     */
    public TransportException(String message, TransportState state, TransportCondition.TransportFailureSeverity severity, String recoveryAction) {
        super(message);
        this.errorState = state;
        this.severity = severity;
        this.recoveryAction = recoveryAction;
    }

    /**
     * Create a new transport exception with cause
     * @param message Error message
     * @param state State when error occurred
     * @param severity Severity of the error
     * @param cause Original cause
     */
    public TransportException(String message, TransportState state, TransportCondition.TransportFailureSeverity severity, Throwable cause) {
        super(message, cause);
        this.errorState = state;
        this.severity = severity;
        this.recoveryAction = null;
    }

    /**
     * Get the state when the error occurred
     */
    public TransportState getErrorState() {
        return errorState;
    }

    /**
     * Get the severity of the error
     */
    public TransportCondition.TransportFailureSeverity getSeverity() {
        return severity;
    }

    /**
     * Get the suggested recovery action, if any
     */
    public String getRecoveryAction() {
        return recoveryAction;
    }

    @Override
    public String getMessage() {
        StringBuilder message = new StringBuilder(super.getMessage());
        message.append(" (State: ").append(errorState).append(", Severity: ").append(severity).append(")");
        
        if (recoveryAction != null) {
            message.append(" - Recovery Action: ").append(recoveryAction);
        }
        
        return message.toString();
    }

    /**
     * Create a timing exception
     */
    public static TransportException timing(String message, TransportState state) {
        return new TransportException(
            message,
            state,
            TransportCondition.TransportFailureSeverity.RECOVERABLE,
            "Retry with adjusted timing"
        );
    }

    /**
     * Create a safety check exception
     */
    public static TransportException safety(String message, TransportState state) {
        return new TransportException(
            message,
            state,
            TransportCondition.TransportFailureSeverity.CRITICAL,
            "Verify safety conditions and reset"
        );
    }

    /**
     * Create an inventory exception
     */
    public static TransportException inventory(String message, TransportState state) {
        return new TransportException(
            message,
            state,
            TransportCondition.TransportFailureSeverity.WARNING,
            "Check inventory contents and configuration"
        );
    }

    /**
     * Create a coordination exception
     */
    public static TransportException coordination(String message, TransportState state) {
        return new TransportException(
            message,
            state,
            TransportCondition.TransportFailureSeverity.WARNING,
            "Verify connection and retry"
        );
    }
}