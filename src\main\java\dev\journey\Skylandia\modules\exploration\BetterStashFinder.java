package dev.journey.Skylandia.modules.exploration;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import dev.journey.Skylandia.Skylandia;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.WindowScreen;
import meteordevelopment.meteorclient.gui.widgets.*;
import meteordevelopment.meteorclient.gui.widgets.containers.WTable;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList;
import meteordevelopment.meteorclient.gui.widgets.input.*;
import meteordevelopment.meteorclient.gui.widgets.pressable.WButton;
import meteordevelopment.meteorclient.gui.widgets.pressable.WCheckbox;
import meteordevelopment.meteorclient.gui.widgets.pressable.WMinus;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.render.MeteorToast;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.orbit.EventHandler;
import net.fabricmc.loader.api.FabricLoader;
import net.minecraft.block.entity.*;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.mob.BreezeEntity;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.item.Items;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.network.packet.s2c.play.ChunkDataS2CPacket;
import net.minecraft.registry.RegistryKey;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;
import net.minecraft.world.chunk.WorldChunk;
import net.minecraft.world.chunk.ChunkSection;
import xaero.common.minimap.waypoints.Waypoint;
import xaero.hud.minimap.BuiltInHudModules;
import xaero.hud.minimap.module.MinimapSession;
import xaero.hud.minimap.waypoint.set.WaypointSet;
import xaero.hud.minimap.world.MinimapWorld;
import xaero.map.mods.SupportMods;
import xaeroplus.XaeroPlus;
import xaeroplus.event.ChunkDataEvent;
import xaeroplus.module.ModuleManager;
import xaeroplus.module.impl.OldChunks;
import xaeroplus.module.impl.PaletteNewChunks;

import java.io.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.function.Consumer;
import java.util.function.Predicate;

import static dev.journey.Skylandia.utils.SkylandiaUtil.sendWebhook;

public class BetterStashFinder extends Module {
    public static final String MOD_ID = "Skylandia";
    public static final File FOLDER = FabricLoader.getInstance().getGameDir().resolve(MOD_ID).toFile();
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    // Structure detection tracking
    private final Set<ChunkPos> processedChunks = Collections.synchronizedSet(new HashSet<>());
    private final Queue<ChunkPos> structureDetectionQueue = new LinkedList<>();
    private final Set<ChunkPos> notifiedTrialChambers = Collections.synchronizedSet(new HashSet<>());
    private int structureScanTicks = 0;
    private final int STRUCTURE_SCAN_DELAY = 20; // 1 second delay between scans
    private final Setting<Integer> structureDetectionBatchSize;
    private final Set<String> seenPlayers = Collections.synchronizedSet(new HashSet<>());
    private int playerLogTickCounter = 0;
    private final int PLAYER_LOG_INTERVAL = 20 * 60 * 5; // 5 minutes in ticks
    
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgStructures = settings.createGroup("Structures");
    private final SettingGroup sgPlayerDetect = settings.createGroup("Player Detection");

    // Structure detection settings
    private final Setting<Boolean> enableStructureDetection = sgStructures.add(new BoolSetting.Builder()
            .name("structure-detection")
            .description("Enables the detection of structures like trial chambers, dungeons, and villages.")
            .defaultValue(true)
            .build()
    );

    private final Setting<Boolean> detectVillages = sgStructures.add(new BoolSetting.Builder()
            .name("detect-villages")
            .description("Detect villages by looking for iron golems and villagers.")
            .defaultValue(true)
            .visible(enableStructureDetection::get)
            .build()
    );

    private final Setting<Boolean> detectTrialChambers = sgStructures.add(new BoolSetting.Builder()
            .name("detect-trial-chambers")
            .description("Detect trial chambers by looking for trial spawners and breezes.")
            .defaultValue(true)
            .visible(enableStructureDetection::get)
            .build()
    );

    private final Setting<Boolean> detectDungeons = sgStructures.add(new BoolSetting.Builder()
            .name("detect-dungeons")
            .description("Detect dungeons by looking for spawners with mossy cobblestone.")
            .defaultValue(true)
            .visible(enableStructureDetection::get)
            .build()
    );

    private final Setting<Boolean> detectStrongholds = sgStructures.add(new BoolSetting.Builder()
            .name("detect-strongholds")
            .description("Detect strongholds by looking for stone brick patterns and end portal frames.")
            .defaultValue(true)
            .visible(enableStructureDetection::get)
            .build()
    );

    private final Setting<Boolean> detectMineshafts = sgStructures.add(new BoolSetting.Builder()
            .name("detect-mineshafts")
            .description("Detect mineshafts by looking for oak planks and cobwebs in specific patterns.")
            .defaultValue(true)
            .visible(enableStructureDetection::get)
            .build()    );

    private final Setting<Integer> villageConfidenceThreshold = sgStructures.add(new IntSetting.Builder()
            .name("village-confidence")
            .description("Minimum confidence percentage for village detection (0-100).")
            .defaultValue(75)
            .min(0)
            .max(100)
            .visible(() -> enableStructureDetection.get() && detectVillages.get())
            .build()
    );

    private final Setting<Integer> trialChamberConfidenceThreshold = sgStructures.add(new IntSetting.Builder()
            .name("trial-chamber-confidence")
            .description("Minimum confidence percentage for trial chamber detection (0-100).")
            .defaultValue(85)
            .min(0)
            .max(100)
            .visible(() -> enableStructureDetection.get() && detectTrialChambers.get())
            .build()
    );

    private final Setting<Boolean> quietMode = sgStructures.add(new BoolSetting.Builder()
            .name("quiet-mode")
            .description("Only show notifications when structures/stashes are found, no debug messages.")
            .defaultValue(true)
            .build()
    );

    // Structure detection is now handled by background processing - no manager needed
    private final Setting<List<BlockEntityType<?>>> storageBlocks = sgGeneral.add(new StorageBlockListSetting.Builder()
            .name("storage-blocks")
            .description("Select the storage blocks to search for.")
            .defaultValue(StorageBlockListSetting.STORAGE_BLOCKS)
            .build()
    );
    private final Setting<Integer> minimumStorageCount = sgGeneral.add(new IntSetting.Builder()
            .name("minimum-storage-count")
            .description("The minimum amount of storage blocks in a chunk to record the chunk.")
            .defaultValue(4)
            .min(1)
            .sliderMin(1)
            .build()
    );
    private final Setting<Boolean> shulkerInstantHit = sgGeneral.add(new BoolSetting.Builder()
            .name("shulker-instant-hit")
            .description("If a single shulker counts as a stash.")
            .defaultValue(false)
            .build()
    );
    private final Setting<Integer> minimumDistance = sgGeneral.add(new IntSetting.Builder()
            .name("minimum-distance")
            .description("The minimum distance you must be from spawn to record a certain chunk.")
            .defaultValue(0)
            .min(0)
            .sliderMax(10000)
            .build()
    );
    private final Setting<Boolean> onlyOldchunks = sgGeneral.add(new BoolSetting.Builder()
            .name("only-old-chunks")
            .description("Checks that the chunks it scans have already been loaded.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Boolean> sendNotifications = sgGeneral.add(new BoolSetting.Builder()
            .name("notifications")
            .description("Sends Minecraft notifications when new stashes are found.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Mode> notificationMode = sgGeneral.add(new EnumSetting.Builder<Mode>()
            .name("notification-mode")
            .description("The mode to use for notifications.")
            .defaultValue(Mode.Both)
            .visible(sendNotifications::get)
            .build()
    );
    private final Setting<List<WebhookConfig>> webhooks = sgGeneral.add(new WebhookListSetting(
            "webhooks",
            "Configure multiple webhooks with different filters.",
            new ArrayList<>()
    ));

    // Player detection notification settings
    private final Setting<Boolean> playerDetectionDetailed = sgPlayerDetect.add(new BoolSetting.Builder()
            .name("detailed-player-detection")
            .description("Send detailed info for each detected player in notifications.")
            .defaultValue(true)
            .build()
    );
    private final Setting<Integer> playerDetectionIntervalMinutes = sgPlayerDetect.add(new IntSetting.Builder()
            .name("player-detection-interval-minutes")
            .description("How often to send player detection notifications (minutes).")
            .defaultValue(60)
            .min(1)
            .max(1440)
            .build()
    );

    public static class WebhookConfig {
        public String url = "";
        public String discordId = "";
        public boolean ping = false;
        public int minStorageCount = 0;
        public int maxStorageCount = Integer.MAX_VALUE;
        public boolean requireShulker = false;
        public boolean onlyOldChunks = false;
        public int minDistanceFromSpawn = 0;
        
        // Container type filters
        public boolean includeChests = true;
        public boolean includeBarrels = true;
        public boolean includeShulkers = true;
        public boolean includeEnderChests = true;
        public boolean includeHoppers = true;
        public boolean includeDispensersDroppers = true;
        public boolean includeFurnaces = true;

        // Structure detection filters
        public boolean includeStructures = true;
        public Map<String, Boolean> enabledStructures = new HashMap<>();
        public Map<String, Double> structureConfidenceThresholds = new HashMap<>();

        public WebhookConfig() {
            // Initialize default structure settings
            for (StructureType type : StructureType.values()) {
                enabledStructures.put(type.name(), true);
                structureConfidenceThresholds.put(type.name(), type.getDefaultConfidence());
            }
        }
    }

    private class WebhookListSetting extends Setting<List<WebhookConfig>> {
        public WebhookListSetting(String name, String description, List<WebhookConfig> defaultValue) {
            super(name, description, defaultValue,
                (webhook) -> {
                    // Save whenever the setting changes
                    try {
                        saveJson();
                    } catch (Exception e) {
                        System.err.println("[Skylandia] Error saving webhook configs: " + e.getMessage());
                    }
                },  // onChanged
                null,  // onModuleActivated
                (IVisible) null  // visible
            );
        }

        @Override
        protected List<WebhookConfig> parseImpl(String str) {
            try {
                List<WebhookConfig> configs = GSON.fromJson(str, new TypeToken<List<WebhookConfig>>() {}.getType());
                return configs != null ? configs : new ArrayList<>(defaultValue);
            } catch (Exception e) {
                System.err.println("[Skylandia] Error parsing webhook config: " + e.getMessage());
                return new ArrayList<>(defaultValue);
            }
        }

        @Override
        protected boolean isValueValid(List<WebhookConfig> value) {
            if (value == null) return false;
            for (WebhookConfig config : value) {
                if (config == null) return false;
            }
            return true;
        }

        @Override
        public List<WebhookConfig> load(NbtCompound tag) {
            try {
                String json = tag.getString(name);
                if (json.isEmpty()) return new ArrayList<>(defaultValue);
                
                List<WebhookConfig> configs = GSON.fromJson(json, new TypeToken<List<WebhookConfig>>() {}.getType());
                if (configs == null) return new ArrayList<>(defaultValue);
                
                // Validate each config
                configs.removeIf(config -> config == null);
                return configs;
            } catch (Exception e) {
                System.err.println("[Skylandia] Error loading webhook configs: " + e.getMessage());
                return new ArrayList<>(defaultValue);
            }
        }

        @Override
        public NbtCompound save(NbtCompound tag) {
            try {
                List<WebhookConfig> configs = get();
                if (configs == null) configs = new ArrayList<>();
                
                // Remove any null configs before saving
                configs.removeIf(config -> config == null);
                
                String json = GSON.toJson(configs);
                tag.putString(name, json);
            } catch (Exception e) {
                System.err.println("[Skylandia] Error saving webhook configs: " + e.getMessage());
            }
            return tag;
        }
    }
    public List<Chunk> chunks = new ArrayList<>();
    private final Setting<Boolean> saveToWaypoints = sgGeneral.add(new BoolSetting.Builder()
            .name("save-to-waypoints")
            .description("Creates xaeros minimap waypoints for stash finds.")
            .defaultValue(false)
            .onChanged(this::waypointSettingChanged)
            .build()
    );

    // Player logging info
    private static class PlayerLogInfo {
        public final String uuid;
        public String name;
        public int[] hourlySeen = new int[24]; // index = hour of day
        public long lastSeenTimestamp = 0L;
        public double lastX, lastY, lastZ;
        public String lastDimension;
        public float lastHealth;
        public String lastArmor = "";
        public String lastEffects = "";
        public String lastGamemode = "";
        public String lastTeam = "";

        public PlayerLogInfo(String uuid, String name) {
            this.uuid = uuid;
            this.name = name;
        }

        public void resetHourly() {
            Arrays.fill(hourlySeen, 0);
        }
    }
    private final Map<String, PlayerLogInfo> playerLogMap = new HashMap<>(); // uuid -> info
    private long lastHourlyReset = System.currentTimeMillis();
    private long lastPlayerLogSent = 0L;
    private static final long PLAYER_LOG_SEND_INTERVAL = 1000L * 60 * 60; // 1 hour in ms

    public BetterStashFinder() {
        super(Skylandia.Hunting, "stash-finder", "Advanced stash finding functionality for Skylandia.");

        structureDetectionBatchSize = settings.getDefaultGroup().add(new IntSetting.Builder()
            .name("structure-detection-batch-size")
            .description("Number of chunks to process for structure detection per tick.")
            .defaultValue(5)
            .min(1)
            .max(100)
            .build()
        );

        // Import existing webhooks to WebhookManager if they don't exist
        importExistingWebhooks();

        // Initialize structure detection
        if (mc.world != null) {
            initializeStructureDetection();
        }
    }
    
    private void importExistingWebhooks() {
        if (webhooks.get() == null) return;
        
        for (WebhookConfig config : webhooks.get()) {
            if (config.url.isEmpty()) continue;
            
            String name = "Imported Webhook " + System.currentTimeMillis();
            String serverName = mc.getCurrentServerEntry() != null ? mc.getCurrentServerEntry().address : "unknown";
            
            dev.journey.Skylandia.utils.WebhookManager.addWebhook(
                name,
                config.url,
                config.discordId,
                serverName,
                "unknown"
            );
        }
    }

    private boolean isOldChunk(ChunkPos chunkPos, RegistryKey<World> dimension) {
        PaletteNewChunks paletteNewChunks = ModuleManager.getModule(PaletteNewChunks.class);
        boolean is119NewChunk = paletteNewChunks.isNewChunk(chunkPos.x, chunkPos.z, dimension);
        boolean is112OldChunk = ModuleManager.getModule(OldChunks.class).isOldChunk(chunkPos.x, chunkPos.z, dimension);
        return !is119NewChunk || is112OldChunk;
    }    private void logWebhookDebug(WebhookConfig webhook, Chunk chunk, double distanceFromSpawn) {
        if (!quietMode.get()) {
            info("Webhook Debug for URL: " + webhook.url.substring(0, Math.min(webhook.url.length(), 20)) + "...");
            info("- Total Storage: " + chunk.getTotal() + " (min: " + webhook.minStorageCount + ", max: " + webhook.maxStorageCount + ")");
            info("- Has Shulker: " + (chunk.shulkers > 0) + " (required: " + webhook.requireShulker + ")");
            info("- Is Old Chunk: " + isOldChunk(chunk.chunkPos, mc.world.getRegistryKey()) + " (required: " + webhook.onlyOldChunks + ")");
            info("- Distance: " + String.format("%.1f", distanceFromSpawn) + " (min: " + webhook.minDistanceFromSpawn + ")");
        }
    }

    private static String getWaypointName(Chunk chunk) {
        String waypointName = "";
        if (chunk.chests > 0) waypointName += "C:" + chunk.chests;
        if (chunk.barrels > 0) waypointName += "B:" + chunk.barrels;
        if (chunk.shulkers > 0) waypointName += "S:" + chunk.shulkers;
        if (chunk.enderChests > 0) waypointName += "E:" + chunk.enderChests;
        if (chunk.hoppers > 0) waypointName += "H:" + chunk.hoppers;
        if (chunk.dispensersDroppers > 0) waypointName += "D:" + chunk.dispensersDroppers;
        if (chunk.furnaces > 0) waypointName += "F:" + chunk.furnaces;
        return waypointName;
    }

    private void initializeStructureDetection() {        // Structure detection initialization moved to background processing
    }

    @Override
    public void onActivate() {
        XaeroPlus.EVENT_BUS.register(this);
        load();
        
        if (mc.world != null) {
            initializeStructureDetection();
        }
    }

    @Override
    public void onDeactivate() {
        try {
            // Safely unregister from events
            XaeroPlus.EVENT_BUS.unregister(this);
            
            // Save data before deactivating
            saveJson();
            saveCsv();
            
            // Clear any pending chunk data
            chunks.clear();
        } catch (Exception e) {
            error("Error during deactivation: " + e.getMessage());
        }
    }    @net.lenni0451.lambdaevents.EventHandler(priority = -1)
    public void onChunkData(ChunkDataEvent event) {
        if (event.seenChunk()) return;
        
        // Process structures in background if enabled
        if (enableStructureDetection.get() && mc.world != null) {
            ChunkPos chunkPos = event.chunk().getPos();
            if (!processedChunks.contains(chunkPos)) {
                processedChunks.add(chunkPos);
                
                // Process structure detection in background thread
                CompletableFuture.runAsync(() -> {
                    try {
                        detectStructuresInChunk(chunkPos);
                    } catch (Exception e) {
                        if (!quietMode.get()) {
                            error("Background structure detection error: %s", e.getMessage());
                        }
                    }
                });
            }
        }
        
        // Check the distance for stash detection
        double chunkXAbs = Math.abs(event.chunk().getPos().x * 16);
        double chunkZAbs = Math.abs(event.chunk().getPos().z * 16);
        if (Math.sqrt(chunkXAbs * chunkXAbs + chunkZAbs * chunkZAbs) < minimumDistance.get()) return;

        Chunk chunk = new Chunk(event.chunk().getPos());

        RegistryKey<World> currentDimension = mc.world.getRegistryKey();

        // Check that the chunk is in old chunks
        if (onlyOldchunks.get()) {
            ChunkPos chunkPos = chunk.chunkPos;
            PaletteNewChunks paletteNewChunks = ModuleManager.getModule(PaletteNewChunks.class);
            boolean is119NewChunk = paletteNewChunks
                    .isNewChunk(
                            chunkPos.x,
                            chunkPos.z,
                            currentDimension
                    );

            boolean is112OldChunk = ModuleManager.getModule(OldChunks.class)
                    .isOldChunk(
                            chunkPos.x,
                            chunkPos.z,
                            currentDimension
                    );
            if (is119NewChunk && !is112OldChunk) return;
        }

        for (BlockEntity blockEntity : event.chunk().getBlockEntities().values()) {
            if (!storageBlocks.get().contains(blockEntity.getType())) continue;

            BlockPos pos = blockEntity.getPos();
            chunk.storagePositions.add(pos);

            if (blockEntity instanceof ChestBlockEntity) chunk.chests++;
            else if (blockEntity instanceof BarrelBlockEntity) chunk.barrels++;
            else if (blockEntity instanceof ShulkerBoxBlockEntity) chunk.shulkers++;
            else if (blockEntity instanceof EnderChestBlockEntity) chunk.enderChests++;
            else if (blockEntity instanceof AbstractFurnaceBlockEntity) chunk.furnaces++;
            else if (blockEntity instanceof DispenserBlockEntity) chunk.dispensersDroppers++;
            else if (blockEntity instanceof HopperBlockEntity) chunk.hoppers++;
        }

        if ((chunk.getTotal() >= minimumStorageCount.get()) || (shulkerInstantHit.get() && chunk.shulkers > 0)) {
            Chunk prevChunk = null;
            int i = chunks.indexOf(chunk);

            if (i < 0) chunks.add(chunk);
            else prevChunk = chunks.set(i, chunk);

            saveJson();
            saveCsv();

            if (!chunk.equals(prevChunk) || !chunk.countsEqual(prevChunk)) {
                if (sendNotifications.get()) {
                    switch (notificationMode.get()) {
                        case Chat ->
                                info("Found stash at (highlight)%s(default), (highlight)%s(default).", chunk.x, chunk.z);
                        case Toast -> mc.getToastManager().add(new MeteorToast(Items.CHEST, title, "Found Stash!"));
                        case Both -> {
                            info("Found stash at (highlight)%s(default), (highlight)%s(default).", chunk.x, chunk.z);
                            mc.getToastManager().add(new MeteorToast(Items.CHEST, title, "Found Stash!"));
                        }
                    }
                }

                double distanceFromSpawn = Math.sqrt(chunk.x * chunk.x + chunk.z * chunk.z);
                
                for (WebhookConfig webhook : webhooks.get()) {
                    if (webhook.url.isEmpty()) continue;
                    
                    // Check filters
                    if (chunk.getTotal() < webhook.minStorageCount || chunk.getTotal() > webhook.maxStorageCount) continue;
                    if (webhook.requireShulker && chunk.shulkers == 0) continue;
                    if (webhook.onlyOldChunks && !isOldChunk(chunk.chunkPos, currentDimension)) continue;
                    if (distanceFromSpawn < webhook.minDistanceFromSpawn) continue;
                    
                    // Check container type filters
                    if (!webhook.includeChests && chunk.chests > 0) continue;
                    if (!webhook.includeBarrels && chunk.barrels > 0) continue;
                    if (!webhook.includeShulkers && chunk.shulkers > 0) continue;
                    if (!webhook.includeEnderChests && chunk.enderChests > 0) continue;
                    if (!webhook.includeHoppers && chunk.hoppers > 0) continue;
                    if (!webhook.includeDispensersDroppers && chunk.dispensersDroppers > 0) continue;
                    if (!webhook.includeFurnaces && chunk.furnaces > 0) continue;

                    StringBuilder message = new StringBuilder();
                    message.append("**Storage Details:**\n");
                    
                    // Get server address
                    String serverAddress = mc.getCurrentServerEntry() != null ? mc.getCurrentServerEntry().address : "singleplayer";
                    message.append("Server: ").append(serverAddress).append("\n\n");
                    
                    // Handle coordinates and dimensions
                    boolean isNether = currentDimension.getValue().getPath().equals("the_nether");
                    int overWorldX = isNether ? chunk.x * 8 : chunk.x;
                    int overWorldZ = isNether ? chunk.z * 8 : chunk.z;
                    int netherX = isNether ? chunk.x : chunk.x / 8;
                    int netherZ = isNether ? chunk.z : chunk.z / 8;
                    
                    message.append("**Coordinates:**\n");
                    message.append("Current Dimension: ").append(currentDimension.getValue().getPath()).append("\n");
                    if (isNether) {
                        message.append("Nether: ").append(chunk.x).append(", ").append(chunk.z).append("\n");
                        message.append("Overworld: ").append(overWorldX).append(", ").append(overWorldZ).append("\n");
                    } else {
                        message.append("Overworld: ").append(chunk.x).append(", ").append(chunk.z).append("\n");
                        message.append("Nether: ").append(netherX).append(", ").append(netherZ).append(" (Travel here in nether)\n");
                    }
                    
                    // Add travel directions
                    String compassDir = getDirectionsFromZeroZero(chunk.x, chunk.z);
                    message.append("\n**Travel Information:**\n");
                    message.append("Direction from 0,0: ").append(compassDir).append("\n");
                    double netherDistance = Math.sqrt(netherX * netherX + netherZ * netherZ);
                    message.append("Nether Distance: ").append(String.format("%.1f", netherDistance)).append(" blocks\n\n");
                    
                    message.append("**Container Counts:**\n");
                    
                    message.append("**Container Counts:**\n");
                    // Group storage blocks by type and collect Y levels
                    Map<String, List<Integer>> storageYLevels = new HashMap<>();
                    
                    for (BlockPos pos : chunk.storagePositions) {
                        BlockEntity be = mc.world.getBlockEntity(pos);
                        if (be == null) continue;
                        
                        String type = null;
                        if (be instanceof ChestBlockEntity && webhook.includeChests) type = "Chest";
                        else if (be instanceof BarrelBlockEntity && webhook.includeBarrels) type = "Barrel";
                        else if (be instanceof ShulkerBoxBlockEntity && webhook.includeShulkers) type = "Shulker";
                        else if (be instanceof EnderChestBlockEntity && webhook.includeEnderChests) type = "Ender Chest";
                        else if (be instanceof HopperBlockEntity && webhook.includeHoppers) type = "Hopper";
                        else if (be instanceof DispenserBlockEntity && webhook.includeDispensersDroppers) type = "Dispenser";
                        else if (be instanceof AbstractFurnaceBlockEntity && webhook.includeFurnaces) type = "Furnace";
                        
                        if (type != null) {
                            storageYLevels.computeIfAbsent(type, k -> new ArrayList<>()).add(pos.getY());
                        }
                    }
                    
                    // Display counts with Y levels
                    for (Map.Entry<String, List<Integer>> entry : storageYLevels.entrySet()) {
                        List<Integer> yLevels = entry.getValue();
                        yLevels.sort(Integer::compareTo); // Sort Y levels
                        message.append("- ").append(entry.getKey()).append("s: ")
                              .append(yLevels.size())
                              .append(" (Y: ")
                              .append(yLevels.stream()
                                    .map(String::valueOf)
                                    .collect(Collectors.joining(", ")))
                              .append(")\n");
                    }
                    message.append("\nTotal Storage Blocks: ").append(chunk.getTotal());

                    new Thread(() -> sendWebhook(webhook.url, title, message.toString(), webhook.ping ? webhook.discordId : null, mc.player.getGameProfile().getName())).start();
                }
                if (saveToWaypoints.get()) {
                    WaypointSet waypointSet = getWaypointSet();
                    if (waypointSet == null) return;
                    addToWaypoints(waypointSet, chunk);
                    SupportMods.xaeroMinimap.requestWaypointsRefresh();
                }
            }
        }
    }    // Structure type utility methods
    private boolean isStructureTypeEnabled(StructureType type) {
        switch (type) {
            case VILLAGE:
                return detectVillages.get();
            case DUNGEON:
                return detectDungeons.get();
            case TRIAL_CHAMBER:
                return detectTrialChambers.get();
            default:
                return true; // Enable other structure types by default
        }
    }
    
    private boolean isStructureTypeEnabled(String structureType) {
        return switch (structureType.toLowerCase()) {
            case "village" -> detectVillages.get();
            case "dungeon" -> detectDungeons.get();
            case "trial chamber" -> detectTrialChambers.get();
            case "stronghold", "mineshaft" -> true; // Always enabled for now
            default -> true;
        };
    }

    private net.minecraft.item.Item getStructureIcon(StructureType type) {
        switch (type) {
            case VILLAGE:
                return Items.EMERALD;
            case DUNGEON:
                return Items.CHEST;
            case TRIAL_CHAMBER:
                return Items.TRIAL_KEY;
            default:
                return Items.STRUCTURE_BLOCK;
        }
    }// Legacy structure processing methods - these are now deprecated in favor of background detection
    // Keeping the isStructureTypeEnabled method as it's still used for settings validation
    
    @Deprecated
    private void processStructureMatches(List<StructureMatch> matches, net.minecraft.world.chunk.Chunk chunk) {
        // This method is deprecated - structure detection now happens in background threads
        // using the onPacketReceive and onTick event handlers with entity/block scanning
    }

    @Deprecated
    private void addStructureWaypoint(final WaypointSet waypointSet, final StructureMatch match) {
        // This method is deprecated - new structure detection uses
        // the improved addStructureWaypoint(String, ChunkPos, int) method
    }

    @Override
    public WWidget getWidget(GuiTheme theme) {
        WVerticalList container = theme.verticalList();
        
        // Webhook configuration section
        // Saved webhooks section
        WTable savedWebhooksTable = container.add(theme.table()).expandX().widget();
        savedWebhooksTable.add(theme.label("Saved Webhooks")).expandX();
        savedWebhooksTable.row();

        List<dev.journey.Skylandia.utils.WebhookManager.StoredWebhook> savedWebhooks =
            dev.journey.Skylandia.utils.WebhookManager.getAllWebhooks();

        for (dev.journey.Skylandia.utils.WebhookManager.StoredWebhook saved : savedWebhooks) {
            WTable webhookRow = savedWebhooksTable.add(theme.table()).expandX().widget();
            
            webhookRow.add(theme.label(saved.name + " (" + saved.serverName + " - " + saved.channelName + ")"));
            
            WButton useWebhook = webhookRow.add(theme.button("Use")).widget();
            useWebhook.action = () -> {
                WebhookConfig newConfig = new WebhookConfig();
                newConfig.url = saved.url;
                newConfig.discordId = saved.discordId;
                List<WebhookConfig> configs = webhooks.get();
                configs.add(newConfig);
                webhooks.set(configs);
                // Update last used timestamp
                saved.lastUsed = System.currentTimeMillis();
                dev.journey.Skylandia.utils.WebhookManager.updateWebhook(saved.name, saved);
            };
            
            WButton deleteWebhook = webhookRow.add(theme.button("Delete")).widget();
            deleteWebhook.action = () -> {
                dev.journey.Skylandia.utils.WebhookManager.removeWebhook(saved.name);
                if (mc.currentScreen instanceof BetterStashFinderScreen) {
                    mc.setScreen(new BetterStashFinderScreen(theme));
                }
            };
            
            savedWebhooksTable.row();
        }

        savedWebhooksTable.add(theme.horizontalSeparator()).expandX();
        savedWebhooksTable.row();

        // Current webhooks section
        WTable webhookTable = container.add(theme.table()).expandX().widget();
        webhookTable.add(theme.label("Current Webhook Configurations")).expandX();
        webhookTable.row();

        List<WebhookConfig> configs = webhooks.get();
        
        // Add webhook button
        WButton addWebhook = webhookTable.add(theme.button("Add Webhook")).expandX().widget();
        addWebhook.action = () -> {
                configs.add(new WebhookConfig());
                webhooks.set(configs);
                // Refresh the GUI using proper parent
                if (mc.currentScreen instanceof BetterStashFinderScreen) {
                    BetterStashFinderScreen screen = new BetterStashFinderScreen(theme);
                    mc.setScreen(screen);
                } else {
                    mc.setScreen(new BetterStashFinderScreen(theme));
                }
        };
        webhookTable.row();

        // Individual webhook configs
        for (int i = 0; i < configs.size(); i++) {
            WebhookConfig config = configs.get(i);
            int index = i;
            
            WTable configTable = webhookTable.add(theme.table()).expandX().widget();
            configTable.add(theme.label("Webhook #" + (i + 1) + " Configuration")).expandX();
            // Add save button
            WButton save = configTable.add(theme.button("Save as Persistent")).widget();
            save.action = () -> {
                mc.setScreen(new SaveWebhookScreen(theme, config));
            };

            WButton delete = configTable.add(theme.button("Delete")).widget();
            delete.action = () -> {
                configs.remove(index);
                webhooks.set(configs);
                // Refresh the GUI using proper parent
                if (mc.currentScreen instanceof BetterStashFinderScreen) {
                    BetterStashFinderScreen screen = new BetterStashFinderScreen(theme);
                    mc.setScreen(screen);
                } else {
                    mc.setScreen(new BetterStashFinderScreen(theme));
                }
            };
            configTable.row();

            // Basic settings
            addTextField(theme, configTable, "Discord Webhook URL:", config.url, val -> config.url = val);
            addTextField(theme, configTable, "Discord User ID:", config.discordId, val -> config.discordId = val);
            addCheckbox(theme, configTable, "Enable Ping:", config.ping, val -> config.ping = val);

            // Filter settings
            configTable.add(theme.label("Filter Settings:")).expandX();
            configTable.row();

            addIntField(theme, configTable, "Min Storage Count:", config.minStorageCount, 0, val -> config.minStorageCount = val);
            addIntField(theme, configTable, "Max Storage Count:", config.maxStorageCount, Integer.MAX_VALUE, val -> config.maxStorageCount = val);
            addIntField(theme, configTable, "Min Distance from Spawn:", config.minDistanceFromSpawn, 0, val -> config.minDistanceFromSpawn = val);
            addCheckbox(theme, configTable, "Require Shulker:", config.requireShulker, val -> config.requireShulker = val);
            addCheckbox(theme, configTable, "Only Old Chunks:", config.onlyOldChunks, val -> config.onlyOldChunks = val);

            // Container type filters
            configTable.add(theme.label("Container Type Filters:")).expandX();
            configTable.row();

            addCheckbox(theme, configTable, "Include Chests:", config.includeChests, val -> config.includeChests = val);
            addCheckbox(theme, configTable, "Include Barrels:", config.includeBarrels, val -> config.includeBarrels = val);
            addCheckbox(theme, configTable, "Include Shulkers:", config.includeShulkers, val -> config.includeShulkers = val);
            addCheckbox(theme, configTable, "Include Ender Chests:", config.includeEnderChests, val -> config.includeEnderChests = val);
            addCheckbox(theme, configTable, "Include Hoppers:", config.includeHoppers, val -> config.includeHoppers = val);
            addCheckbox(theme, configTable, "Include Dispensers/Droppers:", config.includeDispensersDroppers, val -> config.includeDispensersDroppers = val);
            addCheckbox(theme, configTable, "Include Furnaces:", config.includeFurnaces, val -> config.includeFurnaces = val);

            // Structure detection settings
            configTable.add(theme.label("Structure Detection Settings:")).expandX();
            configTable.row();

            addCheckbox(theme, configTable, "Include Structures:", config.includeStructures, val -> config.includeStructures = val);

            // Add structure-specific settings for each structure type
            for (StructureType type : StructureType.values()) {
                String structureName = type.name();
                
                // Enable/disable structure
                addCheckbox(theme, configTable,
                    "Enable " + type.getDisplayName() + ":",
                    config.enabledStructures.getOrDefault(structureName, true),
                    val -> config.enabledStructures.put(structureName, val)
                );
                
                // Confidence threshold
                addDoubleField(theme, configTable,
                    type.getDisplayName() + " Confidence:",
                    config.structureConfidenceThresholds.getOrDefault(structureName, type.getDefaultConfidence()),
                    type.getDefaultConfidence(),
                    val -> config.structureConfidenceThresholds.put(structureName, val)
                );
            }

            webhookTable.row();
            webhookTable.add(theme.horizontalSeparator()).expandX();
            webhookTable.row();
        }

        container.add(theme.horizontalSeparator()).expandX();
        
        // Stash list section
        WVerticalList list = container.add(theme.verticalList()).widget();
        list.add(theme.label("Found Stashes")).expandX();
        
        // Sort chunks
        chunks.sort(Comparator.comparingInt(value -> -value.getTotal()));

        // Clear button
        WButton clear = list.add(theme.button("Clear")).widget();
        WTable table = new WTable();
        if (!chunks.isEmpty()) list.add(table);

        clear.action = () -> {
            removeAllStashWaypoints(chunks);
            chunks.clear();
            table.clear();
        };

        fillTable(theme, table);

        return container;
    }

    private void addTextField(GuiTheme theme, WTable table, String label, String value, java.util.function.Consumer<String> onChanged) {
        table.add(theme.label(label));
        WTextBox textBox = table.add(theme.textBox(value)).expandX().widget();
        textBox.action = () -> {
            onChanged.accept(textBox.get());
            webhooks.set(webhooks.get()); // Trigger setting save
        };
        table.row();
    }

    private void addCheckbox(GuiTheme theme, WTable table, String label, boolean value, java.util.function.Consumer<Boolean> onChanged) {
        table.add(theme.label(label));
        WCheckbox checkbox = table.add(theme.checkbox(value)).widget();
        checkbox.action = () -> {
            onChanged.accept(checkbox.checked);
            webhooks.set(webhooks.get()); // Trigger setting save
        };
        table.row();
    }

    private void addIntField(GuiTheme theme, WTable table, String label, int value, int defaultVal, java.util.function.Consumer<Integer> onChanged) {
        table.add(theme.label(label));
        WIntEdit intEdit = table.add(theme.intEdit(value, 0, Integer.MAX_VALUE, 0, defaultVal, true)).widget();
        intEdit.action = () -> {
            onChanged.accept(intEdit.get() > 0 ? intEdit.get() : defaultVal);
            webhooks.set(webhooks.get()); // Trigger setting save
        };
        table.row();
    }

    private void addDoubleField(GuiTheme theme, WTable table, String label, double value, double defaultVal, java.util.function.Consumer<Double> onChanged) {
        table.add(theme.label(label));
        WDoubleEdit doubleEdit = table.add(theme.doubleEdit(value, 0.0, 1.0)).widget();
        doubleEdit.action = () -> {
            onChanged.accept(doubleEdit.get() >= 0.0 ? doubleEdit.get() : defaultVal);
            webhooks.set(webhooks.get()); // Trigger setting save
        };
        table.row();
    }

    private void fillTable(GuiTheme theme, WTable table) {
        for (Chunk chunk : chunks) {
            table.add(theme.label("Pos: " + chunk.x + ", " + chunk.z));
            table.add(theme.label("Total: " + chunk.getTotal()));

            WButton open = table.add(theme.button("Open")).widget();
            open.action = () -> mc.setScreen(new ChunkScreen(theme, chunk));

            WButton gotoBtn = table.add(theme.button("Goto")).widget();
            gotoBtn.action = () -> PathManagers.get().moveTo(new BlockPos(chunk.x, 0, chunk.z), true);

            WMinus delete = table.add(theme.minus()).widget();
            delete.action = () -> {
                if (chunks.remove(chunk)) {
                    table.clear();
                    fillTable(theme, table);

                    saveJson();
                    saveCsv();
                    Waypoint waypoint = getWaypointByCoordinate(chunk.x, chunk.z);
                    if (waypoint != null) {
                        WaypointSet waypointSet = getWaypointSet();
                        if (waypointSet != null) {
                            waypointSet.remove(waypoint);
                            SupportMods.xaeroMinimap.requestWaypointsRefresh();
                        }
                    }
                }
            };

            table.row();
        }
    }

    private void load() {
        boolean loaded = false;

        // Try to load json
        File file = getJsonFile();
        if (file.exists()) {
            try {
                FileReader reader = new FileReader(file);
                chunks = GSON.fromJson(reader, new TypeToken<List<Chunk>>() {
                }.getType());
                reader.close();

                for (Chunk chunk : chunks) chunk.calculatePos();

                loaded = true;
            } catch (Exception ignored) {
                if (chunks == null) chunks = new ArrayList<>();
            }
        }

        // Try to load csv
        file = getCsvFile();
        if (!loaded && file.exists()) {
            try {
                BufferedReader reader = new BufferedReader(new FileReader(file));
                reader.readLine();

                String line;
                while ((line = reader.readLine()) != null) {
                    String[] values = line.split(" ");
                    Chunk chunk = new Chunk(new ChunkPos(Integer.parseInt(values[0]), Integer.parseInt(values[1])));

                    chunk.chests = Integer.parseInt(values[2]);
                    chunk.shulkers = Integer.parseInt(values[3]);
                    chunk.enderChests = Integer.parseInt(values[4]);
                    chunk.furnaces = Integer.parseInt(values[5]);
                    chunk.dispensersDroppers = Integer.parseInt(values[6]);
                    chunk.hoppers = Integer.parseInt(values[7]);

                    chunks.add(chunk);
                }

                reader.close();
            } catch (Exception ignored) {
                if (chunks == null) chunks = new ArrayList<>();
            }
        }
        // TODO: Add all stashes as waypoints
    }

    private void saveCsv() {
        try {
            File file = getCsvFile();
            file.getParentFile().mkdirs();
            Writer writer = new FileWriter(file);

            writer.write("X,Z,Chests,Barrels,Shulkers,EnderChests,Furnaces,DispensersDroppers,Hoppers\n");
            for (Chunk chunk : chunks) chunk.write(writer);

            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void saveJson() {
        try {
            File file = getJsonFile();
            file.getParentFile().mkdirs();
            Writer writer = new FileWriter(file);
            GSON.toJson(chunks, writer);
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private File getJsonFile() {
        return new File(new File(new File(BetterStashFinder.FOLDER, "better-stash-finder"), Utils.getFileWorldName()), "stashes.json");
    }

    private File getCsvFile() {
        return new File(new File(new File(BetterStashFinder.FOLDER, "better-stash-finder"), Utils.getFileWorldName()), "stashes.csv");
    }

    @Override
    public String getInfoString() {
        return String.valueOf(chunks.size());
    }

    private Waypoint getWaypointByCoordinate(int x, int z) {
        WaypointSet waypointSet = getWaypointSet();
        if (waypointSet == null) return null;
        for (Waypoint waypoint : waypointSet.getWaypoints()) {
            if (waypoint.getX() == x && waypoint.getZ() == z) {
                return waypoint;
            }
        }
        return null;
    }

    private void removeAllStashWaypoints(List<Chunk> chunks) {
        WaypointSet waypointSet = getWaypointSet();
        if (waypointSet == null) return;
        for (Chunk chunk : chunks) {
            Waypoint waypoint = getWaypointByCoordinate(chunk.x, chunk.z);
            if (waypoint != null) {
                waypointSet.remove(waypoint);
            }
        }
        SupportMods.xaeroMinimap.requestWaypointsRefresh();
    }

    private WaypointSet getWaypointSet() {
        MinimapSession minimapSession = BuiltInHudModules.MINIMAP.getCurrentSession();
        if (minimapSession == null) return null;
        MinimapWorld currentWorld = minimapSession.getWorldManager().getCurrentWorld();
        if (currentWorld == null) return null;
        return currentWorld.getCurrentWaypointSet();
    }

    private void addToWaypoints(WaypointSet waypointSet, Chunk chunk) {
        int x = chunk.x;
        int z = chunk.z;

        // dont add waypoint that already exists
        if (getWaypointByCoordinate(x, z) != null) return;

        String waypointName = getWaypointName(chunk);

        // set color based on total storage blocks
        int color = 0;
        if (chunk.getTotal() < 15) color = 10; // green
        else if (chunk.getTotal() < 50) color = 14; // i forgot what these are lmao
        else if (chunk.getTotal() < 100) color = 12;
        else if (chunk.getTotal() >= 100) color = 4; // red i think

        Waypoint waypoint = new Waypoint(
                x,
                70,
                z,
                waypointName,
                "S",
                color,
                0,
                false);

        waypointSet.add(waypoint);
    }

    private void waypointSettingChanged(boolean enabled) {
        if (!enabled) {
            removeAllStashWaypoints(chunks);
        } else {
            WaypointSet waypointSet = getWaypointSet();
            if (waypointSet == null) return;
            for (Chunk chunk : chunks) {
                addToWaypoints(waypointSet, chunk);
            }
            SupportMods.xaeroMinimap.requestWaypointsRefresh();
        }
    }


    public enum Mode {
        Chat,
        Toast,
        Both
    }

    public static class Chunk {
        private static final StringBuilder sb = new StringBuilder();

        public ChunkPos chunkPos;
        public transient int x, z;
        public transient List<BlockPos> storagePositions;
        public int chests, barrels, shulkers, enderChests, furnaces, dispensersDroppers, hoppers;

        public Chunk(ChunkPos chunkPos) {
            this.chunkPos = chunkPos;
            this.storagePositions = new ArrayList<>();
            calculatePos();
        }

        public void calculatePos() {
            x = chunkPos.x * 16 + 8;
            z = chunkPos.z * 16 + 8;
        }

        public int getTotal() {
            return chests + barrels + shulkers + enderChests + furnaces + dispensersDroppers + hoppers;
        }

        public void write(Writer writer) throws IOException {
            sb.setLength(0);
            sb.append(x).append(',').append(z).append(',');
            sb.append(chests).append(',').append(barrels).append(',').append(shulkers).append(',').append(enderChests).append(',').append(furnaces).append(',').append(dispensersDroppers).append(',').append(hoppers).append('\n');
            writer.write(sb.toString());
        }

        public boolean countsEqual(Chunk c) {
            if (c == null) return false;
            return chests != c.chests || barrels != c.barrels || shulkers != c.shulkers || enderChests != c.enderChests || furnaces != c.furnaces || dispensersDroppers != c.dispensersDroppers || hoppers != c.hoppers;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Chunk chunk = (Chunk) o;
            return Objects.equals(chunkPos, chunk.chunkPos);
        }

        @Override
        public int hashCode() {
            return Objects.hash(chunkPos);
        }
    }

    private class BetterStashFinderScreen extends WindowScreen {
        private final BetterStashFinder parent;

        public BetterStashFinderScreen(GuiTheme theme) {
            super(theme, "Stash Finder Settings");
            this.parent = BetterStashFinder.this;
        }

        @Override
        public void initWidgets() {
            add(parent.getWidget(theme)).expandX();
        }
    }

    private static class ChunkScreen extends WindowScreen {
        private final Chunk chunk;

        public ChunkScreen(GuiTheme theme, Chunk chunk) {
            super(theme, "Chunk at " + chunk.x + ", " + chunk.z);

            this.chunk = chunk;
        }

        @Override
        public void initWidgets() {
            WTable t = add(theme.table()).expandX().widget();

            // Total
            t.add(theme.label("Total:"));
            t.add(theme.label(chunk.getTotal() + ""));
            t.row();

            t.add(theme.horizontalSeparator()).expandX();
            t.row();

            // Separate
            t.add(theme.label("Chests:"));
            t.add(theme.label(chunk.chests + ""));
            t.row();

            t.add(theme.label("Barrels:"));
            t.add(theme.label(chunk.barrels + ""));
            t.row();

            t.add(theme.label("Shulkers:"));
            t.add(theme.label(chunk.shulkers + ""));
            t.row();

            t.add(theme.label("Ender Chests:"));
            t.add(theme.label(chunk.enderChests + ""));
        }
    }

    private class SaveWebhookScreen extends WindowScreen {
        private final WebhookConfig config;
        private String webhookName = "";
        private String serverName = "";
        private String channelName = "";

        public SaveWebhookScreen(GuiTheme theme, WebhookConfig config) {
            super(theme, "Save Webhook");
            this.config = config;
            
            // Default server name to current server
            this.serverName = mc.getCurrentServerEntry() != null ? mc.getCurrentServerEntry().address : "";
        }

        @Override
        public void initWidgets() {
            WTable table = add(theme.table()).expandX().widget();
            
            // Webhook name field
            table.add(theme.label("Webhook Name:"));
            WTextBox nameBox = table.add(theme.textBox(webhookName)).expandX().widget();
            nameBox.action = () -> webhookName = nameBox.get();
            table.row();

            // Server name field
            table.add(theme.label("Server Name:"));
            WTextBox serverBox = table.add(theme.textBox(serverName)).expandX().widget();
            serverBox.action = () -> serverName = serverBox.get();
            table.row();

            // Channel name field
            table.add(theme.label("Channel Name:"));
            WTextBox channelBox = table.add(theme.textBox(channelName)).expandX().widget();
            channelBox.action = () -> channelName = channelBox.get();
            table.row();

            // Add save button
            WButton saveButton = table.add(theme.button("Save")).expandX().widget();
            saveButton.action = () -> {
                if (!webhookName.isEmpty()) {
                    dev.journey.Skylandia.utils.WebhookManager.addWebhook(
                        webhookName,
                        config.url,
                        config.discordId,
                        serverName,
                        channelName
                    );
                    
                    // Return to previous screen
                    if (parent instanceof BetterStashFinderScreen) {
                        mc.setScreen(new BetterStashFinderScreen(theme));
                    } else {
                        close();
                    }
                }
            };
            table.row();

            // Add cancel button
            WButton cancelButton = table.add(theme.button("Cancel")).expandX().widget();
            cancelButton.action = this::close;
        }
    }
    
    private String getDirectionsFromZeroZero(int x, int z) {
        StringBuilder directions = new StringBuilder();
        
        if (z < 0) {
            directions.append("North ");
        } else if (z > 0) {
            directions.append("South ");
        }
        
        if (x < 0) {
            directions.append("West");
        } else if (x > 0) {
            directions.append("East");
        }
        
        if (directions.length() == 0) {
            return "At 0,0";
        }
        
        return directions.toString().trim();
    }

    private void showToast(String title, String description, net.minecraft.item.Item icon) {
        if (sendNotifications.get() && mc.player != null) {
            mc.getToastManager().add(new MeteorToast(icon, title, description));
        }
    }

    @EventHandler
    private void onPreTick(TickEvent.Pre event) {
        if (!this.isActive() || mc.world == null) return;

        // Log players in render distance to Discord and track seen players
        if (mc.player != null && mc.world != null) {
            long now = System.currentTimeMillis();
            // Reset hourly counters every 24 hours
            if (now - lastHourlyReset > 1000L * 60 * 60 * 24) {
                for (PlayerLogInfo info : playerLogMap.values()) info.resetHourly();
                lastHourlyReset = now;
            }
            int hour = java.time.LocalDateTime.now().getHour();
            List<net.minecraft.entity.player.PlayerEntity> players = mc.world.getEntitiesByClass(net.minecraft.entity.player.PlayerEntity.class, mc.player.getBoundingBox().expand(mc.options.getViewDistance().getValue() * 16), e -> !e.getName().getString().equals(mc.player.getName().getString()));
            boolean newPlayerSeen = false;
            StringBuilder newPlayersMsg = new StringBuilder();
            Set<String> currentPlayers = new HashSet<>();
            for (net.minecraft.entity.player.PlayerEntity entity : players) {
                String name = entity.getName().getString();
                String uuid = entity.getUuidAsString();
                PlayerLogInfo info = playerLogMap.computeIfAbsent(uuid, u -> new PlayerLogInfo(u, name));
                info.name = name;
                info.hourlySeen[hour]++;
                info.lastSeenTimestamp = now;
                info.lastX = entity.getX();
                info.lastY = entity.getY();
                info.lastZ = entity.getZ();
                info.lastDimension = mc.world.getRegistryKey().getValue().toString();
                info.lastHealth = entity.getHealth();
                info.lastArmor = entity.getInventory().armor.stream().map(item -> item.getName().getString()).collect(java.util.stream.Collectors.joining(", "));
                info.lastEffects = entity.getStatusEffects().stream()
                    .map(eff -> {
                        String effectName;
                        try {
                            effectName = eff.getEffectType().getKey().map(key -> key.getValue().getPath()).orElse("unknown");
                        } catch (Exception e) {
                            effectName = "unknown";
                        }
                        return effectName + " " + (eff.getAmplifier() + 1);
                    })
                    .collect(java.util.stream.Collectors.joining(", "));
                info.lastGamemode = "?"; // Not available for other players
                info.lastTeam = entity.getScoreboardTeam() != null ? entity.getScoreboardTeam().getName() : "";
                currentPlayers.add(name);
                if (seenPlayers.add(name)) {
                    newPlayerSeen = true;
                    newPlayersMsg.append(name).append(", ");
                }
            }
            // Only send update if new player seen or interval has passed
            if ((newPlayerSeen || now - lastPlayerLogSent > playerDetectionIntervalMinutes.get() * 60 * 1000) && !webhooks.get().isEmpty()) {
                lastPlayerLogSent = now;
                for (WebhookConfig webhook : webhooks.get()) {
                    if (!webhook.url.isEmpty()) {
                        StringBuilder msg = new StringBuilder();
                        msg.append("Players in render distance (" + currentPlayers.size() + "):\n");
                        for (String name : currentPlayers) {
                            for (PlayerLogInfo info : playerLogMap.values()) {
                                if (info.name.equals(name)) {
                                    if (playerDetectionDetailed.get()) {
                                        msg.append(formatDetailedPlayerLogInfo(info)).append("\n");
                                    } else {
                                        msg.append(info.name).append("\n");
                                    }
                                }
                            }
                        }
                        if (newPlayerSeen) {
                            msg.append("\nNew player(s) detected: ").append(newPlayersMsg);
                        }
                        new Thread(() -> sendWebhook(webhook.url, "Player Log", msg.toString(), null, mc.player.getGameProfile().getName())).start();
                    }
                }
            }
        }

        // Batch structure detection on main thread
        int batch = structureDetectionBatchSize.get();
        for (int i = 0; i < batch && !structureDetectionQueue.isEmpty(); i++) {
            ChunkPos chunkPos = structureDetectionQueue.poll();
            if (chunkPos != null) {
                try {
                    detectStructuresInChunk(chunkPos);
                } catch (Exception e) {
                    if (!quietMode.get()) {
                        error("Main thread structure detection error: %s", e.getMessage());
                    }
                }
            }
        }

        // Structure entity scanning with delay to prevent lag
        if (enableStructureDetection.get() && structureScanTicks++ >= STRUCTURE_SCAN_DELAY) {
            structureScanTicks = 0;
            scanForStructureEntities();
        }
    }

    @EventHandler
    private void onReadPacket(PacketEvent.Receive event) {
        if (!this.isActive() || !(event.packet instanceof ChunkDataS2CPacket packet) || mc.world == null) return;

        if (enableStructureDetection.get()) {
            ChunkPos chunkPos = new ChunkPos(packet.getChunkX(), packet.getChunkZ());
            if (!processedChunks.contains(chunkPos)) {
                processedChunks.add(chunkPos);
                structureDetectionQueue.add(chunkPos);
            }
        }
    }

    private void scanForStructureEntities() {
        if (mc.world == null || mc.player == null) return;
        
        int renderDistance = mc.options.getViewDistance().getValue();
        ChunkPos playerChunkPos = new ChunkPos(mc.player.getBlockPos());
        
        for (int chunkX = playerChunkPos.x - renderDistance; chunkX <= playerChunkPos.x + renderDistance; chunkX++) {
            for (int chunkZ = playerChunkPos.z - renderDistance; chunkZ <= playerChunkPos.z + renderDistance; chunkZ++) {
                WorldChunk chunk = mc.world.getChunk(chunkX, chunkZ);
                if (chunk != null) {                    Box chunkBox = new Box(
                            chunk.getPos().getStartX(), mc.world.getBottomY(), chunk.getPos().getStartZ(),
                            chunk.getPos().getEndX() + 1, mc.world.getHeight(), chunk.getPos().getEndZ() + 1
                    );
                    
                    scanChunkEntities(chunk, chunkBox);
                }
            }
        }
    }

    private void scanChunkEntities(WorldChunk chunk, Box chunkBox) {
        ChunkPos chunkPos = chunk.getPos();
        List<Entity> entities = mc.world.getEntitiesByClass(Entity.class, chunkBox, entity -> true);
        
        // Village detection
        if (detectVillages.get()) {
            detectVillage(chunkPos, entities);
        }
        
        // Trial chamber detection
        if (detectTrialChambers.get()) {
            detectTrialChamber(chunkPos, entities);
        }
    }

    private void detectVillage(ChunkPos chunkPos, List<Entity> entities) {
        int villagerCount = 0;
        int ironGolemCount = 0;
        int bedCount = 0;

        // Count villagers and iron golems using MobGearESP-style entity iteration
        for (Entity entity : entities) {
            if (entity instanceof VillagerEntity) {
                villagerCount++;
            } else if (entity instanceof IronGolemEntity) {
                ironGolemCount++;
            }
        }

        // Scan for beds in the chunk
        if (mc.world != null) {
            net.minecraft.world.chunk.WorldChunk chunk = mc.world.getChunk(chunkPos.x, chunkPos.z);
            for (BlockEntity blockEntity : chunk.getBlockEntities().values()) {
                if (blockEntity.getType().toString().toLowerCase().contains("bed")) {
                    bedCount++;
                }
            }
        }

        // Village confidence calculation: require both villagers and multiple beds
        int confidence = 0;
        if (villagerCount >= 2 && bedCount >= 2) confidence += 70;
        if (ironGolemCount >= 1) confidence += 20;
        if (villagerCount >= 5 && bedCount >= 5) confidence += 10;

        if (confidence >= villageConfidenceThreshold.get()) {
            reportStructureFound("Village", chunkPos, confidence,
                String.format("Villagers: %d, Beds: %d, Iron Golems: %d", villagerCount, bedCount, ironGolemCount));
        }
    }

    private void detectTrialChamber(ChunkPos chunkPos, List<Entity> entities) {
        int breezeCount = 0;

        for (Entity entity : entities) {
            if (entity instanceof BreezeEntity) {
                breezeCount++;
            }
        }

        // Check for trial spawners in chunk blocks
        WorldChunk chunk = mc.world.getChunk(chunkPos.x, chunkPos.z);
        if (chunk != null) {
            int trialSpawnerCount = scanForTrialSpawners(chunk);

            // Trial chamber confidence calculation
            int confidence = 0;
            if (trialSpawnerCount >= 1) confidence += 60;
            if (breezeCount >= 1) confidence += 30;
            if (trialSpawnerCount >= 3) confidence += 20;

            // Prevent duplicate notifications for the same chunk
            if (confidence >= trialChamberConfidenceThreshold.get() && !notifiedTrialChambers.contains(chunkPos)) {
                notifiedTrialChambers.add(chunkPos);
                reportStructureFound("Trial Chamber", chunkPos, confidence,
                    String.format("Trial Spawners: %d, Breezes: %d", trialSpawnerCount, breezeCount));
            }
        }
    }

    private int scanForTrialSpawners(WorldChunk chunk) {
        int count = 0;
        ChunkSection[] sections = chunk.getSectionArray();
        
        for (ChunkSection section : sections) {
            if (section == null || section.isEmpty()) continue;
            
            for (int x = 0; x < 16; x++) {
                for (int y = 0; y < 16; y++) {
                    for (int z = 0; z < 16; z++) {
                        if (section.getBlockState(x, y, z).getBlock() == Blocks.TRIAL_SPAWNER) {
                            count++;
                        }
                    }
                }
            }
        }
        
        return count;
    }

    private void detectStructuresInChunk(ChunkPos chunkPos) {
        // This method processes chunks in background
        WorldChunk chunk = mc.world.getChunk(chunkPos.x, chunkPos.z);
        if (chunk == null) return;
        
        // Dungeon detection
        if (detectDungeons.get()) {
            detectDungeon(chunk);
        }
        
        // Stronghold detection
        if (detectStrongholds.get()) {
            detectStronghold(chunk);
        }
        
        // Mineshaft detection
        if (detectMineshafts.get()) {
            detectMineshaft(chunk);
        }
    }

    private void detectStructuresInChunk(WorldChunk chunk) {
        // Overloaded method for direct chunk processing
        detectStructuresInChunk(chunk.getPos());
    }

    private void detectDungeon(WorldChunk chunk) {
        ChunkPos chunkPos = chunk.getPos();
        ChunkSection[] sections = chunk.getSectionArray();
        
        int spawnerCount = 0;
        int mossyCobbleCount = 0;
        
        for (ChunkSection section : sections) {
            if (section == null || section.isEmpty()) continue;
            
            for (int x = 0; x < 16; x++) {
                for (int y = 0; y < 16; y++) {
                    for (int z = 0; z < 16; z++) {
                        var block = section.getBlockState(x, y, z).getBlock();
                        if (block == Blocks.SPAWNER) {
                            spawnerCount++;
                        } else if (block == Blocks.MOSSY_COBBLESTONE) {
                            mossyCobbleCount++;
                        }
                    }
                }
            }
        }
        
        // Dungeon confidence calculation
        int confidence = 0;
        if (spawnerCount >= 1) confidence += 50;
        if (mossyCobbleCount >= 10) confidence += 30;
        if (spawnerCount == 1 && mossyCobbleCount >= 20) confidence += 20;
        
        if (confidence >= 70) {
            reportStructureFound("Dungeon", chunkPos, confidence,
                String.format("Spawners: %d, Mossy Cobblestone: %d", spawnerCount, mossyCobbleCount));
        }
    }

    private void detectStronghold(WorldChunk chunk) {
        ChunkPos chunkPos = chunk.getPos();
        ChunkSection[] sections = chunk.getSectionArray();
        
        int stoneBrickCount = 0;
        int endPortalFrameCount = 0;
        
        for (ChunkSection section : sections) {
            if (section == null || section.isEmpty()) continue;
            
            for (int x = 0; x < 16; x++) {
                for (int y = 0; y < 16; y++) {
                    for (int z = 0; z < 16; z++) {
                        var block = section.getBlockState(x, y, z).getBlock();
                        if (block == Blocks.STONE_BRICKS || block == Blocks.STONE_BRICK_STAIRS) {
                            stoneBrickCount++;
                        } else if (block == Blocks.END_PORTAL_FRAME) {
                            endPortalFrameCount++;
                        }
                    }
                }
            }
        }
        
        // Stronghold confidence calculation
        int confidence = 0;
        if (endPortalFrameCount >= 1) confidence += 90;
        if (stoneBrickCount >= 50) confidence += 20;
        
        if (confidence >= 80) {
            reportStructureFound("Stronghold", chunkPos, confidence,
                String.format("End Portal Frames: %d, Stone Bricks: %d", endPortalFrameCount, stoneBrickCount));
        }
    }

    private void detectMineshaft(WorldChunk chunk) {
        ChunkPos chunkPos = chunk.getPos();
        ChunkSection[] sections = chunk.getSectionArray();
        
        int oakPlankCount = 0;
        int cobwebCount = 0;
        int railCount = 0;
        
        for (ChunkSection section : sections) {
            if (section == null || section.isEmpty()) continue;
            
            for (int x = 0; x < 16; x++) {
                for (int y = 0; y < 16; y++) {
                    for (int z = 0; z < 16; z++) {
                        var block = section.getBlockState(x, y, z).getBlock();
                        if (block == Blocks.OAK_PLANKS) {
                            oakPlankCount++;
                        } else if (block == Blocks.COBWEB) {
                            cobwebCount++;
                        } else if (block == Blocks.RAIL || block == Blocks.ACTIVATOR_RAIL) {
                            railCount++;
                        }
                    }
                }
            }
        }
        
        // Mineshaft confidence calculation
        int confidence = 0;
        if (cobwebCount >= 5) confidence += 40;
        if (oakPlankCount >= 20) confidence += 30;
        if (railCount >= 5) confidence += 20;
        
        if (confidence >= 70) {
            reportStructureFound("Mineshaft", chunkPos, confidence,
                String.format("Oak Planks: %d, Cobwebs: %d, Rails: %d", oakPlankCount, cobwebCount, railCount));
        }
    }    private void reportStructureFound(String structureType, ChunkPos chunkPos, int confidence, String details) {
        // Check if this structure type is enabled
        if (!isStructureTypeEnabled(structureType)) {
            return;
        }
        
        // Show toast notification
        showToast(structureType + " Found", 
            String.format("Confidence: %d%% at %s", confidence, chunkPos.toString()),
            getStructureIcon(structureType));
        
        // Add waypoint if enabled
        if (saveToWaypoints.get()) {
            addStructureWaypoint(structureType, chunkPos, confidence);
        }
        
        // Send webhook notification if configured
        sendStructureWebhook(structureType, chunkPos, confidence, details);
    }

    private void addStructureWaypoint(String structureType, ChunkPos chunkPos, int confidence) {
        try {
            WaypointSet waypointSet = getWaypointSet();
            if (waypointSet != null) {
                BlockPos centerPos = new BlockPos(chunkPos.getCenterX(), mc.world.getSeaLevel(), chunkPos.getCenterZ());
                String name = String.format("%s (%d%%)", structureType, confidence);
                
                // Don't add if waypoint already exists at this location
                if (getWaypointByCoordinate(centerPos.getX(), centerPos.getZ()) != null) return;
                
                int color = confidence > 80 ? 4 : confidence > 60 ? 14 : 1; // Red, Orange, or White
                
                Waypoint waypoint = new Waypoint(
                    centerPos.getX(), centerPos.getY(), centerPos.getZ(),
                    name, "S", color
                );
                
                waypointSet.add(waypoint);
                SupportMods.xaeroMinimap.requestWaypointsRefresh();
            }
        } catch (Exception e) {
            if (!quietMode.get()) {
                error("Failed to add structure waypoint: %s", e.getMessage());
            }
        }
    }

    private void sendStructureWebhook(String structureType, ChunkPos chunkPos, int confidence, String details) {
        for (WebhookConfig webhook : webhooks.get()) {
            if (!webhook.url.isEmpty() && webhook.includeStructures) {
                StringBuilder message = new StringBuilder();
                message.append("**").append(structureType).append(" Detected!**\n");
                message.append("Confidence: ").append(confidence).append("%\n");
                message.append("Location: ").append(chunkPos.toString()).append("\n");
                message.append("Details: ").append(details).append("\n");
                
                new Thread(() -> sendWebhook(
                    webhook.url,
                    "BetterStashFinder - " + structureType + " Found",
                    message.toString(),
                    webhook.ping ? webhook.discordId : null,
                    mc.player.getGameProfile().getName()
                )).start();
            }
        }
    }

    private net.minecraft.item.Item getStructureIcon(String structureType) {
        return switch (structureType.toLowerCase()) {
            case "village" -> Items.EMERALD;
            case "trial chamber" -> Items.TRIAL_KEY;
            case "dungeon" -> Items.SPAWNER;
            case "stronghold" -> Items.END_PORTAL_FRAME;
            case "mineshaft" -> Items.RAIL;
            default -> Items.COMPASS;
        };
    }

    private String formatPlayerLogInfo(PlayerLogInfo info) {
        StringBuilder sb = new StringBuilder();
        sb.append("Player: ").append(info.name).append("\n");
        sb.append("UUID: ").append(info.uuid).append("\n");
        sb.append(String.format("Position: X=%.1f, Y=%.1f, Z=%.1f\n", info.lastX, info.lastY, info.lastZ));
        sb.append("Dimension: ").append(info.lastDimension).append("\n");
        sb.append("Health: ").append(info.lastHealth).append("\n");
        sb.append("Armor: ").append(info.lastArmor).append("\n");
        sb.append("Effects: ").append(info.lastEffects).append("\n");
        sb.append("Gamemode: ").append(info.lastGamemode).append("\n");
        sb.append("Team: ").append(info.lastTeam).append("\n");
        sb.append("Hourly Seen (last 24h): ");
        for (int i = 0; i < info.hourlySeen.length; i++) {
            sb.append(i).append(":").append(info.hourlySeen[i]);
            if (i < info.hourlySeen.length - 1) sb.append(", ");
        }
        return sb.toString();
    }

    // Add a more detailed player info formatter
    private String formatDetailedPlayerLogInfo(PlayerLogInfo info) {
        return String.format(
            "%s | Pos: (%.1f, %.1f, %.1f) | Dim: %s | HP: %.1f | Armor: %s | Effects: %s | Team: %s | LastSeen: %s",
            info.name,
            info.lastX, info.lastY, info.lastZ,
            info.lastDimension,
            info.lastHealth,
            info.lastArmor,
            info.lastEffects,
            info.lastTeam,
            new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(info.lastSeenTimestamp))
        );
    }
}
