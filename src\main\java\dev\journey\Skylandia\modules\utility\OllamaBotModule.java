package dev.journey.Skylandia.modules.utility;

import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.fabricmc.loader.api.FabricLoader;
import meteordevelopment.meteorclient.utils.player.ChatUtils;

import java.util.*;
import java.util.concurrent.*;
import java.net.*;
import java.io.*;

public class OllamaBotModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgWhitelist = settings.createGroup("Whitelist");
    private final SettingGroup sgCommands = settings.createGroup("Commands");
    private final SettingGroup sgOllama = settings.createGroup("Ollama");

    private final Setting<List<String>> whitelist = sgWhitelist.add(new StringListSetting.Builder()
        .name("whitelist")
        .description("List of whitelisted player names.")
        .defaultValue(new ArrayList<>())
        .build()
    );

    private final Setting<String> askCommand = sgCommands.add(new StringSetting.Builder()
        .name("ask-command")
        .description("Command prefix for asking questions.")
        .defaultValue("!ask")
        .build()
    );
    private final Setting<String> tpaCommand = sgCommands.add(new StringSetting.Builder()
        .name("tpa-command")
        .description("Command prefix for requesting tp.")
        .defaultValue("!tpa")
        .build()
    );
    private final Setting<String> tpacceptCommand = sgCommands.add(new StringSetting.Builder()
        .name("tpaccept-command")
        .description("Command to accept tp requests.")
        .defaultValue("/tpaccept")
        .build()
    );
    private final Setting<String> ollamaHost = sgOllama.add(new StringSetting.Builder()
        .name("ollama-host")
        .description("Ollama server host (e.g. http://localhost:11434)")
        .defaultValue("http://localhost:11434")
        .build()
    );
    // Configurable path to models file directory
    private final Setting<String> modelsDir = sgOllama.add(new StringSetting.Builder()
        .name("models-directory")
        .description("Directory where ollama-models.txt is stored. Example: C:/Users/<USER>/Desktop/mmc-develop-win32/MultiMC/instances/bot 2/.minecraft/Skylandia")
        .defaultValue("C:/Users/<USER>/Desktop/mmc-develop-win32/MultiMC/instances/bot 2/.minecraft/Skylandia")
        .build()
    );

    // Store available models and whitelist
    private final Setting<List<String>> availableModels = sgOllama.add(new StringListSetting.Builder()
        .name("available-models")
        .description("List of models fetched from Ollama server.")
        .defaultValue(new ArrayList<>())
        .visible(() -> false) // Hide from normal UI, managed internally
        .build()
    );

    // Model selection as a dropdown (populated from availableModels)
    private final Setting<String> ollamaModel = sgOllama.add(new StringSetting.Builder()
        .name("ollama-model")
        .description("Ollama model to use (choose from ollama-models.txt in config dir)")
        .defaultValue("")
        .build()
    );

    private final Setting<List<String>> modelWhitelist = sgOllama.add(new StringListSetting.Builder()
        .name("model-whitelist")
        .description("Only allow these models to be used via chat. Leave empty to allow all.")
        .defaultValue(new ArrayList<>())
        .build()
    );

    private final Setting<String> commandIdentifier = sgCommands.add(new StringSetting.Builder()
        .name("command-identifier")
        .description("Prefix for all bot commands, e.g. & or ! or $ (default: &)")
        .defaultValue("&")
        .build()
    );

    // System prompt for Ollama bot
    private final Setting<String> systemPrompt = sgOllama.add(new StringSetting.Builder()
        .name("system-prompt")
        .description("System prompt sent with every Ollama request. Use this to set the bot's persona or instructions.")
        .defaultValue("")
        .build()
    );

    // Read-only field to display contents of ollama-models.txt
    private final Setting<String> modelsFileContents = sgOllama.add(new StringSetting.Builder()
        .name("models-file-contents")
        .description("Contents of ollama-models.txt (read-only)")
        .defaultValue(readModelsFile())
        .visible(() -> true)
        .build()
    );
    // Read-only field to display contents of ollama-whitelist.txt
    private final Setting<String> whitelistFileContents = sgOllama.add(new StringSetting.Builder()
        .name("whitelist-file-contents")
        .description("Contents of ollama-whitelist.txt (read-only)")
        .defaultValue(readWhitelistFile())
        .visible(() -> true)
        .build()
    );
    // Read-only field to display contents of ollama-chatlog.txt
    private final Setting<String> chatLogFileContents = sgOllama.add(new StringSetting.Builder()
        .name("chatlog-file-contents")
        .description("Contents of ollama-chatlog.txt (read-only)")
        .defaultValue(readChatLogFile())
        .visible(() -> true)
        .build()
    );

    // --- Additional Settings for Enhanced UX ---
    // Option to auto-refresh model list on interval
    private final Setting<Boolean> autoRefreshModels = sgOllama.add(new BoolSetting.Builder()
        .name("auto-refresh-models")
        .description("Automatically refresh the Ollama model list every X minutes.")
        .defaultValue(false)
        .build()
    );
    private final Setting<Integer> refreshInterval = sgOllama.add(new IntSetting.Builder()
        .name("refresh-interval-minutes")
        .description("Interval in minutes for auto-refreshing the model list.")
        .defaultValue(10)
        .min(1)
        .sliderMax(60)
        .visible(autoRefreshModels::get)
        .build()
    );
    // Option to enable/disable chat logging
    private final Setting<Boolean> enableChatLogging = sgOllama.add(new BoolSetting.Builder()
        .name("enable-chat-logging")
        .description("Enable logging of all chat and /msg to ollama-chatlog.txt.")
        .defaultValue(true)
        .build()
    );
    // Option to clear logs from settings
    private final Setting<Boolean> clearChatLog = sgOllama.add(new BoolSetting.Builder()
        .name("clear-chatlog")
        .description("Clear the chat log file (ollama-chatlog.txt) when toggled.")
        .defaultValue(false)
        .onChanged(val -> { if (val) clearChatLogFile(); })
        .build()
    );
    private void clearChatLogFile() {
        File file = getChatLogFile();
        try (PrintWriter out = new PrintWriter(file)) { out.print(""); } catch (Exception ignored) {}
        chatLogFileContents.set(readChatLogFile());
        clearChatLog.set(false); // Reset toggle
    }
    // Option to clear whitelist from settings
    private final Setting<Boolean> clearWhitelist = sgOllama.add(new BoolSetting.Builder()
        .name("clear-whitelist")
        .description("Clear the whitelist file (ollama-whitelist.txt) when toggled.")
        .defaultValue(false)
        .onChanged(val -> { if (val) clearWhitelistFile(); })
        .build()
    );
    private void clearWhitelistFile() {
        File file = getWhitelistFile();
        try (PrintWriter out = new PrintWriter(file)) { out.print(""); } catch (Exception ignored) {}
        whitelistFileContents.set(readWhitelistFile());
        clearWhitelist.set(false); // Reset toggle
    }
    // Option to clear models file from settings
    private final Setting<Boolean> clearModelsFile = sgOllama.add(new BoolSetting.Builder()
        .name("clear-models-file")
        .description("Clear the models file (ollama-models.txt) when toggled.")
        .defaultValue(false)
        .onChanged(val -> { if (val) clearModelsFile(); })
        .build()
    );
    private void clearModelsFile() {
        File file = getModelsFile();
        try (PrintWriter out = new PrintWriter(file)) { out.print(""); } catch (Exception ignored) {}
        modelsFileContents.set(readModelsFile());
        clearModelsFile.set(false); // Reset toggle
    }
    // Option to show/hide advanced settings
    private final Setting<Boolean> showAdvanced = sgGeneral.add(new BoolSetting.Builder()
        .name("show-advanced-settings")
        .description("Show advanced and debug settings.")
        .defaultValue(false)
        .build()
    );

    private final MinecraftClient mc = MinecraftClient.getInstance();
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    public OllamaBotModule() {
        super(dev.journey.Skylandia.Skylandia.Utility, "ollama-bot", "Allows whitelisted users to ask questions to an Ollama model and request tp.");
        // Ensure all files exist on client load
        try {
            getModelsFile().getParentFile().mkdirs();
            getModelsFile().createNewFile();
            getWhitelistFile().getParentFile().mkdirs();
            getWhitelistFile().createNewFile();
            getChatLogFile().getParentFile().mkdirs();
            getChatLogFile().createNewFile();
            getMsgLogFile().getParentFile().mkdirs();
            getMsgLogFile().createNewFile();
        } catch (Exception ignored) {}
        // Populate models list on module construction (client load)
        executor.submit(this::updateModelsFromOllama);
        // Load whitelist from file
        loadWhitelist();
    }

    // In onActivate, start auto-refresh if enabled
    @Override
    public void onActivate() {
        executor.submit(this::updateModelsFromOllama);
        modelsFileContents.set(readModelsFile());
        whitelistFileContents.set(readWhitelistFile());
        chatLogFileContents.set(readChatLogFile());
        saveWhitelist();
        if (autoRefreshModels.get()) startAutoRefresh();
    }
    // Auto-refresh logic
    private ScheduledFuture<?> autoRefreshTask;
    private void startAutoRefresh() {
        if (autoRefreshTask != null && !autoRefreshTask.isCancelled()) autoRefreshTask.cancel(true);
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        autoRefreshTask = scheduler.scheduleAtFixedRate(
            this::updateModelsFromOllama,
            refreshInterval.get(),
            refreshInterval.get(),
            TimeUnit.MINUTES
        );
    }
    @Override
    public void onDeactivate() {
        if (autoRefreshTask != null) autoRefreshTask.cancel(true);
    }

    private File getModelsFile() {
        return new File(modelsDir.get(), "ollama-models.txt");
    }
    // File for whitelist
    private File getWhitelistFile() {
        return new File(modelsDir.get(), "ollama-whitelist.txt");
    }
    // File for chat log
    private File getChatLogFile() {
        return new File(modelsDir.get(), "ollama-chatlog.txt");
    }
    // File for msg log (future use or for /msg logging)
    private File getMsgLogFile() {
        return new File(modelsDir.get(), "ollama-msglog.txt");
    }

    private void updateModelsFromOllama() {
        File modelsFile = getModelsFile();
        try {
            // Run 'ollama list' and write output to file
            ProcessBuilder pb = new ProcessBuilder("ollama", "list");
            pb.redirectErrorStream(true);
            Process proc = pb.start();
            List<String> models = new ArrayList<>();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(proc.getInputStream()))) {
                String line;
                while ((line = br.readLine()) != null) {
                    // Each line: model_name ...
                    String[] parts = line.trim().split(" ", 2);
                    if (parts.length > 0 && !parts[0].isEmpty() && !parts[0].equals("NAME")) {
                        models.add(parts[0]);
                    }
                }
            }
            proc.waitFor();
            // Write models to file
            modelsFile.getParentFile().mkdirs();
            try (PrintWriter out = new PrintWriter(modelsFile)) {
                for (String m : models) out.println(m);
            }
            // Update availableModels setting
            availableModels.set(models);
            // Set dropdown default if needed
            if (!models.isEmpty() && (ollamaModel.get().isEmpty() || !models.contains(ollamaModel.get()))) {
                ollamaModel.set(models.get(0));
            }
            // Update models file contents in settings
            modelsFileContents.set(readModelsFile());
        } catch (Exception e) {
            // Fallback: try to read from file if exists
            if (modelsFile.exists()) {
                try (BufferedReader br = new BufferedReader(new FileReader(modelsFile))) {
                    List<String> models = new ArrayList<>();
                    String line;
                    while ((line = br.readLine()) != null) {
                        if (!line.trim().isEmpty()) models.add(line.trim());
                    }
                    availableModels.set(models);
                    if (!models.isEmpty() && (ollamaModel.get().isEmpty() || !models.contains(ollamaModel.get()))) {
                        ollamaModel.set(models.get(0));
                    }
                    // Update models file contents in settings
                    modelsFileContents.set(readModelsFile());
                } catch (Exception ignored) {}
            }
        }
    }

    // Save whitelist to file
    private void saveWhitelist() {
        File file = getWhitelistFile();
        try {
            file.getParentFile().mkdirs();
            try (PrintWriter out = new PrintWriter(file)) {
                for (String name : whitelist.get()) out.println(name);
            }
        } catch (Exception ignored) {}
        whitelistFileContents.set(readWhitelistFile());
    }
    // Load whitelist from file
    private void loadWhitelist() {
        File file = getWhitelistFile();
        if (!file.exists()) return;
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            List<String> names = new ArrayList<>();
            String line;
            while ((line = br.readLine()) != null) {
                if (!line.trim().isEmpty()) names.add(line.trim());
            }
            whitelist.set(names);
        } catch (Exception ignored) {}
    }
    // Only log chat if enabled
    private void logChat(String sender, String msg, boolean isWhisper) {
        if (!enableChatLogging.get()) return;
        File file = getChatLogFile();
        try {
            file.getParentFile().mkdirs();
            try (FileWriter fw = new FileWriter(file, true)) {
                fw.write(String.format("[%s] %s: %s\n", isWhisper ? "MSG" : "CHAT", sender, msg));
            }
        } catch (Exception ignored) {}
        chatLogFileContents.set(readChatLogFile());
    }

    // Track client/player state for context
    private Map<String, Object> getClientState() {
        Map<String, Object> state = new HashMap<>();
        state.put("playerName", mc.player != null ? mc.player.getName().getString() : "");
        state.put("pos", mc.player != null ? mc.player.getBlockPos().toShortString() : "");
        state.put("health", mc.player != null ? mc.player.getHealth() : -1);
        state.put("dimension", mc.player != null && mc.player.getWorld() != null ? mc.player.getWorld().getRegistryKey().getValue().toString() : "");
        // Add more as needed
        return state;
    }

    @EventHandler
    private void onMessageReceive(ReceiveMessageEvent event) {
        if (mc.player == null) return;
        String rawMsg = event.getMessage().getString();
        String msg = stripFormatting(rawMsg);
        String sender = "";
        boolean isWhisper = false;
        boolean isCommand = false;
        // Detect public chat: <PlayerName> message
        if (msg.startsWith("<")) {
            int end = msg.indexOf('>');
            if (end > 1) sender = msg.substring(1, end);
            logChat(sender, msg, false);
        }
        // Detect whispers: "You whisper to ...: ..." or "[Player] whispers: ..."
        else if (msg.toLowerCase().contains("whisper") || msg.toLowerCase().contains("tell") || msg.toLowerCase().contains("msg")) {
            isWhisper = true;
            // Try to extract sender from common whisper formats
            if (msg.toLowerCase().startsWith("you whisper to ")) {
                int toIdx = msg.indexOf(":");
                if (toIdx > 0) {
                    sender = mc.player.getName().getString();
                    msg = msg.substring(toIdx + 1).trim();
                }
            } else {
                int idx = msg.indexOf(" whispers:");
                if (idx > 0) {
                    sender = msg.substring(0, idx);
                    msg = msg.substring(idx + 10).trim();
                }
            }
            logChat(sender, msg, true);
        }
        String identifier = commandIdentifier.get();
        String askPrefix = identifier + askCommand.get().replaceFirst("^[&!$]", "");
        String tpaPrefix = identifier + tpaCommand.get().replaceFirst("^[&!$]", "");
        String modelsCmd = identifier + "ollama models";
        if (!whitelist.get().contains(sender)) return;
        // Show toast for any recognized command or chat message with prefix
        if (msg.startsWith(askPrefix + " ") || msg.startsWith(tpaPrefix + " ") || msg.equalsIgnoreCase(modelsCmd) || msg.equalsIgnoreCase("/ollama models")) {
            isCommand = true;
            ChatUtils.info("[OllamaBot] Received: " + msg);
        }
        // List models command
        if (msg.equalsIgnoreCase(modelsCmd) || msg.equalsIgnoreCase("/ollama models")) {
            sendChat("[OllamaBot] Available models: " + String.join(", ", availableModels.get()));
            return;
        }
        // &ask [model] question OR &ask question
        if (msg.startsWith(askPrefix + " ")) {
            String rest = msg.substring(askPrefix.length()).trim();
            String[] parts = rest.split(" ", 2);
            String model = ollamaModel.get();
            String question = rest;
            if (parts.length == 2 && availableModels.get().contains(parts[0])) {
                model = parts[0];
                question = parts[1];
            }
            // Model whitelist enforcement
            if (!modelWhitelist.get().isEmpty() && !modelWhitelist.get().contains(model)) {
                sendChat("[OllamaBot] Model not allowed: " + model);
                return;
            }
            final String finalSender = sender;
            final String finalModel = model;
            final String finalQuestion = question;
            final Map<String, Object> context = getClientState();
            event.cancel();
            executor.submit(() -> handleOllamaQuestion(finalSender, finalQuestion, finalModel, context));
        } else if (msg.startsWith(tpaPrefix + " ")) {
            String target = msg.substring(tpaPrefix.length()).trim();
            if (target.equalsIgnoreCase(mc.player.getName().getString())) {
                sendChat(tpacceptCommand.get());
            }
        }
    }

    // Utility: Strip Minecraft formatting codes (color, bold, etc)
    private String stripFormatting(String input) {
        return input.replaceAll("§[0-9A-FK-ORa-fk-or]", "").replaceAll("&[0-9A-FK-ORa-fk-or]", "");
    }

    private void sendChat(String message) {
        if (mc.player != null) {
            mc.player.networkHandler.sendChatMessage(message);
        }
    }

    private void handleOllamaQuestion(String sender, String question, String model, Map<String, Object> context) {
        if (model.isEmpty()) {
            sendChat("[OllamaBot] No model selected. Use settings to select one.");
            return;
        }
        try {
            String response = askOllama(model, question, systemPrompt.get(), context);
            sendChat("[Ollama] " + sender + ": " + response);
        } catch (Exception e) {
            sendChat("[OllamaBot] Error: " + e.getMessage());
        }
    }

    private String askOllama(String model, String prompt, String systemPrompt, Map<String, Object> context) throws IOException {
        String url = ollamaHost.get() + "/api/generate";
        // Add context to prompt
        String contextStr = context != null ? "\n[CONTEXT] " + context.toString() : "";
        String body = String.format("{\"model\":\"%s\",\"prompt\":\"%s%s\",\"system\":\"%s\"}",
            model, prompt.replace("\"", "\\\""), contextStr.replace("\"", "\\\""), systemPrompt.replace("\"", "\\\""));
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);
        try (OutputStream os = conn.getOutputStream()) {
            os.write(body.getBytes());
        }
        try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) sb.append(line);
            // Parse response (assume JSON with { "response": "..." })
            String resp = sb.toString();
            int idx = resp.indexOf("\"response\":");
            if (idx != -1) {
                int start = resp.indexOf('"', idx + 12) + 1;
                int end = resp.indexOf('"', start);
                if (start > 0 && end > start) return resp.substring(start, end);
            }
            return "[No response]";
        }
    }

    // Utility: Read contents of ollama-models.txt for display
    private String readModelsFile() {
        File modelsFile = getModelsFile();
        if (!modelsFile.exists()) return "(No ollama-models.txt found)";
        try (BufferedReader br = new BufferedReader(new FileReader(modelsFile))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
            return sb.toString();
        } catch (Exception e) {
            return "(Error reading ollama-models.txt)";
        }
    }
    // Read contents of ollama-whitelist.txt for display
    private String readWhitelistFile() {
        File whitelistFile = getWhitelistFile();
        if (!whitelistFile.exists()) return "(No ollama-whitelist.txt found)";
        try (BufferedReader br = new BufferedReader(new FileReader(whitelistFile))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
            return sb.toString();
        } catch (Exception e) {
            return "(Error reading ollama-whitelist.txt)";
        }
    }
    // Read contents of ollama-chatlog.txt for display
    private String readChatLogFile() {
        File chatLogFile = getChatLogFile();
        if (!chatLogFile.exists()) return "(No ollama-chatlog.txt found)";
        try (BufferedReader br = new BufferedReader(new FileReader(chatLogFile))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append("\n");
            }
            return sb.toString();
        } catch (Exception e) {
            return "(Error reading ollama-chatlog.txt)";
        }
    }
}
