package dev.journey.Skylandia.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

public class NotificationManager {
    private static final ConcurrentLinkedQueue<DetectionNotification> notifications = new ConcurrentLinkedQueue<>();
    private static final int MAX_NOTIFICATIONS = 100;

    public static void addNotification(DetectionNotification notification) {
        if (notifications.size() >= MAX_NOTIFICATIONS) {
            notifications.poll();
        }
        notifications.offer(notification);
    }

    public static List<DetectionNotification> getNotifications() {
        return new ArrayList<>(notifications);
    }

    public static void clearNotifications() {
        notifications.clear();
    }

    public static List<DetectionNotification> getNotificationsByPriority(NotificationPriority priority) {
        List<DetectionNotification> result = new ArrayList<>();
        for (DetectionNotification notification : notifications) {
            if (notification.getPriority() == priority) {
                result.add(notification);
            }
        }
        return result;
    }

    public static int getNotificationCount() {
        return notifications.size();
    }
}