package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class TunnelEntranceDetector extends PatternDetector {
    private static final Direction[] HORIZONTAL_DIRECTIONS = {
        Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST
    };

    private static final int MIN_ENTRANCE_HEIGHT = 2;
    private static final int MIN_ENTRANCE_WIDTH = 2;

    public TunnelEntranceDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Direction entranceDir = findEntranceDirection(pos);
                    if (entranceDir != null) {
                        float confidence = calculateConfidence(pos, entranceDir);
                        if (confidence > 0.4f) {  // Threshold for detection
                            results.add(createResult(
                                "Potential tunnel entrance",
                                pos,
                                DetectionResult.DetectionType.STRUCTURE,
                                confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private Direction findEntranceDirection(BlockPos center) {
        for (Direction dir : HORIZONTAL_DIRECTIONS) {
            if (isValidEntrance(center, dir)) {
                return dir;
            }
        }
        return null;
    }

    private boolean isValidEntrance(BlockPos center, Direction direction) {
        // Check if we have an open space
        if (!super.isAirBlock(center)) return false;

        // Check for minimum height
        for (int h = 0; h < MIN_ENTRANCE_HEIGHT; h++) {
            if (!super.isAirBlock(center.up(h))) return false;
        }

        // Check for valid floor
        if (!super.isValidSolidBlock(center.down())) return false;

        // Check for walls on sides
        Direction[] sides = getSidePerpendicular(direction);
        int wallCount = 0;
        for (Direction side : sides) {
            if (hasWallPattern(center, side)) {
                wallCount++;
            }
        }

        return wallCount >= 1;
    }

    private boolean hasWallPattern(BlockPos pos, Direction side) {
        BlockPos wallPos = pos.offset(side);
        
        // Check for solid blocks in wall pattern
        int solidCount = 0;
        for (int h = 0; h < MIN_ENTRANCE_HEIGHT; h++) {
            if (super.isValidSolidBlock(wallPos.up(h))) {
                solidCount++;
            }
        }
        
        return solidCount >= MIN_ENTRANCE_HEIGHT - 1;
    }

    private Direction[] getSidePerpendicular(Direction dir) {
        if (dir == Direction.NORTH || dir == Direction.SOUTH) {
            return new Direction[] { Direction.EAST, Direction.WEST };
        } else {
            return new Direction[] { Direction.NORTH, Direction.SOUTH };
        }
    }

    private float calculateConfidence(BlockPos center, Direction direction) {
        float confidence = 0.4f; // Base confidence for valid entrance

        // Check for signs of player modification
        if (hasPlayerBlocks(center, direction)) {
            confidence += 0.2f;
        }

        // Check for symmetry
        if (hasSymmetry(center, direction)) {
            confidence += 0.2f;
        }

        // Check for extended tunnel
        confidence += calculateTunnelDepthBonus(center, direction);

        return Math.min(confidence, 1.0f);
    }

    private boolean hasPlayerBlocks(BlockPos center, Direction direction) {
        // Look for blocks that are likely placed by players
        Direction[] sides = getSidePerpendicular(direction);
        for (Direction side : sides) {
            BlockPos wallPos = center.offset(side);
            if (super.isAnyBlockType(wallPos,
                Blocks.STONE_BRICKS,
                Blocks.SMOOTH_STONE,
                Blocks.COBBLESTONE_STAIRS,
                Blocks.STONE_BRICK_STAIRS
            )) {
                return true;
            }
        }
        return false;
    }

    private boolean hasSymmetry(BlockPos center, Direction direction) {
        Direction[] sides = getSidePerpendicular(direction);
        BlockState firstSide = world.getBlockState(center.offset(sides[0]));
        BlockState secondSide = world.getBlockState(center.offset(sides[1]));
        
        return firstSide.getBlock() == secondSide.getBlock();
    }

    private float calculateTunnelDepthBonus(BlockPos start, Direction direction) {
        float bonus = 0.0f;
        int depth = 0;
        BlockPos current = start;
        
        while (depth < 10) {
            current = current.offset(direction);
            if (!super.isAirBlock(current) || !super.isAirBlock(current.up())) {
                break;
            }
            depth++;
        }
        
        // Add bonus based on tunnel depth
        return Math.min(0.2f, depth * 0.02f);
    }
}
