package dev.journey.Skylandia.modules.exploration;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.minecraft.block.Block;
import net.minecraft.registry.Registries;
import net.minecraft.util.Identifier;

import java.util.ArrayList;
import java.util.List;

public class SearchRule {
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    
    public String name;
    public String description;
    public String author;
    public List<String> blocks;  // Block IDs
    public BlockPattern pattern;
    public SearchCondition condition;

    public static class BlockPattern {
        public String[][] pattern;  // Block IDs in pattern
        public int rotations;       // Number of rotations to check (1-4)
        public boolean ignoreAir;   // Whether to ignore air blocks in pattern
    }

    public static class SearchCondition {
        public double minGradient = 0.0;
        public int minY = -64;
        public int maxY = 320;
        public int minSurroundingSolid = 0;
        public int requiredBlockCount = 1;
        public boolean requireAllBlocks = false;
        public boolean checkRedstone = false;
    }

    public SearchRule() {
        this.blocks = new ArrayList<>();
        this.pattern = new BlockPattern();
        this.condition = new SearchCondition();
    }

    public boolean isValid() {
        return name != null && !name.isEmpty() && 
               blocks != null && !blocks.isEmpty();
    }

    public String serialize() {
        return GSON.toJson(this);
    }

    public static SearchRule deserialize(String json) {
        try {
            return GSON.fromJson(json, SearchRule.class);
        } catch (Exception e) {
            return null;
        }
    }

    public static String encodeForDiscord(SearchRule rule) {
        // Create a compact string format for Discord messages
        StringBuilder sb = new StringBuilder();
        sb.append("```json\n");
        sb.append(rule.serialize());
        sb.append("\n```");
        return sb.toString();
    }

    public static SearchRule decodeFromDiscord(String message) {
        // Extract JSON from Discord code block
        String json = message.replaceAll("```json\\n", "")
                           .replaceAll("```", "")
                           .trim();
        return deserialize(json);
    }

    public boolean matchesBlock(Block block) {
        String blockId = Registries.BLOCK.getId(block).toString();
        return blocks.contains(blockId);
    }

    public Block[][] getPatternBlocks() {
        if (pattern == null || pattern.pattern == null) return null;
        
        Block[][] blocks = new Block[pattern.pattern.length][];
        for (int i = 0; i < pattern.pattern.length; i++) {
            blocks[i] = new Block[pattern.pattern[i].length];
            for (int j = 0; j < pattern.pattern[i].length; j++) {
                String blockId = pattern.pattern[i][j];
                if (blockId == null || blockId.equals("minecraft:air") && pattern.ignoreAir) {
                    blocks[i][j] = null;
                } else {
                    blocks[i][j] = Registries.BLOCK.get(Identifier.tryParse(blockId));
                }
            }
        }
        return blocks;
    }
}