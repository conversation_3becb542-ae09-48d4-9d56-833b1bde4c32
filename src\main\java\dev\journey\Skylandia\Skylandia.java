package dev.journey.Skylandia;

import dev.journey.Skylandia.commands.MeteorFolderCommand;
import dev.journey.Skylandia.commands.ScreenshotFolderCommand;
import dev.journey.Skylandia.commands.Stats2b2t;
import dev.journey.Skylandia.modules.automation.*;
import dev.journey.Skylandia.modules.exploration.*;
import dev.journey.Skylandia.modules.render.*;
import dev.journey.Skylandia.modules.utility.*;
import dev.journey.Skylandia.utils.Update.UpdateChecker;
import meteordevelopment.meteorclient.addons.MeteorAddon;
import meteordevelopment.meteorclient.commands.Commands;
import meteordevelopment.meteorclient.events.game.GameLeftEvent;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.orbit.EventHandler;
import net.fabricmc.loader.api.FabricLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static meteordevelopment.meteorclient.MeteorClient.EVENT_BUS;


public class Skylandia extends MeteorAddon {
    public static final String MOD_ID = "skylandia";
    public static final Logger LOG = LoggerFactory.getLogger(Skylandia.class);
    //public static final Category Main = new Category("Skylandia");
    public static final Category Hunting = new Category("SkyHunting");
    public static final Category Utility = new Category("SkyUtils");
    public static final Category Automation = new Category("SkyAutomation");
    public static final Category Render = new Category("SkyRender");

    @Override
    public void onInitialize() {
        LOG.info("Initializing Skylandia!");
        
        // Initialize WebhookManager
        dev.journey.Skylandia.utils.WebhookManager.init();

        EVENT_BUS.subscribe(this);

        //Hunting
        Modules.get().add(new ActivatedSpawnerDetector());
        Modules.get().add(new StackedMinecartsDetector());
        Modules.get().add(new CaveDisturbanceDetector());
        Modules.get().add(new PortalPatternFinder());
        Modules.get().add(new Trails());
        Modules.get().add(new BaseFinder());

        // Exploration
        Modules.get().add(new StashBotModule());
        //Utility
        Modules.get().add(new GrimDuraFirework());
        Modules.get().add(new SignHistorian());
        Modules.get().add(new Pitch40Util());
        Modules.get().add(new GrimEfly());
        Modules.get().add(new SecureChatModule());
        Modules.get().add(new AutoLoginModule());
        Modules.get().add(new OllamaBotModule());

        //Render
        Modules.get().add(new HoleAndTunnelAndStairsESP());
        Modules.get().add(new PotESP());
        Modules.get().add(new MobGearESP());
        Modules.get().add(new DroppedItemESP());
        Modules.get().add(new EntityClusterESP());
        Modules.get().add(new VanityESP());

        //Automation
        Modules.get().add(new ElytraSwap());
        Modules.get().add(new TridentDupe());
        Modules.get().add(new Firework());
        Modules.get().add(new AutoEnchant());
        Modules.get().add(new AreaLoader());
        Modules.get().add(new AFKVanillaFly());
        Modules.get().add(new BaritonePathing());
        Modules.get().add(new TridentAura());
        Modules.get().add(new AutoPortal());
        Modules.get().add(new ItemFrameDupe());
        Modules.get().add(new AutoDropDupe());  // Add new dupe automation module
        Modules.get().add(new duprexion());
        Modules.get().add(new AutomationModule());
        Modules.get().add(new ShulkerTransportModule());
        Modules.get().add(new SmartShulkerManager());

        /* To Release

        Modules.get().add(new ChestIndex());

        */

        //Commands
        Commands.add(new MeteorFolderCommand());
        Commands.add(new ScreenshotFolderCommand());
        Commands.add(new Stats2b2t());

        if (FabricLoader.getInstance().isModLoaded("xaeroworldmap") && FabricLoader.getInstance().isModLoaded("xaerominimap")) {

            Modules.get().add(new BetterStashFinder());
            Modules.get().add(new OldChunkNotifier());
            Modules.get().add(new TrailFollower());
            Modules.get().add(new SearchBot());
            Modules.get().add(new TerrainAnalyzer());
        } else {
            LOG.info("Xaeros minimap and world map not found, disabling modules that require it.");
        }
    }

    @EventHandler
    private void onGameLeft(GameLeftEvent event) {
        UpdateChecker.resetCheckedStatus();
    }

    @Override
    public void onRegisterCategories() {
        Modules.registerCategory(Hunting);
        Modules.registerCategory(Automation);
        Modules.registerCategory(Render);
        Modules.registerCategory(Utility);
    }

    public String getPackage() {
        return "dev.journey.Skylandia";
    }
}