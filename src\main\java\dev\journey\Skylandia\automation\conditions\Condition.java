package dev.journey.Skylandia.automation.conditions;

import dev.journey.Skylandia.automation.AutomationFlow;

@FunctionalInterface
public interface Condition {
    boolean test(AutomationFlow flow);

    default Condition and(Condition other) {
        return flow -> test(flow) && other.test(flow);
    }

    default Condition or(Condition other) {
        return flow -> test(flow) || other.test(flow);
    }

    default Condition not() {
        return flow -> !test(flow);
    }
}