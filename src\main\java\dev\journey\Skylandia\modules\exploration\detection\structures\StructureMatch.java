package dev.journey.Skylandia.modules.exploration.detection.structures;

import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;

public class StructureMatch {
    private final StructureType type;
    private final BlockPos center;
    private final double confidence;
    private final Box bounds;
    private final String details;

    public StructureMatch(StructureType type, BlockPos center, double confidence, Box bounds, String details) {
        this.type = type;
        this.center = center;
        this.confidence = confidence;
        this.bounds = bounds;
        this.details = details;
    }

    public StructureType getType() {
        return type;
    }

    public BlockPos getCenter() {
        return center;
    }

    public double getConfidence() {
        return confidence;
    }

    public Box getBounds() {
        return bounds;
    }

    public String getDetails() {
        return details;
    }

    @Override
    public String toString() {
        return String.format("%s at %s (%.2f%% confidence) - %s", 
            type.getDisplayName(), 
            center.toShortString(), 
            confidence * 100,
            details);
    }
}