package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import dev.journey.Skylandia.utils.BlockPosUtils;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class TunnelDetector extends PatternDetector {
    private static final int MIN_TUNNEL_LENGTH = 4;
    private static final Direction[] HORIZONTAL_DIRECTIONS = {
        Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST
    };

    public TunnelDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (super.isAirBlock(pos)) {
                        TunnelInfo info = findTunnel(pos);
                        if (info != null && !isAlreadyDetected(results, info.center)) {
                            results.add(DetectionResult.fromPositions(
                                String.format("Tunnel detected (length: %d)", info.length),
                                info.center,
                                BlockPosUtils.toVec3d(info.center),
                                DetectionResult.DetectionType.STRUCTURE,
                                info.confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private TunnelInfo findTunnel(BlockPos start) {
        for (Direction direction : HORIZONTAL_DIRECTIONS) {
            TunnelInfo info = analyzeTunnel(start, direction);
            if (info != null) {
                return info;
            }
        }
        return null;
    }

    private TunnelInfo analyzeTunnel(BlockPos start, Direction direction) {
        int length = 0;
        BlockPos current = start;
        List<BlockPos> tunnelBlocks = new ArrayList<>();
        tunnelBlocks.add(start);

        while (length < 16) { // Maximum reasonable tunnel length to check
            BlockPos next = current.offset(direction);
            if (!isValidTunnelSection(next)) {
                break;
            }

            tunnelBlocks.add(next);
            current = next;
            length++;
        }

        if (length >= MIN_TUNNEL_LENGTH) {
            BlockPos center = BlockPosUtils.centerOf(tunnelBlocks.toArray(new BlockPos[0]));
            float confidence = calculateConfidence(length, tunnelBlocks);
            return new TunnelInfo(center, length, confidence);
        }

        return null;
    }

    private boolean isValidTunnelSection(BlockPos pos) {
        // Check for walkable space
        if (!super.isAirBlock(pos) || !super.isAirBlock(pos.up())) return false;

        // Check for valid floor
        if (!super.isValidSolidBlock(pos.down())) return false;

        // Check for walls on at least one side
        boolean hasWall = false;
        for (Direction dir : HORIZONTAL_DIRECTIONS) {
            if (super.isValidSolidBlock(pos.offset(dir))) {
                hasWall = true;
                break;
            }
        }

        return hasWall;
    }

    private float calculateConfidence(int length, List<BlockPos> blocks) {
        float confidence = 0.5f; // Base confidence for meeting minimum length

        // Longer tunnels increase confidence
        confidence += Math.min(0.1f * (length - MIN_TUNNEL_LENGTH), 0.3f);

        // Check for regular spacing and alignment
        if (hasRegularPattern(blocks)) {
            confidence += 0.2f;
        }

        return Math.min(confidence, 1.0f);
    }

    private boolean hasRegularPattern(List<BlockPos> blocks) {
        if (blocks.size() < 3) return false;

        // Calculate the average distance between sections
        double totalDistance = 0;
        for (int i = 1; i < blocks.size(); i++) {
            totalDistance += blocks.get(i).getSquaredDistance(blocks.get(i - 1));
        }
        double averageDistance = totalDistance / (blocks.size() - 1);

        // Check if distances are consistent
        for (int i = 1; i < blocks.size(); i++) {
            double distance = blocks.get(i).getSquaredDistance(blocks.get(i - 1));
            if (Math.abs(distance - averageDistance) > 2.0) {
                return false;
            }
        }

        return true;
    }

    private boolean isAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (BlockPosUtils.isWithinRange(result.getPosition(), pos, 64)) { // Within 8 blocks
                return true;
            }
        }
        return false;
    }

    private static class TunnelInfo {
        final BlockPos center;
        final int length;
        final float confidence;

        TunnelInfo(BlockPos center, int length, float confidence) {
            this.center = center;
            this.length = length;
            this.confidence = confidence;
        }
    }
}
