package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CompositeDetector extends PatternDetector {
    private static final double CLUSTER_RADIUS = 32.0;
    private final List<PatternDetector> subDetectors;

    public CompositeDetector(World world) {
        super(world);
        this.subDetectors = new ArrayList<>();
        initializeDetectors();
    }

    private void initializeDetectors() {
        // Add specific detectors we want to combine
        subDetectors.add(new TunnelDetector(world));
        subDetectors.add(new TunnelEntranceDetector(world));
        subDetectors.add(new VillageHouseDetector(world));
        subDetectors.add(new VillageAnomalyDetector(world));
        subDetectors.add(new PortalFrameDetector(world));
        subDetectors.add(new BedrockStaircaseDetector(world));
        subDetectors.add(new PistonDoorDetector(world));
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();
        Map<BlockPos, List<DetectionResult>> clusters = new HashMap<>();

        // Collect results from all sub-detectors
        for (PatternDetector detector : subDetectors) {
            List<DetectionResult> detectorResults = detector.detect(center, radius);
            for (DetectionResult result : detectorResults) {
                addToCluster(clusters, result);
            }
        }

        // Analyze clusters and generate composite results
        for (Map.Entry<BlockPos, List<DetectionResult>> entry : clusters.entrySet()) {
            if (entry.getValue().size() > 1) { // Only consider areas with multiple detections
                DetectionResult compositeResult = createCompositeResult(entry.getKey(), entry.getValue());
                results.add(compositeResult);
            }
        }

        return results;
    }

    private void addToCluster(Map<BlockPos, List<DetectionResult>> clusters, DetectionResult result) {
        BlockPos resultPos = result.getPosition();
        boolean addedToExisting = false;

        // Try to add to existing cluster
        for (Map.Entry<BlockPos, List<DetectionResult>> entry : clusters.entrySet()) {
            if (isWithinClusterRadius(resultPos, entry.getKey())) {
                entry.getValue().add(result);
                addedToExisting = true;
                break;
            }
        }

        // Create new cluster if not added to existing one
        if (!addedToExisting) {
            List<DetectionResult> newCluster = new ArrayList<>();
            newCluster.add(result);
            clusters.put(resultPos, newCluster);
        }
    }

    private boolean isWithinClusterRadius(BlockPos pos1, BlockPos pos2) {
        return pos1.getSquaredDistance(pos2) <= CLUSTER_RADIUS * CLUSTER_RADIUS;
    }

    private DetectionResult createCompositeResult(BlockPos center, List<DetectionResult> clusterResults) {
        float totalConfidence = 0;
        float maxConfidence = 0;
        StringBuilder description = new StringBuilder("Complex structure detected: ");
        Map<DetectionResult.DetectionType, Integer> typeCounts = new HashMap<>();

        // Analyze results in cluster
        for (DetectionResult result : clusterResults) {
            // Track detection types
            typeCounts.merge(result.getType(), 1, Integer::sum);
            
            // Update confidences
            totalConfidence += result.getConfidence();
            maxConfidence = Math.max(maxConfidence, result.getConfidence());
            
            // Add to description
            description.append(result.getDescription()).append(", ");
        }

        // Remove trailing comma and space
        if (description.toString().endsWith(", ")) {
            description.setLength(description.length() - 2);
        }

        // Calculate composite confidence
        float compositeConfidence = calculateCompositeConfidence(
            clusterResults.size(),
            totalConfidence / clusterResults.size(),
            maxConfidence,
            typeCounts
        );

        return new DetectionResult(
            description.toString(),
            center,
            new Vec3d(center.getX() + 0.5, center.getY() + 0.5, center.getZ() + 0.5),
            determineCompositeType(typeCounts),
            compositeConfidence
        );
    }

    private float calculateCompositeConfidence(
        int resultCount,
        float averageConfidence,
        float maxConfidence,
        Map<DetectionResult.DetectionType, Integer> typeCounts
    ) {
        float confidence = 0.5f; // Base confidence

        // More results suggest higher likelihood of player activity
        confidence += Math.min(0.1f * resultCount, 0.3f);

        // High confidence in individual detections increases overall confidence
        confidence += averageConfidence * 0.2f;
        confidence += maxConfidence * 0.1f;

        // Multiple types of detections suggest more complex structures
        if (typeCounts.size() > 1) {
            confidence += 0.1f * typeCounts.size();
        }

        return Math.min(confidence, 1.0f);
    }

    private DetectionResult.DetectionType determineCompositeType(Map<DetectionResult.DetectionType, Integer> typeCounts) {
        // Find the most common type
        DetectionResult.DetectionType mostCommonType = null;
        int maxCount = 0;

        for (Map.Entry<DetectionResult.DetectionType, Integer> entry : typeCounts.entrySet()) {
            if (entry.getValue() > maxCount) {
                maxCount = entry.getValue();
                mostCommonType = entry.getKey();
            }
        }

        return mostCommonType != null ? mostCommonType : DetectionResult.DetectionType.STRUCTURE;
    }
}
