package dev.journey.Skylandia.automation.transport;

import dev.journey.Skylandia.modules.automation.ShulkerTransportModule;
import dev.journey.Skylandia.automation.AutomationFlow;
import dev.journey.Skylandia.automation.conditions.Condition;
import dev.journey.Skylandia.automation.actions.Action;
import net.minecraft.client.MinecraftClient;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Manages coordination between Transporter and Commander roles
 */
public class CoordinationManager {
    private static final String MSG_PREFIX = "TRANSPORT:";
    private static final long DEFAULT_TIMEOUT = 5000; // 5 seconds

    private final ShulkerTransportModule module;
    private final TransportConfig config;
    private final MinecraftClient mc;
    
    private CompletableFuture<Boolean> pendingResponse;
    private String lastMessageId;
    private long lastMessageTime;
    private String commanderName;
    private String transporterName;

    public CoordinationManager(ShulkerTransportModule module, TransportConfig config) {
        this.module = module;
        this.config = config;
        this.mc = MinecraftClient.getInstance();
    }

    /**
     * Update coordination state
     */
    public void update() {
        if (pendingResponse != null && !pendingResponse.isDone()) {
            long elapsed = System.currentTimeMillis() - lastMessageTime;
            if (elapsed > DEFAULT_TIMEOUT) {
                pendingResponse.complete(false);
            }
        }
    }

    /**
     * Handle incoming coordination message
     */
    public void handleMessage(String message) {
        if (!message.startsWith(MSG_PREFIX)) return;
        
        String[] parts = message.substring(MSG_PREFIX.length()).split("\\|");
        if (parts.length < 2) return;

        String messageId = parts[0];
        String command = parts[1];

        // Ignore own messages
        if (messageId.equals(lastMessageId)) return;

        try {
            switch (command) {
                case "READY":
                    handleReadySignal();
                    break;
                case "START":
                    handleStartSignal();
                    break;
                case "PEARL":
                    handlePearlSignal();
                    break;
                case "CHAMBER":
                    handleChamberSignal();
                    break;
                case "TRANSFER":
                    handleTransferSignal();
                    break;
                case "COMPLETE":
                    handleCompleteSignal();
                    break;
                case "ERROR":
                    handleErrorSignal(parts.length > 2 ? parts[2] : "Unknown error");
                    break;
            }
        } catch (Exception e) {
            module.error("Failed to handle message: " + e.getMessage());
            signalError(e.getMessage());
        }
    }

    /**
     * Create a condition that waits for transport readiness
     */
    public Condition readyForTransport() {
        return flow -> {
            if (!validateState()) return false;
            return sendAndWaitForResponse("READY");
        };
    }

    /**
     * Signal the start of transport
     */
    public TransportAction signalTransportStart() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!sendMessage("START")) {
                    throw TransportException.coordination(
                        "Failed to signal transport start",
                        module.getState()
                    );
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                signalError("Rolling back transport start");
            }
        };
    }

    /**
     * Wait for pearl throw confirmation
     */
    public Condition waitForPearl() {
        return flow -> {
            if (!validateState()) return false;
            return sendAndWaitForResponse("PEARL");
        };
    }

    /**
     * Signal chamber activation
     */
    public TransportAction confirmActivation() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!sendMessage("CHAMBER")) {
                    throw TransportException.coordination(
                        "Failed to confirm chamber activation",
                        module.getState()
                    );
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                signalError("Chamber activation failed");
            }
        };
    }

    /**
     * Wait for teleport completion
     */
    public Action waitForTeleport() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!sendAndWaitForResponse("CHAMBER", 10000)) {
                    throw TransportException.coordination(
                        "Teleport timeout",
                        module.getState()
                    );
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                signalError("Teleport failed");
            }
        };
    }

    /**
     * Signal transport completion
     */
    public TransportAction signalTransportComplete() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!sendMessage("COMPLETE")) {
                    throw TransportException.coordination(
                        "Failed to signal completion",
                        module.getState()
                    );
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                // No rollback needed for completion signal
            }
        };
    }

    /**
     * Attempt recovery from error
     */
    public void attemptRecovery(TransportState previousState) {
        ((ShulkerTransportModule)module).setState(previousState);
        sendMessage("READY");
    }

    /**
     * Shutdown coordination
     */
    public void shutdown() {
        if (pendingResponse != null) {
            pendingResponse.complete(false);
        }
    }

    /**
     * Validate the current state for coordination operations
     * @return true if state is valid
     * @throws TransportException if state is invalid
     */
    public boolean validateState() {
        if (mc.player == null || mc.world == null) {
            throw new TransportException(
                "Player or world not available",
                module.getState(),
                TransportCondition.TransportFailureSeverity.CRITICAL
            );
        }
        return true;
    }

    private void signalError(String reason) {
        sendMessage("ERROR|" + reason);
    }

    /**
     * Set commander player name
     */
    public void setCommanderName(String name) {
        this.commanderName = name;
    }

    /**
     * Set transporter player name
     */
    public void setTransporterName(String name) {
        this.transporterName = name;
    }

    /**
     * Get commander name
     */
    public String getCommanderName() {
        return commanderName;
    }

    /**
     * Get transporter name
     */
    public String getTransporterName() {
        return transporterName;
    }

    /**
     * Execute start sequence for current mode
     */
    public void executeStartSequence() {
        if (config.mode.get() == TransportMode.COMMANDER) {
            module.executeCommandList(config.commanderStartSequence.get(), transporterName);
        } else {
            module.executeCommandList(config.transporterAcceptSequence.get(), commanderName);
        }
    }

    /**
     * Execute end sequence for current mode
     */
    public void executeEndSequence() {
        if (config.mode.get() == TransportMode.COMMANDER) {
            module.executeCommandList(config.commanderEndSequence.get(), transporterName);
        } else {
            module.executeCommandList(config.transporterCompleteSequence.get(), commanderName);
        }
    }

    private boolean sendMessage(String command) {
        try {
            lastMessageId = String.valueOf(System.currentTimeMillis());
            lastMessageTime = System.currentTimeMillis();
            
            String fullMessage = MSG_PREFIX + lastMessageId + "|" + command;
            module.sendTransportMessage(command + "|" + lastMessageId);
            return true;
        } catch (Exception e) {
            module.error("Failed to send message: " + e.getMessage());
            return false;
        }
    }

    private boolean sendAndWaitForResponse(String command) {
        return sendAndWaitForResponse(command, DEFAULT_TIMEOUT);
    }

    private boolean sendAndWaitForResponse(String command, long timeout) {
        if (pendingResponse != null && !pendingResponse.isDone()) {
            throw TransportException.coordination(
                "Already waiting for response",
                module.getState()
            );
        }

        pendingResponse = new CompletableFuture<>();
        if (!sendMessage(command)) {
            pendingResponse.complete(false);
            return false;
        }

        try {
            return pendingResponse.get(timeout, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            module.error("Timeout waiting for response");
            return false;
        }
    }

    private void handleReadySignal() {
        if (config.mode.get().isCoordinator()) {
            sendMessage("READY");
        }
        completeResponse(true);
    }

    private void handleStartSignal() {
        if (!config.mode.get().isCoordinator()) {
            setTransportState(TransportState.PREPARING_CHAMBER);
            executeStartSequence();
        }
        completeResponse(true);
    }

    private void handlePearlSignal() {
        if (!config.mode.get().isCoordinator()) {
            setTransportState(TransportState.PEARL_THROWN);
        }
        completeResponse(true);
    }

    private void setTransportState(TransportState state) {
        module.getConfig().active.set(true);
        ((ShulkerTransportModule)module).setState(state);
        module.sendTransportMessage("State: " + state.getDisplayName());
    }

    private void handleChamberSignal() {
        if (config.mode.get().isCoordinator()) {
            setTransportState(TransportState.TELEPORTING);
        }
        completeResponse(true);
    }

    private void handleTransferSignal() {
        setTransportState(TransportState.TRANSFERRING);
        completeResponse(true);
    }

    private void handleCompleteSignal() {
        setTransportState(TransportState.COMPLETE);
        executeEndSequence();
        completeResponse(true);
    }

    private void handleErrorSignal(String reason) {
        module.error("Received error signal: " + reason);
        completeResponse(false);
    }

    private void completeResponse(boolean success) {
        if (pendingResponse != null && !pendingResponse.isDone()) {
            pendingResponse.complete(success);
        }
    }

    /**
     * Get condition for received start signal
     */
    public Condition receivedStartSignal() {
        return flow -> {
            if (!validateState()) return false;
            return sendAndWaitForResponse("START");
        };
    }

    /**
     * Convert pearl wait condition to action
     */
    public Action waitForPearlAsAction() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState()) {
                    throw TransportException.safety("Invalid state for pearl wait", module.getState());
                }
                if (!sendAndWaitForResponse("PEARL")) {
                    throw TransportException.coordination(
                        "Pearl wait timeout",
                        module.getState()
                    );
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return true;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                signalError("Pearl wait cancelled");
            }
        };
    }

    /**
     * Get action for confirming transport complete
     */
    public Action confirmTransportComplete() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!sendMessage("COMPLETE")) {
                    throw TransportException.coordination(
                        "Failed to confirm completion",
                        module.getState()
                    );
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                // No rollback needed
            }
        };
    }
}
