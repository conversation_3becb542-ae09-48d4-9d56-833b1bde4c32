package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class PistonDoorDetector extends PatternDetector {
    private static final Block[] PISTON_BLOCKS = {
        Blocks.PISTON,
        Blocks.STICKY_PISTON,
        Blocks.MOVING_PISTON
    };

    private static final Block[] REDSTONE_BLOCKS = {
        Blocks.REDSTONE_WIRE,
        Blocks.REDSTONE_BLOCK,
        Blocks.REDSTONE_TORCH,
        Blocks.REDSTONE_WALL_TORCH,
        Blocks.REPEATER,
        Blocks.COMPARATOR,
        Blocks.LEVER,
        Blocks.OBSERVER
    };

    public PistonDoorDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (isPistonDoorComponent(pos)) {
                        BlockPos doorCenter = findDoorCenter(pos);
                        if (doorCenter != null && !isDoorAlreadyDetected(results, doorCenter)) {
                            results.add(new DetectionResult(
                                "Potential piston door mechanism",
                                doorCenter,
                                new Vec3d(doorCenter.getX() + 0.5, doorCenter.getY() + 0.5, doorCenter.getZ() + 0.5),
                                DetectionResult.DetectionType.STRUCTURE,
                                calculateConfidence(doorCenter)
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private boolean isPistonDoorComponent(BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        return isPistonBlock(state.getBlock()) || isRedstoneComponent(state.getBlock());
    }

    private boolean isPistonBlock(Block block) {
        for (Block pistonBlock : PISTON_BLOCKS) {
            if (block == pistonBlock) return true;
        }
        return false;
    }

    private boolean isRedstoneComponent(Block block) {
        for (Block redstoneBlock : REDSTONE_BLOCKS) {
            if (block == redstoneBlock) return true;
        }
        return false;
    }

    private BlockPos findDoorCenter(BlockPos start) {
        // Look for a pattern of pistons and redstone components
        int pistonCount = 0;
        int redstoneCount = 0;
        BlockPos center = null;

        for (int x = -2; x <= 2; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos pos = start.add(x, y, z);
                    Block block = world.getBlockState(pos).getBlock();

                    if (isPistonBlock(block)) {
                        pistonCount++;
                        if (center == null) center = pos;
                    } else if (isRedstoneComponent(block)) {
                        redstoneCount++;
                    }
                }
            }
        }

        // A piston door typically has at least 2 pistons and some redstone
        if (pistonCount >= 2 && redstoneCount >= 1) {
            return center != null ? center : start;
        }

        return null;
    }

    private boolean isDoorAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (result.getPosition().getSquaredDistance(pos) < 16) { // Within 4 blocks
                return true;
            }
        }
        return false;
    }

    private float calculateConfidence(BlockPos center) {
        float confidence = 0.5f; // Base confidence

        // Count nearby components
        int pistonCount = 0;
        int redstoneCount = 0;

        for (int x = -2; x <= 2; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Block block = world.getBlockState(pos).getBlock();

                    if (isPistonBlock(block)) {
                        pistonCount++;
                    } else if (isRedstoneComponent(block)) {
                        redstoneCount++;
                    }
                }
            }
        }

        // Adjust confidence based on component count
        confidence += Math.min(0.1f * pistonCount, 0.3f);
        confidence += Math.min(0.05f * redstoneCount, 0.2f);

        return Math.min(confidence, 1.0f);
    }
}
