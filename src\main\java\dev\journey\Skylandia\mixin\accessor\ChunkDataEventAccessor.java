package dev.journey.Skylandia.mixin.accessor;

import meteordevelopment.meteorclient.events.world.ChunkDataEvent;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.gen.Accessor;
import net.minecraft.world.chunk.WorldChunk;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.gen.Accessor;

@Mixin(value = ChunkDataEvent.class)
public interface ChunkDataEventAccessor {
    @Accessor(value = "chunk")
    WorldChunk getChunk();
}