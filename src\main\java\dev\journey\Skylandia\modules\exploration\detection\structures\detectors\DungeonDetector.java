package dev.journey.Skylandia.modules.exploration.detection.structures.detectors;

import dev.journey.Skylandia.modules.exploration.detection.structures.StructureDetector;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import net.minecraft.block.Blocks;
import net.minecraft.block.BlockState;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.block.entity.MobSpawnerBlockEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;

import java.util.ArrayList;
import java.util.List;

public class DungeonDetector extends StructureDetector {
    private static final BlockState MOSSY_COBBLESTONE = Blocks.MOSSY_COBBLESTONE.getDefaultState();
    private static final BlockState COBBLESTONE = Blocks.COBBLESTONE.getDefaultState();
    private static final BlockState SPAWNER = Blocks.SPAWNER.getDefaultState();

    public DungeonDetector(World world, double confidenceThreshold, int maxSearchRadius) {
        super(world, confidenceThreshold, maxSearchRadius);
    }

    @Override
    public StructureType getStructureType() {
        return StructureType.DUNGEON;
    }

    @Override
    public List<StructureMatch> detectInChunk(Chunk chunk) {
        List<StructureMatch> matches = new ArrayList<>();
        int chunkX = chunk.getPos().x * 16;
        int chunkZ = chunk.getPos().z * 16;

        // Dungeons are typically found underground
        for (int x = chunkX; x < chunkX + 16; x++) {
            for (int z = chunkZ; z < chunkZ + 16; z++) {
                for (int y = 4; y < world.getHeight(); y++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    BlockState state = world.getBlockState(pos);

                    if (state.isOf(Blocks.SPAWNER)) {
                        BlockEntity be = world.getBlockEntity(pos);
                        if (be instanceof MobSpawnerBlockEntity) {
                            double confidence = calculateDungeonConfidence(pos);
                            if (confidence >= confidenceThreshold) {
                                Box bounds = calculateBounds(pos, 9, 5, 9); // Standard dungeon size
                                String details = String.format("Dungeon with spawner at Y=%d", y);
                                matches.add(new StructureMatch(
                                    getStructureType(),
                                    pos,
                                    confidence,
                                    bounds,
                                    details
                                ));
                            }
                        }
                    }
                }
            }
        }

        return matches;
    }

    private double calculateDungeonConfidence(BlockPos spawnerPos) {
        int mossyCount = 0;
        int cobbleCount = 0;
        int totalBlocks = 0;

        // Check surrounding blocks in a 7x5x7 area
        for (int x = -3; x <= 3; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -3; z <= 3; z++) {
                    if (x == 0 && y == 0 && z == 0) continue; // Skip spawner position
                    
                    BlockPos checkPos = spawnerPos.add(x, y, z);
                    BlockState state = world.getBlockState(checkPos);
                    
                    if (state.isOf(Blocks.MOSSY_COBBLESTONE)) {
                        mossyCount++;
                        totalBlocks++;
                    } else if (state.isOf(Blocks.COBBLESTONE)) {
                        cobbleCount++;
                        totalBlocks++;
                    }
                }
            }
        }

        // Typical dungeons have a mix of mossy and regular cobblestone
        double mossyRatio = (double) mossyCount / totalBlocks;
        double cobbleRatio = (double) cobbleCount / totalBlocks;
        
        // Ideal ratios: ~30-40% mossy, ~60-70% cobble
        double idealMossyRatio = 0.35;
        double idealCobbleRatio = 0.65;
        
        double mossyConfidence = 1.0 - Math.abs(mossyRatio - idealMossyRatio);
        double cobbleConfidence = 1.0 - Math.abs(cobbleRatio - idealCobbleRatio);
        
        return (mossyConfidence + cobbleConfidence) / 2.0;
    }
}