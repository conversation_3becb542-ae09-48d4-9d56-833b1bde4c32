package dev.journey.Skylandia.automation;

import dev.journey.Skylandia.automation.conditions.Condition;
import dev.journey.Skylandia.automation.actions.Action;
import net.minecraft.util.math.BlockPos;
import java.util.*;

/**
 * Represents an automation flow with conditions and actions
 */
public class AutomationFlow {
    private final List<FlowNode> nodes = new ArrayList<>();
    private FlowNode currentNode = null;
    private boolean isRunning = false;
    private final Map<String, Object> variables = new HashMap<>();

    public AutomationFlow begin() {
        this.currentNode = new FlowNode();
        nodes.add(currentNode);
        return this;
    }

    public AutomationFlow when(Condition condition) {
        if (currentNode == null) begin();
        currentNode.setCondition(condition);
        return this;
    }

    public AutomationFlow then(Action action) {
        if (currentNode == null) begin();
        currentNode.addAction(action);
        return this;
    }

    public AutomationFlow orElse(Action action) {
        if (currentNode == null) throw new IllegalStateException("Must call when() before orElse()");
        currentNode.setFallbackAction(action);
        return this;
    }

    public AutomationFlow next() {
        currentNode = new FlowNode();
        nodes.add(currentNode);
        return this;
    }

    public void setVariable(String name, Object value) {
        variables.put(name, value);
    }

    public Object getVariable(String name) {
        return variables.get(name);
    }

    public void execute() {
        isRunning = true;
        for (FlowNode node : nodes) {
            if (!isRunning) break;
            
            if (node.getCondition() == null || node.getCondition().test(this)) {
                // Execute all actions in sequence
                for (Action action : node.getActions()) {
                    if (!isRunning) break;
                    action.execute(this);
                }
            } else if (node.getFallbackAction() != null) {
                node.getFallbackAction().execute(this);
            }
        }
    }

    public void stop() {
        isRunning = false;
    }

    private static class FlowNode {
        private Condition condition;
        private final List<Action> actions = new ArrayList<>();
        private Action fallbackAction;

        public void setCondition(Condition condition) {
            this.condition = condition;
        }

        public Condition getCondition() {
            return condition;
        }

        public void addAction(Action action) {
            actions.add(action);
        }

        public List<Action> getActions() {
            return actions;
        }

        public void setFallbackAction(Action fallbackAction) {
            this.fallbackAction = fallbackAction;
        }

        public Action getFallbackAction() {
            return fallbackAction;
        }
    }
}