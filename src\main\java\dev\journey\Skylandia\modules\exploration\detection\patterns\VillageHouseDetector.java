package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

public class VillageHouseDetector extends PatternDetector {
    private static final int MIN_HOUSE_SIZE = 3;
    private static final int MAX_HOUSE_SIZE = 12;
    private static final int MIN_HEIGHT = 3;
    private static final int MAX_HEIGHT = 8;

    private static final Block[] VILLAGE_BLOCKS = {
        Blocks.OAK_PLANKS,
        Blocks.SPRUCE_PLANKS,
        Blocks.BIRCH_PLANKS,
        Blocks.ACACIA_PLANKS,
        Blocks.DARK_OAK_PLANKS,
        Blocks.COBBLESTONE,
        Blocks.STONE_BRICKS,
        Blocks.SMOOTH_STONE
    };

    public VillageHouseDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (isVillageBlock(pos)) {
                        HouseInfo info = findHouse(pos);
                        if (info != null && !isAlreadyDetected(results, info.center)) {
                            results.add(createResult(
                                String.format("Village house (size: %dx%dx%d)", info.width, info.height, info.length),
                                info.center,
                                DetectionResult.DetectionType.STRUCTURE,
                                info.confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private boolean isVillageBlock(BlockPos pos) {
        Block block = world.getBlockState(pos).getBlock();
        for (Block villageBlock : VILLAGE_BLOCKS) {
            if (block == villageBlock) return true;
        }
        return false;
    }

    private HouseInfo findHouse(BlockPos start) {
        // Try to find a house structure in each horizontal direction
        for (Direction dir : Direction.Type.HORIZONTAL) {
            HouseInfo info = analyzeHouseStructure(start, dir);
            if (info != null) {
                return info;
            }
        }
        return null;
    }

    private HouseInfo analyzeHouseStructure(BlockPos start, Direction direction) {
        Direction side = direction.rotateYClockwise();
        
        // Find house dimensions
        int width = findDimension(start, side, MAX_HOUSE_SIZE);
        if (width < MIN_HOUSE_SIZE) return null;

        int length = findDimension(start, direction, MAX_HOUSE_SIZE);
        if (length < MIN_HOUSE_SIZE) return null;

        int height = findHeight(start, width, length, direction, side);
        if (height < MIN_HEIGHT || height > MAX_HEIGHT) return null;

        // Calculate center position
        BlockPos center = start.offset(direction, length / 2).offset(side, width / 2).up(height / 2);
        float confidence = calculateConfidence(width, length, height);

        return new HouseInfo(center, width, length, height, confidence);
    }

    private int findDimension(BlockPos start, Direction direction, int maxSize) {
        int size = 1;
        BlockPos current = start;

        while (size < maxSize) {
            current = current.offset(direction);
            if (!isVillageBlock(current)) break;
            size++;
        }

        return size;
    }

    private int findHeight(BlockPos start, int width, int length, Direction lengthDir, Direction widthDir) {
        int maxFoundHeight = 0;

        // Check height at each corner and center
        BlockPos[] checkPositions = {
            start,
            start.offset(lengthDir, length - 1),
            start.offset(widthDir, width - 1),
            start.offset(lengthDir, length - 1).offset(widthDir, width - 1),
            start.offset(lengthDir, length / 2).offset(widthDir, width / 2)
        };

        for (BlockPos pos : checkPositions) {
            int height = 0;
            BlockPos current = pos;

            while (height < MAX_HEIGHT) {
                current = current.up();
                if (!isVillageBlock(current)) break;
                height++;
            }

            maxFoundHeight = Math.max(maxFoundHeight, height);
        }

        return maxFoundHeight;
    }

    private float calculateConfidence(int width, int length, int height) {
        float confidence = 0.5f; // Base confidence

        // Larger structures are more likely to be houses
        if (width >= 4 && length >= 4) {
            confidence += 0.1f;
        }

        // Reasonable height increases confidence
        if (height >= 3 && height <= 6) {
            confidence += 0.2f;
        }

        // Square or near-square shapes are common in villages
        float ratio = Math.max(width, length) / (float)Math.min(width, length);
        if (ratio <= 1.5f) {
            confidence += 0.2f;
        }

        return Math.min(confidence, 1.0f);
    }

    private boolean isAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (result.getPosition().getSquaredDistance(pos) < 64) { // Within 8 blocks
                return true;
            }
        }
        return false;
    }

    private static class HouseInfo {
        final BlockPos center;
        final int width;
        final int length;
        final int height;
        final float confidence;

        HouseInfo(BlockPos center, int width, int length, int height, float confidence) {
            this.center = center;
            this.width = width;
            this.length = length;
            this.height = height;
            this.confidence = confidence;
        }
    }
}
