package dev.journey.Skylandia.modules.automation;

import dev.journey.Skylandia.Skylandia;
import dev.journey.Skylandia.automation.AutomationFlow;
import dev.journey.Skylandia.automation.actions.Action;
import dev.journey.Skylandia.automation.actions.BasicActions;
import dev.journey.Skylandia.automation.conditions.ShulkerConditions;
import dev.journey.Skylandia.automation.recording.ActionRecorder;
import dev.journey.Skylandia.automation.recording.ActionRecorder.RecordedAction;
import dev.journey.Skylandia.automation.recording.RecordedActionConverter;
import meteordevelopment.meteorclient.events.meteor.KeyEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.utils.misc.input.KeyAction;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.DyeColor;
import net.minecraft.util.math.BlockPos;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AutomationModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgRecording = settings.createGroup("Recording");
    private final SettingGroup sgFlows = settings.createGroup("Flows");

    private final Setting<Boolean> active = sgGeneral.add(new BoolSetting.Builder()
        .name("active")
        .description("Whether the automation is currently running")
        .defaultValue(false)
        .build()
    );

    // Example flow settings
    private final Setting<Boolean> enableShulkerHunter = sgFlows.add(new BoolSetting.Builder()
        .name("shulker-hunter")
        .description("Automatically finds and collects shulker boxes")
        .defaultValue(false)
        .build()
    );

    // Recording settings
    private final Setting<Boolean> isRecording = sgRecording.add(new BoolSetting.Builder()
        .name("recording")
        .description("Whether action recording is currently active")
        .defaultValue(false)
        .onChanged(this::onRecordingToggled)
        .build()
    );

    private final Setting<String> recordingHotkey = sgRecording.add(new StringSetting.Builder()
        .name("stop-hotkey")
        .description("Hotkey to stop recording (e.g. 'r')")
        .defaultValue("r")
        .build()
    );

    private final Setting<String> recordingName = sgRecording.add(new StringSetting.Builder()
        .name("recording-name")
        .description("Name for the recorded action sequence")
        .defaultValue("recorded-sequence")
        .build()
    );

    private final Setting<DyeColor> targetShulkerColor = sgFlows.add(new EnumSetting.Builder<DyeColor>()
        .name("shulker-color")
        .description("The color of shulker boxes to hunt for")
        .defaultValue(DyeColor.PURPLE)
        .visible(enableShulkerHunter::get)
        .build()
    );

    private final Setting<Integer> searchRadius = sgFlows.add(new IntSetting.Builder()
        .name("search-radius")
        .description("The radius to search for shulker boxes")
        .defaultValue(20)
        .min(5)
        .max(10000)
        .visible(enableShulkerHunter::get)
        .build()
    );

    private final Setting<BlockPos> fallbackPosition = sgFlows.add(new BlockPosSetting.Builder()
        .name("fallback-position")
        .description("Where to go if no shulkers are found always set this to your base of some sort")
        .defaultValue(new BlockPos(0, 64, 0))
        .visible(enableShulkerHunter::get)
        .build()
    );

    private final List<AutomationFlow> activeFlows = new ArrayList<>();
    private final ActionRecorder actionRecorder = new ActionRecorder();
    private final Map<String, List<Action>> savedRecordings = new HashMap<>();

    public AutomationModule() {
        super(Skylandia.Automation, "flow-automation", "Creates complex automation flows with conditions and actions");
    }

    public AutomationModule(Category category, String name, String description) {
        super(category, name, description);
    }

    @Override
    public void onActivate() {
        setupFlows();
        mc.worldRenderer.reload();
    }

    @Override
    public void onDeactivate() {
        activeFlows.clear();
        if (actionRecorder.isRecording()) {
            actionRecorder.stopRecording();
            isRecording.set(false);
        }
    }

    private void onRecordingToggled(boolean recording) {
        if (recording) {
            info("Starting action recording...");
            actionRecorder.startRecording();
        } else {
            info("Stopping action recording...");
            List<RecordedAction> actions = actionRecorder.stopRecording();
            if (!actions.isEmpty()) {
                List<Action> convertedActions = RecordedActionConverter.convertToActions(actions);
                savedRecordings.put(recordingName.get(), convertedActions);
                info("Recorded " + actions.size() + " actions as '" + recordingName.get() + "'");
            }
        }
    }

    /**
     * Creates a new recording with the specified name and actions.
     * @param name Name of the recording
     * @param actions List of actions to add to the recording
     */
    public void addRecording(String name, List<Action> actions) {
        savedRecordings.put(name, actions);
        info("Added new recording: " + name + " with " + actions.size() + " actions");
        setupFlows(); // Refresh active flows
    }

    /**
     * Removes a recording by name.
     * @param name Name of the recording to remove
     */
    public void removeRecording(String name) {
        if (savedRecordings.remove(name) != null) {
            info("Removed recording: " + name);
            setupFlows(); // Refresh active flows
        }
    }

    /**
     * Lists all saved recordings.
     * @return List of recording names
     */
    public List<String> getRecordings() {
        return new ArrayList<>(savedRecordings.keySet());
    }

    private AutomationFlow createRecordedFlow(String name, List<Action> actions) {
        AutomationFlow flow = new AutomationFlow().begin();
        actions.forEach(flow::then);
        return flow;
    }

    private final Setting<Boolean> enableAutoSearch = sgFlows.add(new BoolSetting.Builder()
        .name("auto-search")
        .description("Automatically search for and collect targeted blocks")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> searchPauseDelay = sgFlows.add(new IntSetting.Builder()
        .name("search-pause")
        .description("Delay in ticks between search attempts")
        .defaultValue(100)
        .min(20)
        .max(1000)
        .visible(enableAutoSearch::get)
        .build()
    );

    private void setupFlows() {
        info("Setting up automation flows...");
        activeFlows.clear();

        // Add recorded flows
        for (Map.Entry<String, List<Action>> recording : savedRecordings.entrySet()) {
            info("Adding recorded flow: " + recording.getKey());
            AutomationFlow recordedFlow = createRecordedFlow(recording.getKey(), recording.getValue());
            activeFlows.add(recordedFlow);
        }

        info("Configuring Shulker Hunter Flow...");
        if (enableShulkerHunter.get()) {
            AutomationFlow shulkerHunterFlow = new AutomationFlow()
                .begin()
                .when(ShulkerConditions.shulkerNearby(searchRadius.get())
                      .and(ShulkerConditions.shulkerOfColor(targetShulkerColor.get())))
                .then(BasicActions.moveToLastShulker()
                      .then(BasicActions.interact())
                      .then(BasicActions.wait(10))
                      .then(BasicActions.breakBlock()))
                .orElse(enableAutoSearch.get()
                       ? BasicActions.findShulker()
                            .then(BasicActions.wait(searchPauseDelay.get()))
                            .then(BasicActions.followPath())
                       : BasicActions.moveTo(fallbackPosition.get()));

            activeFlows.add(shulkerHunterFlow);
            info("Shulker Hunter Flow configured with settings:");
            info(" - Search Radius: " + searchRadius.get());
            info(" - Target Color: " + targetShulkerColor.get());
            info(" - Auto Search: " + enableAutoSearch.get());
        }
    }

    @EventHandler
    private void onKey(KeyEvent event) {
        // Super detailed debug logging with reflection info
        System.out.println("\n[Debug] ==========================================");
        System.out.println("[Debug] KeyAction Class Analysis");
        System.out.println("[Debug] ==========================================");
        System.out.println("=== MeteorClient Analysis ===");
        Package pkg = KeyAction.class.getPackage();
        System.out.println("KeyAction package: " + pkg.getName());
        System.out.println("Implementation title: " + pkg.getImplementationTitle());
        System.out.println("Implementation version: " + pkg.getImplementationVersion());
        System.out.println("Specification version: " + pkg.getSpecificationVersion());

        System.out.println("\n=== KeyAction Enum Details ===");
        System.out.println("Enum classloader: " + KeyAction.class.getClassLoader());
        System.out.println("Event action classloader: " + event.action.getClass().getClassLoader());
        for (KeyAction val : KeyAction.values()) {
            System.out.println(String.format("Value: %-10s | Ordinal: %-2d | Raw: %s",
                val.name(), val.ordinal(), val));
        }
        
        System.out.println("\n=== Enum Constants ===");
        for (KeyAction action : KeyAction.values()) {
            System.out.println(String.format("Constant: %-15s | Name: %-15s | Ordinal: %d | ToString: %s",
                action, action.name(), action.ordinal(), action.toString()));
        }
        System.out.println("[Debug] ==========================================");
        System.out.println("\n[Debug] Current Key Event Details:");
        System.out.println(" - Key char: " + String.valueOf((char)event.key));
        System.out.println(" - Key code: " + event.key);
        System.out.println(" - Current hotkey: " + recordingHotkey.get());
        System.out.println(" - Action class: " + event.action.getClass().getName());
        System.out.println(" - Action name: " + event.action.name());
        System.out.println(" - Action toString: " + event.action);
        System.out.println(" - Action ordinal: " + event.action.ordinal());
        System.out.println(" - Is recording: " + actionRecorder.isRecording());

        // Check for recording stop hotkey using proper KeyAction constant
        // Temporarily switch to ordinal comparison
        if (actionRecorder.isRecording() &&
            event.action.ordinal() == 0 && // Press (ordinal 0)
            String.valueOf((char)event.key).equalsIgnoreCase(recordingHotkey.get())) {
            isRecording.set(false);
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (!active.get()) {
            return;
        }
        if (mc.player == null || mc.world == null) {
            warning("Player or world not available");
            return;
        }

        // Log state before executing flows
        info("Executing automation flows...");
        info("Active flows: " + activeFlows.size());
        info("Player position: " + mc.player.getBlockPos());
        try {
            for (AutomationFlow currentFlow : activeFlows) {
                try {
                    info("Starting flow execution...");
                    currentFlow.execute();
                    info("Flow execution completed");
                } catch (Exception execError) {
                    error("Flow execution failed: " + execError.getMessage());
                    continue;
                }
            }
        } catch (Exception moduleError) {
            error("Module error: " + moduleError.getMessage());
            active.set(false);
        }
    }
}