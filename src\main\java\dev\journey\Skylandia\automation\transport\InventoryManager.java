package dev.journey.Skylandia.automation.transport;

import dev.journey.Skylandia.modules.automation.ShulkerTransportModule;
import dev.journey.Skylandia.automation.AutomationFlow;
import dev.journey.Skylandia.automation.actions.Action;
import net.minecraft.block.ShulkerBoxBlock;
import net.minecraft.client.MinecraftClient;
import net.minecraft.inventory.Inventory;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.screen.GenericContainerScreenHandler;
import net.minecraft.screen.ScreenHandler;
import net.minecraft.util.math.BlockPos;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Manages inventory operations for shulker box transport
 */
public class InventoryManager {
    private static final int TRANSFER_TIMEOUT = 5000; // 5 seconds
    private static final int CONFIRMATION_TIMEOUT = 30000; // 30 seconds

    private final ShulkerTransportModule module;
    private final TransportConfig config;
    private final MinecraftClient mc;

    private CompletableFuture<Boolean> pendingConfirmation;
    private Map<Integer, ItemStack> pendingTransfers;
    private long lastTransferTime;
    private long lastActionTime;
    private boolean awaitingConfirmation;
    private boolean collecting;
    private boolean pearlThrown;
    private boolean dropComplete;
    private boolean returnComplete;
    private boolean teleportConfirmed;
    private int pearlSlot = 0;

    public InventoryManager(ShulkerTransportModule module, TransportConfig config) {
        this.module = module;
        this.config = config;
        this.mc = MinecraftClient.getInstance();
        this.pendingTransfers = new HashMap<>();
    }

    /**
     * Update manager state
     */
    public void update() {
        long currentTime = System.currentTimeMillis();
        
        // Update confirmation timeout
        if (pendingConfirmation != null && !pendingConfirmation.isDone()) {
            if (currentTime - lastTransferTime > CONFIRMATION_TIMEOUT) {
                pendingConfirmation.complete(false);
                awaitingConfirmation = false;
            }
        }

        // Reset states if too much time has passed
        if (currentTime - lastActionTime > 5000) {
            collecting = false;
            pearlThrown = false;
            dropComplete = false;
            returnComplete = false;
            teleportConfirmed = false;
        }
    }

    /**
     * Validate inventories before transport
     */
    public TransportAction validateInventories() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("inventory validation")) {
                    throw TransportException.inventory("Invalid state for inventory validation", 
                        module.getState());
                }

                // Check player inventory space
                if (!hasRequiredSpace()) {
                    throw TransportException.inventory("Insufficient inventory space", 
                        module.getState());
                }

                // Check shulker box contents
                if (!validateShulkerContents()) {
                    throw TransportException.inventory("Invalid shulker box contents", 
                        module.getState());
                }

                lastTransferTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                pendingTransfers.clear();
            }
        };
    }

    /**
     * Execute item transfer
     */
    public TransportAction executeTransfer() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("item transfer")) {
                    throw TransportException.inventory("Invalid state for item transfer", 
                        module.getState());
                }

                if (config.requireConfirmation.get()) {
                    requestTransferConfirmation();
                } else {
                    performTransfer();
                }

                lastTransferTime = System.currentTimeMillis();
            }

            @Override
            public boolean requiresConfirmation() {
                return config.requireConfirmation.get();
            }

            @Override
            public void rollback(AutomationFlow flow) {
                cancelTransfer();
            }
        };
    }

    /**
     * Wait for transfer completion
     */
    public TransportAction waitForTransfer() {
        return new TransportAction() {
            @Override
            public void execute(AutomationFlow flow) {
                if (!validateState("transfer wait")) {
                    throw TransportException.inventory("Invalid state for transfer wait", 
                        module.getState());
                }

                try {
                    if (pendingConfirmation != null) {
                        if (!pendingConfirmation.get(TRANSFER_TIMEOUT, TimeUnit.MILLISECONDS)) {
                            throw TransportException.inventory("Transfer confirmation timeout", 
                                module.getState());
                        }
                    }
                } catch (Exception e) {
                    throw TransportException.inventory("Transfer wait failed: " + e.getMessage(), 
                        module.getState());
                }
            }

            @Override
            public boolean requiresConfirmation() {
                return false;
            }

            @Override
            public void rollback(AutomationFlow flow) {
                cancelTransfer();
            }
        };
    }

    private boolean validateState(String operation) {
        if (mc.player == null || mc.world == null) {
            module.error("Player or world not available for " + operation);
            return false;
        }

        if (System.currentTimeMillis() - lastTransferTime < 1000) {
            module.warning("Transfer attempted too quickly");
            return false;
        }

        return true;
    }

    private boolean hasRequiredSpace() {
        if (mc.player == null) return false;

        // Count empty slots
        int emptySlots = 0;
        for (ItemStack stack : mc.player.getInventory().main) {
            if (stack.isEmpty()) emptySlots++;
        }

        return emptySlots >= config.maxShulkerBoxes.get();
    }

    private boolean validateShulkerContents() {
        ScreenHandler handler = mc.player.currentScreenHandler;
        if (!(handler instanceof GenericContainerScreenHandler)) return false;

        GenericContainerScreenHandler container = (GenericContainerScreenHandler) handler;
        int slots = container.getRows() * 9;

        // Verify this is a shulker box (27 slots)
        if (slots != 27) return false;

        // Check for blacklisted items
        for (int i = 0; i < 27; i++) {
            ItemStack stack = container.getSlot(i).getStack();
            if (!stack.isEmpty() && config.isItemBlacklisted(stack.getItem())) {
                return false;
            }
        }

        return true;
    }

    private void requestTransferConfirmation() {
        if (awaitingConfirmation) {
            throw TransportException.inventory("Already awaiting confirmation", 
                module.getState());
        }

        pendingConfirmation = new CompletableFuture<>();
        awaitingConfirmation = true;

        // Store current inventory state
        if (mc.player != null) {
            pendingTransfers.clear();
            for (int i = 0; i < mc.player.getInventory().main.size(); i++) {
                ItemStack stack = mc.player.getInventory().main.get(i);
                if (!stack.isEmpty()) {
                    pendingTransfers.put(i, stack.copy());
                }
            }
        }
    }

    private void performTransfer() {
        ScreenHandler handler = mc.player.currentScreenHandler;
        if (!(handler instanceof GenericContainerScreenHandler)) {
            throw TransportException.inventory("Container not open", module.getState());
        }

        GenericContainerScreenHandler container = (GenericContainerScreenHandler) handler;
        if (container.getRows() * 9 != 27) {
            throw TransportException.inventory("Not a shulker box", module.getState());
        }

        try {
            // Quick transfer items between inventories
            for (int i = 0; i < 27; i++) {
                ItemStack stack = container.getSlot(i).getStack();
                if (!stack.isEmpty() && !config.isItemBlacklisted(stack.getItem())) {
                    // Click slot with quick transfer (SHIFT + click)
                    mc.interactionManager.clickSlot(
                        container.syncId,
                        i,
                        0,
                        net.minecraft.screen.slot.SlotActionType.QUICK_MOVE,
                        mc.player
                    );
                }
            }
            return;
        } catch (Exception e) {
            module.error("Transfer failed: " + e.getMessage());
            throw e;
        }
    }

    private void cancelTransfer() {
        if (pendingConfirmation != null) {
            pendingConfirmation.complete(false);
        }
        
        // Restore previous inventory state
        if (mc.player != null && !pendingTransfers.isEmpty()) {
            // TODO: Implement inventory restoration
        }

        pendingTransfers.clear();
        awaitingConfirmation = false;
    }

    /**
     * Get whether items are being collected
     */
    public boolean isCollecting() {
        return collecting;
    }

    /**
     * Set collecting state
     */
    public void setCollecting(boolean collecting) {
        this.collecting = collecting;
        this.lastActionTime = System.currentTimeMillis();
    }

    /**
     * Get whether teleport is confirmed
     */
    public boolean isTeleportConfirmed() {
        return teleportConfirmed;
    }

    /**
     * Set teleport confirmed state
     */
    public void setTeleportConfirmed(boolean confirmed) {
        this.teleportConfirmed = confirmed;
        this.lastActionTime = System.currentTimeMillis();
    }

    /**
     * Get whether pearl has been thrown
     */
    public boolean isPearlThrown() {
        return pearlThrown;
    }

    /**
     * Set pearl thrown state and slot
     */
    public void setPearlThrown(boolean thrown, int slot) {
        this.pearlThrown = thrown;
        this.pearlSlot = slot;
        this.lastActionTime = System.currentTimeMillis();
    }

    /**
     * Get whether item dropping is complete
     */
    public boolean isDropComplete() {
        return dropComplete;
    }

    /**
     * Set drop complete state
     */
    public void setDropComplete(boolean complete) {
        this.dropComplete = complete;
        this.lastActionTime = System.currentTimeMillis();
    }

    /**
     * Get whether return is complete
     */
    public boolean isReturnComplete() {
        return returnComplete;
    }

    /**
     * Set return complete state
     */
    public void setReturnComplete(boolean complete) {
        this.returnComplete = complete;
        this.lastActionTime = System.currentTimeMillis();
    }

    /**
     * Clear pearl from slot
     */
    public void clearPearlSlot() {
        if (mc.player != null && pearlSlot >= 0 && pearlSlot < 9) {
            mc.player.getInventory().selectedSlot = pearlSlot;
            mc.player.dropSelectedItem(true);
        }
    }

    /**
     * Process transfer confirmation response
     */
    public void handleConfirmation(boolean confirmed) {
        if (!awaitingConfirmation) return;

        if (confirmed) {
            performTransfer();
        } else {
            cancelTransfer();
        }

        if (pendingConfirmation != null) {
            pendingConfirmation.complete(confirmed);
        }
        awaitingConfirmation = false;
    }

    /**
     * Validate that the stash exists and is accessible
     * @return true if stash exists and is valid
     */
    public boolean validateStashExists() {
        if (mc.player == null || mc.world == null) return false;

        // Check if a container is open
        if (!(mc.player.currentScreenHandler instanceof GenericContainerScreenHandler)) {
            return false;
        }

        // Verify it's a shulker box (27 slots)
        GenericContainerScreenHandler container = (GenericContainerScreenHandler) mc.player.currentScreenHandler;
        if (container.getRows() * 9 != 27) {
            return false;
        }

        // Check if container has any valid items
        boolean hasItems = false;
        for (int i = 0; i < 27; i++) {
            if (!container.getSlot(i).getStack().isEmpty()) {
                hasItems = true;
                break;
            }
        }

        return hasItems;
    }
}