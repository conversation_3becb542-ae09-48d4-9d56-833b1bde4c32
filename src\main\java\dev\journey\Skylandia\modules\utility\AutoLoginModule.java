package dev.journey.Skylandia.modules.utility;

import dev.journey.Skylandia.Skylandia;
import meteordevelopment.meteorclient.events.game.GameJoinedEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.render.MeteorToast;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.client.network.ServerInfo;
import net.minecraft.item.Item;
import net.minecraft.item.Items;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import org.lwjgl.glfw.GLFW;

import java.text.SimpleDateFormat;
import java.util.*;

public class AutoLoginModule extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgTyping = settings.createGroup("Typing");
    private final SettingGroup sgToasts = settings.createGroup("Toast Icons");

    private final Setting<String> serverAddress = sgGeneral.add(new StringSetting.Builder()
        .name("server-address")
        .description("The server address to trigger auto-login on")
        .defaultValue("")
        .build()
    );

    private final Setting<String> accountName = sgGeneral.add(new StringSetting.Builder()
        .name("account-name")
        .description("Your Minecraft account name (optional)")
        .defaultValue("")
        .build()
    );

    private final Setting<String> loginCommand = sgGeneral.add(new StringSetting.Builder()
        .name("login-string")
        .description("The login message to send")
        .defaultValue("/login")
        .build()
    );

    private final Setting<Boolean> manualEnter = sgTyping.add(new BoolSetting.Builder()
        .name("manual-enter")
        .description("Wait for manual enter press after typing")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> loginDelay = sgTyping.add(new IntSetting.Builder()
        .name("login-delay")
        .description("Delay before starting login sequence")
        .defaultValue(20)
        .min(1)
        .sliderMax(100)
        .build()
    );

    private final Setting<Integer> charDelay = sgTyping.add(new IntSetting.Builder()
        .name("char-delay")
        .description("Delay between typing each character")
        .defaultValue(2)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Integer> enterDelay = sgTyping.add(new IntSetting.Builder()
        .name("enter-delay")
        .description("Delay before pressing enter")
        .defaultValue(10)
        .min(1)
        .sliderMax(50)
        .visible(() -> !manualEnter.get())
        .build()
    );

    private final Setting<ToastIcon> activateIcon = sgToasts.add(new EnumSetting.Builder<ToastIcon>()
        .name("activate-icon")
        .description("Icon for activation toast")
        .defaultValue(ToastIcon.CommandBlock)
        .build()
    );

    private final Setting<ToastIcon> matchIcon = sgToasts.add(new EnumSetting.Builder<ToastIcon>()
        .name("match-icon")
        .description("Icon for server match")
        .defaultValue(ToastIcon.Compass)
        .build()
    );

    private final Setting<ToastIcon> chatIcon = sgToasts.add(new EnumSetting.Builder<ToastIcon>()
        .name("chat-icon")
        .description("Icon for chat open")
        .defaultValue(ToastIcon.Book)
        .build()
    );

    private final Setting<ToastIcon> doneIcon = sgToasts.add(new EnumSetting.Builder<ToastIcon>()
        .name("done-icon")
        .description("Icon for login complete")
        .defaultValue(ToastIcon.GreenDye)
        .build()
    );

    private enum ToastIcon {
        CommandBlock(Items.COMMAND_BLOCK),
        Compass(Items.COMPASS),
        Book(Items.BOOK),
        GreenDye(Items.LIME_DYE),
        Barrier(Items.BARRIER);

        final Item item;
        ToastIcon(Item item) {
            this.item = item;
        }
    }

    private enum State {
        WAITING,
        TYPING,
        ENTER_DOWN,
        ENTER_UP,
        DONE
    }

    // State variables
    private State state = State.WAITING;
    private int timer;
    private String currentText = "";
    private final List<String> log = new ArrayList<>();
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");

    public AutoLoginModule() {
        super(Skylandia.Utility, "auto-login", "Automatically sends login command when joining specified servers");
    }

    private void addLog(String message) {
        String time = timeFormat.format(new Date());
        log.add(0, String.format("[%s] %s", time, message));
    }

    private void sendCommand(String cmd) {
        if (mc.player != null) {
            if (cmd.startsWith(".") || cmd.startsWith("#")) {
                mc.player.networkHandler.sendChatCommand(cmd.substring(1));
            } else {
                mc.player.networkHandler.sendChatMessage(cmd);
            }
        }
    }

    @Override
    public String getInfoString() {
        return !log.isEmpty() ? log.get(0) : null;
    }


    @Override
    public void onActivate() {
        if (serverAddress.get().isEmpty() || loginCommand.get().isEmpty()) {
            mc.getToastManager().add(new MeteorToast(ToastIcon.Barrier.item, title, "Configure server and login string!"));
            toggle();
            return;
        }
        state = State.WAITING;
        timer = 0;
        currentText = "";
        addLog("Activated - Waiting for server");
        mc.getToastManager().add(new MeteorToast(activateIcon.get().item, title, "Auto-login activated"));
    }

    @EventHandler
    private void onGameJoined(GameJoinedEvent event) {
        ServerInfo server = mc.getCurrentServerEntry();
        if (server == null || !server.address.equalsIgnoreCase(serverAddress.get())) {
            return;
        }
        if (!accountName.get().isEmpty() && !mc.player.getName().getString().equals(accountName.get())) return;

        addLog("Server matched - Starting login sequence");
        mc.getToastManager().add(new MeteorToast(matchIcon.get().item, title, "Server matched"));
        state = State.WAITING;
        timer = 0;
        currentText = "";
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        if (mc.player == null) return;

        // Main state machine
        switch (state) {
            case WAITING -> {
                if (++timer >= loginDelay.get()) {
                    state = State.TYPING;
                    timer = 0;
                    addLog("Opening chat");
                    mc.getToastManager().add(new MeteorToast(chatIcon.get().item, title, "Starting to type..."));
                    if (!(mc.currentScreen instanceof ChatScreen)) {
                        mc.setScreen(new ChatScreen(""));
                    }
                }
            }
            case TYPING -> {
                if (timer >= charDelay.get()) {
                    timer = 0;
                    if (currentText.length() < loginCommand.get().length()) {
                        currentText = loginCommand.get().substring(0, currentText.length() + 1);
                        if (mc.currentScreen instanceof ChatScreen) {
                            mc.setScreen(new ChatScreen(currentText));
                        }
                    } else {
                        state = manualEnter.get() ? State.DONE : State.ENTER_DOWN;
                        timer = enterDelay.get();
                        mc.getToastManager().add(new MeteorToast(chatIcon.get().item, title, 
                            manualEnter.get() ? "Waiting for Enter..." : "Pressing Enter..."));
                    }
                }
                timer++;
            }
            case ENTER_DOWN -> {
                if (timer <= 0) {
                    if (mc.currentScreen instanceof ChatScreen chat) {
                        addLog("Pressing Enter...");
                        mc.keyboard.onKey(mc.getWindow().getHandle(), GLFW.GLFW_KEY_ENTER, 0, 1, 0);
                        chat.keyPressed(GLFW.GLFW_KEY_ENTER, 0, 0);
                        state = State.ENTER_UP;
                        timer = 2;
                    }
                } else {
                    timer--;
                }
            }
            case ENTER_UP -> {
                if (timer <= 0) {
                    mc.keyboard.onKey(mc.getWindow().getHandle(), GLFW.GLFW_KEY_ENTER, 0, 0, 0);
                    addLog("Login command sent");
                    mc.getToastManager().add(new MeteorToast(doneIcon.get().item, title, "Login complete"));
                    state = State.DONE;
                } else {
                    timer--;
                }
            }
        }
    }

    @Override
    public void onDeactivate() {
        state = State.WAITING;
        timer = 0;
        currentText = "";
        if (mc.currentScreen instanceof ChatScreen) {
            mc.setScreen(null);
        }
        addLog("Module deactivated");
        mc.getToastManager().add(new MeteorToast(activateIcon.get().item, title, "Auto-login deactivated"));
    }
}