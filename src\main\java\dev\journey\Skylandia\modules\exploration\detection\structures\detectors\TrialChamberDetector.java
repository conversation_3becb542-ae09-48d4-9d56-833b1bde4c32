package dev.journey.Skylandia.modules.exploration.detection.structures.detectors;

import dev.journey.Skylandia.modules.exploration.detection.structures.StructureDetector;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import net.minecraft.block.Blocks;
import net.minecraft.block.BlockState;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;

import java.util.ArrayList;
import java.util.List;

public class TrialChamberDetector extends StructureDetector {
    private static final BlockState[][][] ENTRANCE_PATTERN = {
        {
            {Blocks.COPPER_BLOCK.getDefaultState(), Blocks.COPPER_BLOCK.getDefaultState(), Blocks.COPPER_BLOCK.getDefaultState()},
            {Blocks.COPPER_BLOCK.getDefaultState(), null, Blocks.COPPER_BLOCK.getDefaultState()},
            {Blocks.COPPER_BLOCK.getDefaultState(), Blocks.COPPER_BLOCK.getDefaultState(), Blocks.COPPER_BLOCK.getDefaultState()}
        },
        {
            {Blocks.TUFF.getDefaultState(), Blocks.TUFF.getDefaultState(), Blocks.TUFF.getDefaultState()},
            {Blocks.TUFF.getDefaultState(), null, Blocks.TUFF.getDefaultState()},
            {Blocks.TUFF.getDefaultState(), Blocks.TUFF.getDefaultState(), Blocks.TUFF.getDefaultState()}
        }
    };

    public TrialChamberDetector(World world, double confidenceThreshold, int maxSearchRadius) {
        super(world, confidenceThreshold, maxSearchRadius);
    }

    @Override
    public StructureType getStructureType() {
        return StructureType.TRIAL_CHAMBER;
    }

    @Override
    public List<StructureMatch> detectInChunk(Chunk chunk) {
        List<StructureMatch> matches = new ArrayList<>();
        int chunkX = chunk.getPos().x * 16;
        int chunkZ = chunk.getPos().z * 16;

        // Trial chambers typically appear underground
        for (int x = chunkX; x < chunkX + 16; x++) {
            for (int z = chunkZ; z < chunkZ + 16; z++) {
                for (int y = 0; y < world.getHeight(); y++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    
                   // Check for copper or tuff blocks first
                   BlockState state = world.getBlockState(pos);
                   if (isTrialChamberBlock(state)) {
                       info("Found potential trial chamber block at %s", pos.toShortString());
                       
                       // Look for trial chamber entrance pattern
                       double confidence = calculatePatternConfidence(pos, ENTRANCE_PATTERN);
                       info("Pattern confidence at %s: %.2f%%", pos.toShortString(), confidence * 100);
                       
                       if (confidence >= confidenceThreshold) {
                           Box bounds = calculateBounds(pos, 48, 32, 48); // Trial chambers are typically large
                           double ratio = calculateCopperTuffRatio(pos, bounds);
                           String details = String.format("Trial Chamber entrance found at Y=%d (Copper/Tuff ratio: %.2f)",
                               y, ratio);
                           matches.add(new StructureMatch(
                               getStructureType(),
                               pos,
                               confidence,
                               bounds,
                               details
                           ));
                       }
                   }
                }
            }
        }

        return matches;
    }

    public boolean isTrialChamberBlock(BlockState state) {
        return state.isOf(Blocks.COPPER_BLOCK) ||
               state.isOf(Blocks.EXPOSED_COPPER) ||
               state.isOf(Blocks.WEATHERED_COPPER) ||
               state.isOf(Blocks.OXIDIZED_COPPER) ||
               state.isOf(Blocks.TUFF) ||
               state.isOf(Blocks.SUSPICIOUS_GRAVEL);
    }

    private double calculateCopperTuffRatio(BlockPos center, Box bounds) {
        int copperCount = 0;
        int tuffCount = 0;

        for (int x = (int) bounds.minX; x <= bounds.maxX; x++) {
            for (int y = (int) bounds.minY; y <= bounds.maxY; y++) {
                for (int z = (int) bounds.minZ; z <= bounds.maxZ; z++) {
                    BlockState state = world.getBlockState(new BlockPos(x, y, z));
                    if (state.isOf(Blocks.COPPER_BLOCK) ||
                        state.isOf(Blocks.EXPOSED_COPPER) ||
                        state.isOf(Blocks.WEATHERED_COPPER) ||
                        state.isOf(Blocks.OXIDIZED_COPPER)) {
                        copperCount++;
                    } else if (state.isOf(Blocks.TUFF)) {
                        tuffCount++;
                    }
                }
            }
        }

        return tuffCount > 0 ? (double) copperCount / tuffCount : 0.0;
    }

    private void info(String message, Object... args) {
        System.out.printf("[TrialChamberDetector] " + message + "%n", args);
    }
}