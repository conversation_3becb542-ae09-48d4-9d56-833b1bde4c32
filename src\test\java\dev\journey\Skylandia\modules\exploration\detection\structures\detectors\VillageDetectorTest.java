package dev.journey.Skylandia.modules.exploration.detection.structures.detectors;

import dev.journey.Skylandia.modules.exploration.detection.structures.StructureMatch;
import dev.journey.Skylandia.modules.exploration.detection.structures.StructureType;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.DoorBlock;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class VillageDetectorTest {

    @Mock
    private World world;

    @Mock
    private Chunk chunk;

    @Mock
    private DoorBlock doorBlock;

    private VillageDetector detector;
    private static final double CONFIDENCE_THRESHOLD = 0.8;
    private static final int MAX_SEARCH_RADIUS = 48;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        detector = new VillageDetector(world, CONFIDENCE_THRESHOLD, MAX_SEARCH_RADIUS);
        
        // Setup default chunk position and world height
        ChunkPos chunkPos = new ChunkPos(0, 0);
        when(chunk.getPos()).thenReturn(chunkPos);
        when(world.getTopY()).thenReturn(320);
        when(world.getBottomY()).thenReturn(0);
    }

    @Test
    void getStructureType_ReturnsVillage() {
        assertEquals(StructureType.VILLAGE, detector.getStructureType());
    }

    @Test
    void detectInChunk_CompleteVillage_ReturnsHighConfidenceMatch() {
        BlockPos center = new BlockPos(7, 64, 7);
        mockVillagePattern(center, 20, 100, 3); // Ideal numbers from implementation
        mockSurfaceLevel(center);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertFalse(matches.isEmpty());
        StructureMatch match = matches.get(0);
        assertTrue(match.confidence() > 0.9);
        assertEquals(center, match.center());
        assertEquals(StructureType.VILLAGE, match.type());
    }

    @Test
    void detectInChunk_PartialVillage_ReturnsLowerConfidenceMatch() {
        BlockPos center = new BlockPos(7, 64, 7);
        mockVillagePattern(center, 10, 50, 2); // Half the ideal numbers
        mockSurfaceLevel(center);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertFalse(matches.isEmpty());
        StructureMatch match = matches.get(0);
        assertTrue(match.confidence() >= CONFIDENCE_THRESHOLD);
        assertTrue(match.confidence() < 0.9);
    }

    @Test
    void detectInChunk_NoVillageStructures_ReturnsEmptyList() {
        BlockPos center = new BlockPos(7, 64, 7);
        mockVillagePattern(center, 0, 0, 0); // No village structures
        mockSurfaceLevel(center);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertTrue(matches.isEmpty());
    }

    @Test
    void detectInChunk_OnlyPaths_ReturnsLowConfidence() {
        BlockPos center = new BlockPos(7, 64, 7);
        mockVillagePattern(center, 20, 0, 0); // Only paths, no buildings or doors
        mockSurfaceLevel(center);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertTrue(matches.isEmpty()); // Should be below confidence threshold
    }

    @Test
    void detectInChunk_OnlyBuildings_ReturnsLowConfidence() {
        BlockPos center = new BlockPos(7, 64, 7);
        mockVillagePattern(center, 0, 100, 0); // Only buildings, no paths or doors
        mockSurfaceLevel(center);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertTrue(matches.isEmpty()); // Should be below confidence threshold
    }

    @Test
    void detectInChunk_MultipleVillageSections_ReturnsAllMatches() {
        BlockPos center1 = new BlockPos(7, 64, 7);
        BlockPos center2 = new BlockPos(7, 64, 15);
        
        mockVillagePattern(center1, 20, 100, 3);
        mockVillagePattern(center2, 15, 80, 2);
        mockSurfaceLevel(center1);
        mockSurfaceLevel(center2);

        List<StructureMatch> matches = detector.detectInChunk(chunk);

        assertEquals(2, matches.size());
        assertTrue(matches.stream().allMatch(match -> match.confidence() >= CONFIDENCE_THRESHOLD));
    }

    private void mockVillagePattern(BlockPos center, int pathBlocks, int buildingBlocks, int doors) {
        // Mock surface blocks in 16x16x16 area
        for (int x = -8; x <= 8; x++) {
            for (int y = -4; y <= 4; y++) {
                for (int z = -8; z <= 8; z++) {
                    BlockPos pos = center.add(x, y, z);
                    BlockState state;

                    // Distribute blocks based on desired counts
                    if (pathBlocks > 0 && Math.random() < 0.2) {
                        state = Blocks.DIRT_PATH.getDefaultState();
                        pathBlocks--;
                    } else if (buildingBlocks > 0 && Math.random() < 0.3) {
                        state = Blocks.COBBLESTONE.getDefaultState();
                        buildingBlocks--;
                    } else if (doors > 0 && Math.random() < 0.1) {
                        state = mock(BlockState.class);
                        when(state.getBlock()).thenReturn(doorBlock);
                        doors--;
                    } else {
                        state = Blocks.AIR.getDefaultState();
                    }

                    when(world.getBlockState(pos)).thenReturn(state);
                }
            }
        }
    }

    private void mockSurfaceLevel(BlockPos pos) {
        // Mock air blocks above and solid blocks below to simulate surface
        for (int y = pos.getY() + 1; y <= world.getTopY(); y++) {
            when(world.getBlockState(new BlockPos(pos.getX(), y, pos.getZ())))
                .thenReturn(Blocks.AIR.getDefaultState());
        }
        for (int y = pos.getY(); y >= world.getBottomY(); y--) {
            when(world.getBlockState(new BlockPos(pos.getX(), y, pos.getZ())))
                .thenReturn(Blocks.DIRT.getDefaultState());
        }
    }
}