package dev.journey.Skylandia.automation.actions;

import dev.journey.Skylandia.automation.AutomationFlow;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.Hand;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.player.ChatUtils;

public class BasicActions {
    private static final String BARITONE_PREFIX = "b";
    private static final MinecraftClient mc = MinecraftClient.getInstance();
    
    private static void logInfo(String message) {
        ChatUtils.info("Automation", message);
    }
    
    private static void logError(String message) {
        ChatUtils.error("Automation", message);
    }

    public static Action moveTo(BlockPos pos) {
        return flow -> {
            // Use Baritone to path to coordinates
            sendBaritoneCommand("goto " + pos.getX() + " " + pos.getY() + " " + pos.getZ());
        };
    }

    public static Action mineShulker() {
        return flow -> {
            if (mc.player == null) return;
            // Tell Baritone to mine nearest shulker box
            sendBaritoneCommand("mine shulker_box");
        };
    }

    public static Action moveToLastShulker() {
        return flow -> {
            Object lastPosObj = flow.getVariable("lastShulkerPos");
            if (!(lastPosObj instanceof BlockPos)) {
                throw new RuntimeException("No shulker position found");
            }
            BlockPos pos = (BlockPos) lastPosObj;
            if (mc.player == null) return;
            sendBaritoneCommand(String.format("goto %d %d %d", pos.getX(), pos.getY(), pos.getZ()));
        };
    }

    public static Action breakBlock() {
        return flow -> {
            if (mc.interactionManager == null || mc.player == null) return;
            
            Object targetObj = flow.getVariable("lastShulkerPos");
            if (!(targetObj instanceof BlockPos)) {
                throw new RuntimeException("No target block position found");
            }
            BlockPos pos = (BlockPos) targetObj;
            
            // Face the block
            Vec3d blockCenter = Vec3d.ofCenter(pos);
            Rotations.rotate(Rotations.getYaw(blockCenter), Rotations.getPitch(blockCenter));
            
            // Break the block
            mc.interactionManager.updateBlockBreakingProgress(pos, mc.player.getHorizontalFacing());
        };
    }

    private static void sendBaritoneCommand(String cmd) {
        if (mc.player != null && mc.getNetworkHandler() != null) {
            String fullCommand = BARITONE_PREFIX + " " + cmd;
            logInfo("Executing Baritone command: " + fullCommand);
            try {
                mc.getNetworkHandler().sendChatCommand(fullCommand);
            } catch (Exception e) {
                logError("Failed to execute Baritone command: " + e.getMessage());
            }
        } else {
            logError("Cannot execute command - player or network handler is null");
        }
    }
    
    public static Action stopPathing() {
        return flow -> {
            if (mc.player == null) return;
            sendBaritoneCommand("stop");
        };
    }
    
    public static Action findShulker() {
        return flow -> {
            if (mc.player == null) return;
            sendBaritoneCommand("find shulker_box");
        };
    }
    
    public static Action followPath() {
        return flow -> {
            if (mc.player == null) return;
            sendBaritoneCommand("path");
        };
    }

    public static Action interact() {
        return flow -> {
            if (mc.interactionManager == null || mc.player == null) return;
            
            Object targetObj = flow.getVariable("lastShulkerPos");
            if (!(targetObj instanceof BlockPos)) {
                throw new RuntimeException("No target block position found");
            }
            BlockPos pos = (BlockPos) targetObj;
            
            // Face the block
            Vec3d blockCenter = Vec3d.ofCenter(pos);
            Rotations.rotate(Rotations.getYaw(blockCenter), Rotations.getPitch(blockCenter));
            
            // Interact with the block
            BlockHitResult hitResult = new BlockHitResult(Vec3d.ofCenter(pos), mc.player.getHorizontalFacing(), pos, false);
            BlockUtils.interact(hitResult, Hand.MAIN_HAND, true);
        };
    }

    public static Action openInventory() {
        return flow -> {
            if (mc.player == null) return;
            mc.player.getInventory().selectedSlot = 0; // Reset selected slot
            mc.setScreen(new net.minecraft.client.gui.screen.ingame.InventoryScreen(mc.player));
        };
    }

    public static Action wait(int ticks) {
        return flow -> {
            try {
                Thread.sleep(ticks * 50L);
            } catch (InterruptedException ignored) {}
        };
    }

    public static Action storePosition(String name) {
        return flow -> {
            if (mc.player == null) return;
            flow.setVariable(name, mc.player.getBlockPos());
        };
    }

    public static Action returnToStored(String name) {
        return flow -> {
            Object posObj = flow.getVariable(name);
            if (!(posObj instanceof BlockPos)) {
                throw new RuntimeException("No stored position found: " + name);
            }
            BlockPos pos = (BlockPos) posObj;
            PathManagers.get().moveTo(pos, true);
        };
    }
}