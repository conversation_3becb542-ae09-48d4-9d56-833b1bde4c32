package dev.journey.Skylandia.modules.exploration.detection.structures;

import net.minecraft.block.BlockState;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import java.util.ArrayList;
import java.util.List;

public abstract class StructureDetector {
    protected final World world;
    protected final double confidenceThreshold;
    protected final int maxSearchRadius;

    protected StructureDetector(World world, double confidenceThreshold, int maxSearchRadius) {
        this.world = world;
        this.confidenceThreshold = confidenceThreshold;
        this.maxSearchRadius = maxSearchRadius;
    }

    public abstract StructureType getStructureType();
    
    public abstract List<StructureMatch> detectInChunk(Chunk chunk);

    protected boolean matchesPattern(BlockPos center, BlockState[][][] pattern) {
        int halfHeight = pattern.length / 2;
        int halfWidth = pattern[0].length / 2;
        int halfDepth = pattern[0][0].length / 2;

        for (int y = 0; y < pattern.length; y++) {
            for (int x = 0; x < pattern[y].length; x++) {
                for (int z = 0; z < pattern[y][x].length; z++) {
                    BlockPos pos = center.add(
                        x - halfWidth,
                        y - halfHeight,
                        z - halfDepth
                    );
                    
                    BlockState expected = pattern[y][x][z];
                    if (expected != null) {
                        BlockState actual = world.getBlockState(pos);
                        if (!expected.equals(actual)) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    protected double calculatePatternConfidence(BlockPos center, BlockState[][][] pattern) {
        int matches = 0;
        int total = 0;
        int halfHeight = pattern.length / 2;
        int halfWidth = pattern[0].length / 2;
        int halfDepth = pattern[0][0].length / 2;

        for (int y = 0; y < pattern.length; y++) {
            for (int x = 0; x < pattern[y].length; x++) {
                for (int z = 0; z < pattern[y][x].length; z++) {
                    BlockState expected = pattern[y][x][z];
                    if (expected != null) {
                        total++;
                        BlockPos pos = center.add(
                            x - halfWidth,
                            y - halfHeight,
                            z - halfDepth
                        );
                        BlockState actual = world.getBlockState(pos);
                        if (expected.equals(actual)) {
                            matches++;
                        }
                    }
                }
            }
        }
        return total > 0 ? (double) matches / total : 0.0;
    }

    protected Box calculateBounds(BlockPos center, int width, int height, int depth) {
        return new Box(
            center.getX() - width/2.0, center.getY() - height/2.0, center.getZ() - depth/2.0,
            center.getX() + width/2.0, center.getY() + height/2.0, center.getZ() + depth/2.0
        );
    }

    protected boolean isWithinChunk(BlockPos pos, Chunk chunk) {
        int chunkX = chunk.getPos().x * 16;
        int chunkZ = chunk.getPos().z * 16;
        return pos.getX() >= chunkX && pos.getX() < chunkX + 16 
            && pos.getZ() >= chunkZ && pos.getZ() < chunkZ + 16;
    }
}