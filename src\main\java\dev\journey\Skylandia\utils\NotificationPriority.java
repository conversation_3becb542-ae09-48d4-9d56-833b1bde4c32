package dev.journey.Skylandia.utils;

public enum NotificationPriority {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL;

    public int getValue() {
        return ordinal();
    }

    public static NotificationPriority fromValue(int value) {
        for (NotificationPriority priority : values()) {
            if (priority.getValue() == value) {
                return priority;
            }
        }
        return LOW;
    }
}