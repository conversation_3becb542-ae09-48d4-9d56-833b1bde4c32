package dev.journey.Skylandia.modules.exploration.detection.patterns;

import dev.journey.Skylandia.modules.exploration.detection.DetectionResult;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class VillageAnomalyDetector extends PatternDetector {
    private static final Block[] VILLAGE_BLOCKS = {
        Blocks.FLETCHING_TABLE,
        Blocks.SMITHING_TABLE,
        Blocks.BLAST_FURNACE,
        Blocks.SMOKER,
        Blocks.CARTOGRAPHY_TABLE,
        Blocks.BREWING_STAND,
        Blocks.CAULDRON,
        Blocks.COMPOSTER,
        Blocks.BARREL,
        Blocks.LECTERN
    };

    private static final Block[] PLAYER_BLOCKS = {
        Blocks.CHEST,
        Blocks.FURNACE,
        Blocks.CRAFTING_TABLE,
        Blocks.ENCHANTING_TABLE,
        Blocks.ANVIL,
        Blocks.TORCH,
        Blocks.LADDER,
        Blocks.BOOKSHELF
    };

    private static final int ANALYSIS_RADIUS = 8;

    public VillageAnomalyDetector(World world) {
        super(world);
    }

    @Override
    public List<DetectionResult> detect(BlockPos center, int radius) {
        List<DetectionResult> results = new ArrayList<>();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -radius; y <= radius; y++) {
                for (int z = -radius; z <= radius; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (isVillageBlock(pos)) {
                        AnomalyInfo info = analyzeArea(pos);
                        if (info != null && !isAlreadyDetected(results, info.center)) {
                            results.add(new DetectionResult(
                                info.description,
                                info.center,
                                new Vec3d(info.center.getX() + 0.5, info.center.getY() + 0.5, info.center.getZ() + 0.5),
                                DetectionResult.DetectionType.ANOMALY,
                                info.confidence
                            ));
                        }
                    }
                }
            }
        }

        return results;
    }

    private AnomalyInfo analyzeArea(BlockPos center) {
        Set<BlockPos> villageBlocks = new HashSet<>();
        Set<BlockPos> playerBlocks = new HashSet<>();
        Set<BlockPos> modifiedBlocks = new HashSet<>();

        // Analyze surrounding area
        for (int x = -ANALYSIS_RADIUS; x <= ANALYSIS_RADIUS; x++) {
            for (int y = -ANALYSIS_RADIUS; y <= ANALYSIS_RADIUS; y++) {
                for (int z = -ANALYSIS_RADIUS; z <= ANALYSIS_RADIUS; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Block block = world.getBlockState(pos).getBlock();

                    if (isVillageBlock(pos)) {
                        villageBlocks.add(pos);
                    } else if (isPlayerBlock(pos)) {
                        playerBlocks.add(pos);
                    } else if (isModifiedBlock(pos)) {
                        modifiedBlocks.add(pos);
                    }
                }
            }
        }

        if (villageBlocks.isEmpty()) return null;

        float confidence = calculateConfidence(villageBlocks, playerBlocks, modifiedBlocks);
        if (confidence < 0.4f) return null;

        String description = generateDescription(villageBlocks, playerBlocks, modifiedBlocks);
        return new AnomalyInfo(center, description, confidence);
    }

    private float calculateConfidence(Set<BlockPos> villageBlocks, Set<BlockPos> playerBlocks, Set<BlockPos> modifiedBlocks) {
        float confidence = 0.4f; // Base confidence for having village blocks

        // Player blocks near village structures suggest modification
        if (!playerBlocks.isEmpty()) {
            confidence += Math.min(0.1f * playerBlocks.size(), 0.3f);
        }

        // Modified blocks suggest player activity
        if (!modifiedBlocks.isEmpty()) {
            confidence += Math.min(0.1f * modifiedBlocks.size(), 0.3f);
        }

        // If there's a high ratio of player blocks to village blocks, increase confidence
        float ratio = (float) playerBlocks.size() / villageBlocks.size();
        if (ratio > 0.5f) {
            confidence += 0.2f;
        }

        return Math.min(confidence, 1.0f);
    }

    private String generateDescription(Set<BlockPos> villageBlocks, Set<BlockPos> playerBlocks, Set<BlockPos> modifiedBlocks) {
        StringBuilder description = new StringBuilder("Modified village structure: ");

        if (!playerBlocks.isEmpty()) {
            description.append(playerBlocks.size()).append(" player blocks");
        }

        if (!modifiedBlocks.isEmpty()) {
            if (!playerBlocks.isEmpty()) description.append(", ");
            description.append(modifiedBlocks.size()).append(" modified blocks");
        }

        return description.toString();
    }

    private boolean isVillageBlock(BlockPos pos) {
        Block block = world.getBlockState(pos).getBlock();
        for (Block villageBlock : VILLAGE_BLOCKS) {
            if (block == villageBlock) return true;
        }
        return false;
    }

    private boolean isPlayerBlock(BlockPos pos) {
        Block block = world.getBlockState(pos).getBlock();
        for (Block playerBlock : PLAYER_BLOCKS) {
            if (block == playerBlock) return true;
        }
        return false;
    }

    private boolean isModifiedBlock(BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        Block block = state.getBlock();

        // Check for blocks that are likely player modifications
        return block == Blocks.GLASS_PANE || 
               block == Blocks.GLASS ||
               block == Blocks.SMOOTH_STONE ||
               block == Blocks.POLISHED_ANDESITE ||
               block == Blocks.STONE_BRICKS ||
               (block == Blocks.CHEST && !isVillageBlock(pos));
    }

    private boolean isAlreadyDetected(List<DetectionResult> results, BlockPos pos) {
        for (DetectionResult result : results) {
            if (result.getPosition().getSquaredDistance(pos) < 64) { // Within 8 blocks
                return true;
            }
        }
        return false;
    }

    private static class AnomalyInfo {
        final BlockPos center;
        final String description;
        final float confidence;

        AnomalyInfo(BlockPos center, String description, float confidence) {
            this.center = center;
            this.description = description;
            this.confidence = confidence;
        }
    }
}
